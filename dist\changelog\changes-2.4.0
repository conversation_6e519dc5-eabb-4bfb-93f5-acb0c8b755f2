Qt Creator version 2.4 contains bug fixes and new features.

The most important changes are listed in this document. For a complete
list of changes, see the Git log for the Qt Creator sources that
you can check out from the public Git repository. For example:

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --cherry-pick --pretty=oneline v2.3.1...origin/2.4

General
   * Improve the error message that appears when plugin errors occur
   * Decrease the minimum size of the Options dialog

Editing
   * Advanced search: Show more information about the search parameters
   * Advanced search: Move the previously modal dialog into the search results pane
   * Advanced search: Keep a history of the most recent searches and their results
   * Implement code style schemas for reuse in different projects

Managing Projects
   * Support the sharing of project settings
   * Add new project wizards for non-Qt C and C++ applications

Debugging
   * Add initial Qt 5 support in dumpers
   * Make vtables visible
   * Improve remote debugging
   * Improve register handling (partial updates, input in hex, ARM Neon support)
   * Remove inferior calls in QDate, QTime, QDateTime dumpers
   * Fix QTextCodec dumpers
   * Globally enable pretty printing for references
   * Improve handling of unprintable data in Locals and Expressions
   * Add 'Local8Bit' as a display option for char arrays and pointers

Debugging QML/JS
   * Add the option to attach to a running application
   * Lots of bug fixes especially for debugging in combination with C++
   * Add initial support for debugging JS in Qt 5 (v8 engine)

Analyzing Code
   * Retrieve and store QML tracing data by using the standalone qmlprofiler
     command line tool
   * Store and load traces in a file
   * Add the option to attach to a running application

C++ Support
   * Add the refactoring action to synchronize function declarations and definitions
   * Make 'insert definition from declaration' use minimally qualified names
     and find a good insertion location next to surrounding declarations
   * Fix completion for typedefs for templates in namespaces
   * Use minimally qualified names in function signature completion
   * Use minimally qualified names in 'insert local declaration' refactoring action
   * Prefer files in the same directory when switching between the header and source
   * Fix the problem with encoding and quick fixes (QTCREATORBUG-6140)
   * Fix the preservation of indentation level in comments with tabs (QTCREATORBUG-6151)
   * Improve performance for files with a huge number of literals

QML/JS Support
   * Add 'Rename usages' functionality (QTCREATORBUG-3669)
   * Add collection of static analysis messages with Ctrl-Shift-C
   * Add semantic highlighting
   * Significantly improve scanning of C++ documents for qmlRegisterType and
     setContextProperty calls (QTCREATORBUG-3199)
   * Add a warning about inappropriate use of constructor functions
   * Add a warning about unreachable code
   * Add support for .import directive in js files
   * Add completion for XMLHttpRequest, DB API and JSON.
   * Add the 'length' property to functions
   * Use mime types to distinguish qml and js files
   * Show the function argument hint for signals
   * When completing enums, add qualified names instead of strings
   * Honor typeinfo lines in qmldir files
   * Make string literals that contain file names into links (QTCREATORBUG-5701)
   * Add a warning about invalid types in 'property' declarations (QTCREATORBUG-3666)
   * Fix the highlighting of property types (QTCREATORBUG-6127)
   * Fix the 'follow symbol' functionality for local variables (QTCREATORBUG-6094)
   * Fix function argument hints on variables (QTCREATORBUG-5752)
   * Fix the completion for enums in a different scope
   * Fix typing a slash (/) triggering a global completion
   * Fix handling of meta object revision in C++ QML plugins
   * Fix indentation of block property initializers
   * Fix indentation of labelled statements
   * Fix scope for completion in code bindings
   * Allow for different builtin type information per Qt version
   * Update builtin type information and parser for Qt 5
   * Use qmlscene for previewing files in Qt 5

Qt Quick Designer
   * Add breadcrumb navigation for components
   * Add layout functionality to context menu

Help

Platform Specific

Mac
* Fix 'Run in Terminal' so that it finds xterm by default

Linux (GNOME and KDE)

Windows
   * Fix aborting the build, so that Qt Creator sends Ctrl-C to the
     build process via the process_ctrlc_stub helper program

Symbian Target

Remote Linux Support
   * Move Maemo and MeeGo support into its own plugin
   * Assume less about the target systems, for example, in respect to the
     presence of POSIX-conforming tools.

Qt Designer

FakeVim
   * Add basic command editing in Ex mode
   * Mention 'comma escape' in the documentation
   * Fix foreground color for search hits in the Dark scheme
   * Fix toggling visual mode with 'v'

Version control plugins
   * SVN: Support for SVN version 1.7
   * Git: Add an option to toggle decoration settings for log and show commands


Additional credits go to:
   * Benito van der Zander (code model)
   * Christoph Mathys (debugger)
   * Hugues Delorme (version control refactoring, bazaar)
   * Jonathan Liu (SVN 1.7 support)
   * Nicolas Arnaud-Cormos (quick fixes)
   * Orgad Shaneh (git, debugger, other places)
   * Peter Kuemmel (bug fixes)
   * Thorbjørn Lindeijer (code model)
   * Vlad Brovko (improve symlink support)
   * Yuchen Deng (bug fixes, project wizards)

