// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-group.html
    \previouspage studio-3d-node.html
    \nextpage studio-3d-instancing.html

    \title Group

    The \uicontrol Group component is a \l Node component that can be
    used to wrap other objects for the purpose of grouping them. This allows you
    to transform and set the opacity and visibility of multiple 3D components in
    the \l Properties view simultaneously.

    To add a \uicontrol Group component
    to your scene, drag-and-drop it from \uicontrol Components >
    \uicontrol {Qt Quick 3D} > \uicontrol {Qt Quick 3D} to the \l {3D}
    view or to \l Navigator > \uicontrol View3D > \uicontrol {Scene Environment}
    > \uicontrol Scene.

    If the \uicontrol Group component is not displayed in
    \uicontrol {Components}, you should add the \uicontrol {Qt Quick 3D} module to
    your project, as described in \l {Adding and Removing Modules}.

    Select the \uicontrol Group component in \uicontrol Navigator to modify
    \uicontrol Node properties for its child components in the
    \uicontrol Properties view.
*/
