// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-troubleshooting-debugging.html
    \page creator-analyze-mode.html
    \nextpage creator-qml-performance-monitor.html

    \title Analyzing Code

    You can use the code analysis tools in the \uicontrol Debug mode. To
    switch to \uicontrol Debug mode, select \uicontrol Debug in the mode
    selector, or select the \uicontrol {Analyze} menu and then select a tool.
    When you are in the \uicontrol Debug mode, you can switch between tools by
    selecting them in the menu on the toolbar.

    You can drag and drop the views in the \uicontrol Debug mode to new
    positions on the screen. The size and position of views are saved for future
    sessions. Select \uicontrol View > \uicontrol Views >
    \uicontrol {Reset to Default Layout} to reset the views to their original
    sizes and positions.

    You can use the following code analysis tools in the \uicontrol Debug
    mode:

    \list

        \li \l{Profiling QML Applications}{QML Profiler}

            Inspect binding evaluations, signal handling, and
            painting operations when running QML code. This is useful for
            identifying potential bottlenecks, especially in the evaluation
            of bindings.

        \li \l{Checking Code Coverage}{Coco}

            Analyze the way an application runs as part of a test suite, for
            example, and use the results to make the tests more efficient and
            complete.

        \li \l{Using Valgrind Code Analysis Tools}{Valgrind Code Analysis Tools}

            Detect problems in memory management by using the Memcheck
            tool and find cache misses in the code by using the Callgrind tool.

        \li \l{Using Clang Tools}{Clang Tools}

            Detect problems in C, C++, and Objective-C programs by
            using Clang-Tidy and Clazy.

        \li \l{Detecting Memory Leaks with Heob}{Heob}

            Use the Heob heap observer on Windows to detect buffer
            overruns and memory leaks.

        \li \l{Analyzing CPU Usage}{Performance Analyzer}

            Analyze the CPU usage of embedded applications and Linux
            desktop applications with the Performance Analyzer that integrates
            the Linux Perf tool.

        \li \l{Analyzing Code with Cppcheck}{Cppcheck}

            Use the experimental Cppcheck plugin to detect undefined
            behavior and dangerous coding constructs.

        \li \l{Visualizing Chrome Trace Events}{Chrome Trace Format Visualizer}

            Use the Chrome Trace Format (CTF) Visualizer to view
            Chrome trace events. This is especially useful when viewing
            large trace files that are difficult to visualize using the
            built-in trace-viewer (\c{chrome://tracing}).
    \endlist

*/
