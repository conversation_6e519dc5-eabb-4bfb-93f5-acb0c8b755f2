// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-advanced.html
    \page creator-os-supported-platforms.html
    \nextpage creator-desktop-platforms.html

    \title Supported Platforms

    You can install and run \QC on several operating systems to create
    applications for multiple desktop, embedded, and mobile device platforms,
    as well as web browsers (experimental).

    The following table summarizes operating system support for developing
    applications.

    \table
        \header
            \li {1,2} Target Platform
            \li {3,1} Development Platform
        \header
            \li \l Linux
            \li \l \macos
            \li \l Windows
        \row
            \li \l Android
            \li \image ok.png
            \li \image ok.png
            \li \image ok.png
        \row
            \li \l{Bare Metal}
            \li \image ok.png
            \li \image ok.png
            \li \image ok.png
        \row
            \li \l Boot2Qt
            \li \image ok.png
            \li \image ok.png
            \li \image ok.png
        \row
            \li \l{Remote Linux}
            \li \image ok.png
            \li \image ok.png
            \li \image ok.png
        \row
            \li \l iOS
            \li
            \li \image ok.png
            \li
        \row
            \li \l{Microcontroller Units (MCU)}{MCUs}
            \li \image ok.png
            \li
            \li \image ok.png
        \row
            \li \l QNX
            \li \image ok.png
            \li \image ok.png
            \li \inlineimage ok.png
        \row
            \li \l{Building Applications for the Web}{WebAssembly}
            \li \image ok.png
            \li \image ok.png
            \li \image ok.png
    \endtable

    \note UWP support was removed from \QC 8.0.
    To develop for UWP using Qt 5, use \QC 7.0, or earlier.

    \QC automatically runs scheduled checks for updates based on the settings
    specified in \uicontrol Edit > \uicontrol Preferences \uicontrol Environment >
    \uicontrol Update.

    For more information on the requirements for each platform, see:

    \list
        \li \l {Desktop Platforms}
        \li \l {Embedded Platforms}
        \li \l {Mobile Platforms}
    \endlist
*/
