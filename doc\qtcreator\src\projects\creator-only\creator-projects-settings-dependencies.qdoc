// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-build-dependencies.html
    \previouspage creator-code-style-settings.html
    \nextpage creator-project-settings-environment.html

    \title Specifying Dependencies

    If you have multiple projects loaded in a session, you can define the
    order in which they are built. For example, if project A depends on project
    B, project B must be built first.

    \note The build order is stored as a property of a session, not a project.
    You must open the session for these settings to take effect. For more
    information, see \l{Managing Sessions}.

    \image qtcreator-build-dependencies.png "Dependencies view"

    To define the build order of projects within a session:

    \list 1

        \li Select \uicontrol Projects > \uicontrol {Project Settings} >
            \uicontrol Dependencies.

        \li Select projects that must be built before the current project is
            built.

        \li Select the \uicontrol {Synchronize configuration} check box to
            use the same kit as well as the same build and deploy
            configuration to build and deploy all dependent projects loaded
            in a session.

    \endlist

    \QC calculates the build order based on the dependencies that you
    specify for the projects loaded in the session.

    \note You cannot use this view to specify subprojects for projects.
    For more information on how to add subprojects, see \l{Adding Subprojects
    to Projects}.

*/
