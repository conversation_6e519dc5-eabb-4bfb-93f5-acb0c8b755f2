// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [run settings debugger]

    \section1 Enabling Debugging

    \image qtquick-debugging-settings.png "Debugger Settings"

    To select the languages to debug, select the \uicontrol {Enable C++} and
    \uicontrol {Enable QML} check boxes.

    \note Opening a socket at a well-known port presents a security risk. Anyone
    on the Internet could connect to the application that you are debugging and
    execute any JavaScript functions. Therefore, you must make sure that the
    port is properly protected by a firewall.

    Optionally, in \uicontrol {Additional startup commands}, you can enter
    additional settings for debugging C++:

    \list
        \li \l{Adding Custom Debugging Helpers}{Custom debugging helpers}
        \li \l{Specifying GDB Settings}{GDB commands} to execute after GDB
            has started, but before the debugged program is started or
            attached, and before the debugging helpers are initialized
    \endlist

    However, you can usually leave this field empty.

    For more information about debugging, see \l{Debugging}.

//! [run settings debugger]
*/
