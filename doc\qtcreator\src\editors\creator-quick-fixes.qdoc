// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-editor-quick-fixes.html
    \previouspage creator-editor-refactoring.html
    \if defined(qtdesignstudio)
    \nextpage creator-editor-options.html
    \else
    \nextpage creator-beautifier.html
    \endif

    \title Applying Refactoring Actions

    \QC allows you to quickly and conveniently apply actions (quick fixes)
    to refactor your code by selecting them in a context menu. The actions
    available depend on the position of the cursor in the code editor.

    \if defined(qtcreator)
    To apply refactoring actions to C++ code, right-click an operand,
    conditional statement, string, or name to open a context menu.
    \endif
    To apply refactoring actions to QML code, right-click an item ID or name.

    In the context menu, select \uicontrol {Refactoring} and then select a
    refactoring action.

    You can also press \key {Alt+Enter} to open a context menu that contains
    refactoring actions available in the current cursor position.

    \if defined(qtcreator)
    \section1 Creating Functions

    You can apply refactoring actions to implement member functions, insert
    virtual functions of base classes, create getter and setter functions,
    and generate constructors. You can specify settings for generating the
    functions either globally for all projects or separately for each project
    in the \l{Specifying Settings}{build and run} settings of the project.

    \section2 Implementing Member Functions

    You can apply the \uicontrol {Create Implementations for Member Functions}
    refactoring action to create implementations for all member functions in
    one go. In the \uicontrol {Member Function Implementations} dialog, you can
    specify whether the member functions are generated inline or outside the
    class.

    \image qtcreator-refactoring-member-function-implementations.png "Implement Member Functions dialog"

    \section2 Inserting Virtual Functions

    You can apply the \uicontrol {Insert Virtual Functions of Base Classes}
    refactoring action to insert declarations and the corresponding definitions
    inside or outside the class or in an implementation file (if it exists).

    \image qtcreator-refactoring-virtual-function-dialog.png "Insert Virtual Functions dialog"

    Select the functions to insert in the list of available functions. You can
    filter the list and hide reimplemented functions from it.

    You can add \e virtual or the \e override equivalent to the function
    declaration.

    \section2 Creating Getters and Setters

    You can apply the \uicontrol {Create Getter and Setter Member Functions}
    refactoring action to create either both getter and setter member functions
    for member variables or only a getter or setter.

    \image qtcreator-refactoring-getters-and-setters.png "Getters and Setters dialog"

    \section2 Generating Constructors

    You can apply the \uicontrol {Generate Constructor} refactoring action to
    create a public, protected, or private constructor for a class. Select the
    class members to initialize in the constructor. Drag and drop the parameters
    to specify their order in the constructor.

    \image qtcreator-refactoring-constructor.png "Constructor dialog"

    \section1 Specifying Settings for Refactoring Actions

    You can specify settings for the refactoring actions either globally for
    all projects or separately for each project. To specify global options,
    select \uicontrol Edit > \uicontrol Preferences > \uicontrol C++ >
    \uicontrol {Quick Fixes}.

    To specify custom settings for a particular project, select
    \uicontrol Projects > \uicontrol {Project Settings} >
    \uicontrol {Quick Fixes}, and then deselect \uicontrol {Use global settings}.

    \image qtcreator-refactoring-options-locations.png "Quick Fixes settings"

    To revert to global settings, select \uicontrol {Reset to Global}. To
    delete the custom settings, select \uicontrol {Use global settings}, and
    then select \uicontrol {Delete Custom Settings File}.

    \section2 Function Locations

    In the \uicontrol {Generated Function Locations} group, you can determine
    whether refactoring actions should generate getter and setter functions
    in the header file (inside or outside the class) or in the implementation
    file.

    \section2 Function Names and Attributes

    In the \uicontrol {Getter Setter Generation Properties} group, you can
    specify additional settings for getter and setter names, attributes, and
    parameters. You can specify that setter functions should be created as
    \e slots and that signals should be generated with the new value as a
    parameter.

    \image qtcreator-refactoring-options-generation.png "Getter and Setter generation settings"

    \section2 Namespace Handling

    In the \uicontrol {Missing Namespace Handling} group, select whether to
    generate missing namespaces, add \c {using namespace} where necessary, or
    rewrite types to match the existing namespaces.

    \image qtcreator-refactoring-options-namespaces.png "Namespace handling settings"

    \section2 Custom Parameter Types

    In the \uicontrol {Custom Getter Setter Templates} group, specify how the
    code of a getter or setter function for a certain data type should look
    like. This is necessary for types where assignment cannot use \c operator=,
    as in the pre-defined settings for \c unique_ptr or where \c operator== is
    not suitable for comparison, as in the pre-defined settings for
    floating-point types. For example, if you have a special type \c MyClass,
    you can specify that a function, \c myCompare, should be used for comparison
    rather than the default of \c ==.

    To specify special handling for a custom parameter type, select
    \uicontrol Add and set the parameter type, comparison, return expression,
    and return type. In the \uicontrol {Return type} field, you can use \c <new>
    and \c <cur> to access the parameter and current value. Use \c <type> to
    access the type and \c <T> for the template parameter.

    \image qtcreator-refactoring-options-templates.png "Settings for handling custom parameter types"

    Usually, arguments are passed by using a \c const reference. To pass
    arguments of a particular type as values, list them in the
    \uicontrol {Value types} field. Namespaces and template arguments are
    removed. The real Type must contain the given Type. For example, \c int
    matches \c int32_t but not \c vector<int>, and \c vector matches
    \c {std::pmr::vector<int>} but not \c {std::optional<vector<int>>}.

    To return non-trivial objects by using a \c const reference, select the
    \uicontrol {Return non-value types by const reference} check box.
    \endif

    \section1 Summary of Refactoring Actions

    \if defined(qtcreator)
    If you use the \l{Parsing C++ Files with the Clang Code Model}
    {Clang code model} to parse the C++ files, the
    \l{http://clang.llvm.org/diagnostics.html}{Clang fix-it hints}
    that have been integrated into \QC are also available to you. In addition to
    the standard ways of activating refactoring actions, you can select the
    actions that are applicable on a line in the context menu in the left margin
    of the code editor.

   \section2 Refactoring C++ Code

   You can apply the following types of refactoring actions to C++ code:

   \list
        \li Change binary operands
        \li Simplify if and while conditions (for example, move declarations out
            of if conditions)
        \li Modify strings (for example, set the encoding for a string to
            Latin-1, mark strings translatable, and convert symbol names to
            camel case)
        \li Create variable declarations
        \li Create function declarations and definitions
   \endlist

    The following table summarizes the refactoring actions for C++ code. The
    action is available when the cursor is in the position described in the
    Activation column.

    \table
        \header
            \li Refactoring Action
            \li Description
            \li Activation
        \row
            \li Add Curly Braces
            \li Adds curly braces to an if statement that does not contain a
                compound statement. For example, rewrites

    \code
    if (a)
        b;
    \endcode

                as

    \code
    if (a) {
        b;
    }
    \endcode
            \li \c if
        \row
            \li Move Declaration out of Condition
            \li Moves a declaration out of an if or while condition to simplify
                the condition. For example, rewrites

    \code
    if (Type name = foo()) {}
    \endcode

                as

    \code
    Type name = foo;
    if (name) {}
    \endcode
            \li Name of the introduced variable
        \row
            \li Rewrite Condition Using ||
            \li Rewrites the expression according to De Morgan's laws. For
                example, rewrites:
    \code
    !a && !b
    \endcode

                as

    \code
    !(a || b)
    \endcode
            \li \c &&
        \row
            \li Rewrite Using \e operator
            \li Rewrites an expression negating it and using the inverse
                operator. For example, rewrites:

            \list

    \li    \code
    a op b
    \endcode

                as

    \code
    !(a invop b)
    \endcode

    \li    \code
    (a op b)
    \endcode

                as

    \code
    !(a invop b)
    \endcode

    \li   \code
    !(a op b)
    \endcode

                as

    \code
    (a invob b)
    \endcode

    \endlist

            \li \c {<=}, \c {<}, \c {>}, \c {>=}, \c {==} or \c {!=}
        \row
            \li Split Declaration
            \li Splits a simple declaration into several declarations. For
                example, rewrites:
    \code
    int *a, b;
    \endcode

                as

    \code
    int *a;
    int b;
    \endcode
            \li Type name or variable name
        \row
            \li Split if Statement
            \li Splits an if statement into several statements. For example,
                rewrites:

    \code
    if (something && something_else) {
    }
    \endcode

                as

    \code
    if (something) {
       if (something_else) {
       }
    }
    \endcode

                and

    \code
    if (something || something_else)
        x;
    \endcode

                with

    \code
    if (something)
        x;
    else if (something_else)
        x;
    \endcode

            \li \c && or \c ||
        \row
            \li Swap Operands
            \li Rewrites an expression in the inverse order using the inverse
                operator. For example, rewrites:
    \code
    a op b
    \endcode

                as
    \code
    b flipop a
    \endcode
            \li \c {<=}, \c {<}, \c {>},  \c {>=}, \c {==}, \c {!=}, \c {&&}
                or \c {||}
        \row
            \li Convert to Decimal
            \li Converts an integer literal to decimal representation
            \li Numeric literal
        \row
            \li Convert to Hexadecimal
            \li Converts an integer literal to hexadecimal representation
            \li Numeric literal
        \row
            \li Convert to Octal
            \li Converts an integer literal to octal representation
            \li Numeric literal
        \row
            \li Convert to Objective-C String Literal
            \li Converts a string literal to an Objective-C string literal if
                the file type is Objective-C(++). For example, rewrites the
                following strings

    \code
    "abcd"
    QLatin1String("abcd")
    QLatin1Literal("abcd")
    \endcode

                as

    \code
    @"abcd"
    \endcode
            \li String literal
        \row
            \li Enclose in QLatin1Char()
            \li Sets the encoding for a character to Latin-1, unless the
                character is already enclosed in QLatin1Char, QT_TRANSLATE_NOOP,
                tr, trUtf8, QLatin1Literal, or QLatin1String. For example,
                rewrites

    \code
    'a'
    \endcode

                as

    \code
    QLatin1Char('a')
    \endcode
            \li String literal
        \row
            \li Enclose in QLatin1String()
            \li Sets the encoding for a string to Latin-1, unless the string is
                already enclosed in QLatin1Char, QT_TRANSLATE_NOOP, tr, trUtf8,
                QLatin1Literal, or QLatin1String. For example, rewrites
    \code
    "abcd"
    \endcode

                as

    \code
    QLatin1String("abcd")
    \endcode

            \li String literal

        \row
            \li Mark as Translatable
            \li Marks a string translatable. For example, rewrites \c "abcd"
                with one of the following options, depending on which of them is
                available:

    \code
    tr("abcd")
    QCoreApplication::translate("CONTEXT", "abcd")
    QT_TRANSLATE_NOOP("GLOBAL", "abcd")
    \endcode

            \li String literal

        \row
            \li Add Definition in ...
            \li Inserts a definition stub for a function declaration either in
                the header file (inside or outside the class) or in the
                implementation file. For free functions, inserts the definition
                after the declaration of the function or in the implementation
                file. Qualified names are minimized when possible, instead of
                always being fully expanded.

                For example, rewrites

    \code
    Class Foo {
        void bar();
    };
    \endcode

                as (inside class)

    \code
    Class Foo {
        void bar() {

        }
    };
    \endcode

                as (outside class)

    \code
    Class Foo {
        void bar();
    };

    void Foo::bar()
    {

    }
    \endcode

                as (in implementation file)

    \code
    // Header file
    Class Foo {
        void bar();
    };

    // Implementation file
    void Foo::bar()
    {

    }
    \endcode

            \li Function name
        \row
            \li Add \c Function Declaration
            \li Inserts the member function declaration that matches the member
                function definition into the class declaration. The function can
                be \c {public}, \c {protected}, \c {private}, \c {public slot},
                \c {protected slot}, or \c {private slot}.
            \li Function name
        \row
            \li Add Class Member
            \li Adds a member declaration for the class member being
                initialized if it is not yet declared. You must enter
                the data type of the member.
            \li Identifier
        \row
            \li Create Implementations for Member Functions
            \li Creates implementations for all member functions in one go.
                In the \uicontrol {Member Function Implementations} dialog,
                you can specify whether the member functions are generated
                inline or outside the class.
            \li Function name
        \row
            \li Switch with Next/Previous Parameter
            \li Moves a parameter down or up one position in a parameter list.
            \li Parameter in the declaration or definition of a function
        \row
            \li Extract Function
            \li Moves the selected code to a new function and replaces the block
                of code with a call to the new function. Enter a name for the
                function in the \uicontrol {Extract Function Refactoring}
                dialog.
            \li Block of code selected
        \row
            \li Extract Constant as Function Parameter
            \li Replaces the selected literal and all its occurrences with the
                function parameter \c{newParameter}. The parameter
                \c{newParameter} will have the original literal as the default
                value.
            \li Block of code selected
        \row
            \li Add Local Declaration
            \li Adds the type of an assignee, if the type of the right-hand
                side of the assignment is known. For example, rewrites

    \code
    a = foo();
    \endcode

                as

    \code
    Type a = foo();
    \endcode

                where Type is the return type of \c {foo()}

            \li Assignee

        \row
            \li Convert to Camel Case
            \li Converts a symbol name to camel case, where elements of the name
                are joined without delimiter characters and the initial
                character of each element is capitalized. For example, rewrites
                \c an_example_symbol as \c anExampleSymbol and
                \c AN_EXAMPLE_SYMBOL as \c AnExampleSymbol
            \li Identifier
        \row
            \li Complete Switch Statement
            \li Adds all possible cases to a switch statement of the type
                \c enum
            \li \c switch
        \row
            \li Generate Missing Q_PROPERTY Members
            \li Adds missing members to a \c Q_PROPERTY:
            \list
                \li \c read function
                \li \c write function, if there is a WRITE
                \li \c {onChanged} signal, if there is a NOTIFY
                \li data member with the name \c {m_<propertyName>}
            \endlist
            \li \c Q_PROPERTY
        \row
            \li Generate Q_PROPERTY and Missing Members
            \li Generates a Q_PROPERTY and adds missing members to it, as
                described above.
            \li Class member
        \row
            \li Generate Constant Q_PROPERTY and Missing Members
            \li Generates a constant Q_PROPERTY and adds missing members
                to it, as described above.
            \li Class member
        \row
            \li Generate Q_PROPERTY and Missing Members with Reset Function
            \li Generates a Q_PROPERTY and adds missing members to it, as
                described above, but with an additional \c reset function.
            \li Class member
       \row
            \li Apply Changes
            \li Keeps function declarations and definitions synchronized by
                checking for the matching declaration or definition when you
                edit a function signature and by applying the changes to the
                matching code.
            \li Function signature. When this action is available, a light bulb
                icon appears: \inlineimage icons/refactormarker.png
        \row
            \li Add #include for undeclared or forward declared identifier
            \li Adds an \c {#include} directive to the current file to make the
                definition of a symbol available.
            \li Undeclared identifier
        \row
            \li Add Forward Declaration
            \li Adds a forward declaration for an undeclared identifier
                operation.
            \li Undeclared identifier
        \row
            \li Reformat Pointers or References
            \li Reformats declarations with pointers or references according
                to the code style settings for the current project. In case no
                project is open, the current global code style settings are
                used.

                For example, rewrites:

    \code
    char*s;
    \endcode

                as

    \code
    char *s;
    \endcode

                When applied to selections, all suitable declarations in the
                selection are rewritten.

            \li Declarations with pointers or references and selections
                containing such declarations
        \row
            \li Create Getter and Setter Member Functions
            \li Creates either both getter and setter member functions for
                member variables or only a getter or setter.
            \li Member variable in class definition
        \row
            \li Generate Getter and Setter
            \li Creates getter and setter member functions for a member
                variable.
            \li Member variable in class definition
        \row
            \li Generate Getter
            \li Creates a getter member function for a member variable.
            \li Member variable in class definition
        \row
            \li Generate Setter
            \li Creates a setter member function for a member variable.
            \li Member variable in class definition
        \row
            \li Generate Constructor
            \li Creates a constructor for a class.
            \li Class definition
        \row
            \li Move Function Definition
            \li Moves a function definition to the implementation file, outside
                the class or back to its declaration. For example, rewrites:
    \code
    class Foo
    {
      void bar()
      {
          // do stuff here
      }
    };
    \endcode

                as
    \code
    class Foo
    {
      void bar();
    };

    void Foo::bar() {
        // do stuff here
    }
    \endcode

            \li Function signature
        \row
            \li Move All Function Definitions
            \li Moves all function definitions to the implementation file or
                outside the class. For example, rewrites:
    \code
    class Foo
    {
      void bar()
      {
          // do stuff here
      }
      void baz()
      {
          // do stuff here
      }
    };
    \endcode

                as

    \code
    class Foo
    {
      void bar();
      void baz();
    };

    void Foo::bar() {
        // do stuff here
    }

    void Foo::baz() {
        // do stuff here
    }
    \endcode

            \li Class name
        \row
            \li Assign to Local Variable
            \li Adds a local variable which stores the return value of a
                function call or a new expression. For example, rewrites:

    \code
    QString s;
    s.toLatin1();
    \endcode

                as

    \code
    QString s;
    QByteArray latin1 = s.toLatin1();
    \endcode

                and

    \code
    new Foo;
    \endcode

                as

    \code
    Foo * localFoo = new Foo;
    \endcode

                By default, \QC uses the \c auto variable type when creating the
                variable. To label the variable with its actual type, select
                \uicontrol Edit > \uicontrol Preferences > \uicontrol C++ >
                \uicontrol {Quick Fixes}, and then deselect the
                \uicontrol {Use type "auto" when creating new variables} check
                box.

            \li Function call or class name
        \row
            \li Insert Virtual Functions of Base Classes
            \li Inserts declarations and the corresponding definitions inside or
                outside the class or in an implementation file (if it exists).
                For more information, see \l{Inserting Virtual Functions}.
            \li Class or base class name
        \row
            \li Optimize for-Loop
            \li Rewrites post increment operators as pre increment operators and
                post decrement operators as pre decrement operators. It also
                moves other than string or numeric literals and id expressions
                from the condition of a for loop to its initializer. For
                example, rewrites:

    \code
    for (int i = 0; i < 3 * 2; i++)
    \endcode

                as

    \code
    for (int i = 0, total = 3 * 2; i < total; ++i)
    \endcode
            \li \c for

        \row
            \li Escape String Literal as UTF-8
            \li Escapes non-ASCII characters in a string literal to hexadecimal
                escape sequences. String Literals are handled as UTF-8.
            \li String literal

        \row
            \li Unescape String Literal as UTF-8
            \li Unescapes octal or hexadecimal escape sequences in a string
                literal. String Literals are handled as UTF-8.
            \li String literal

        \row
            \li Convert to Stack Variable
            \li Converts the selected pointer to a stack variable. For example,
                rewrites:

    \code
    QByteArray *foo = new QByteArray("foo");
    foo->append("bar");
    \endcode

                as

    \code
    QByteArray foo("foo");
    foo.append("bar");
    \endcode

                This operation is limited to work only within function scope.
                Also, the coding style for pointers and references is not
                respected yet.
            \li Pointer Variable

        \row
            \li Convert to Pointer
            \li Converts the selected stack variable to a pointer. For example,
                rewrites:

    \code
    QByteArray foo = "foo";
    foo.append("bar");
    \endcode

                as

    \code
    QByteArray *foo = new QByteArray("foo");
    foo->append("bar");
    \endcode

                This operation is limited to work only within function scope.
                Also, the coding style for pointers and references is not
                respected yet.
            \li Stack Variable
        \row
            \li Remove \c {using namespace} and Adjust Type Names Accordingly
            \li Remove occurrences of \c {using namespace} in the local scope
                and adjust type names accordingly.
            \li \c using directive
       \row
            \li Remove All Occurrences of \c {using namespace} in Global Scope
                and Adjust Type Names Accordingly
            \li Remove all occurrences of \c {using namespace} in the global
                scope and adjust type names accordingly.
            \li \c using directive
        \row
            \li Convert connect() to Qt 5 Style
            \li Converts a Qt 4 QObject::connect() to Qt 5 style.
            \li QObject::connect() (Qt 4 style)
    \endtable

    \section2 Refactoring QML Code
    \endif

    You can apply the following types of refactoring actions to QML code:

    \list
        \li Rename IDs
        \li Split initializers
        \li Move a QML type into a separate file to reuse it in other .qml files
    \endlist

    The following table summarizes the refactoring actions for QML code. The
    action is available when the cursor is in the position described in the
    Activation column.

    \table
        \header
            \li Refactoring Action
            \li Description
            \li Activation

        \row
            \li Move Component into Separate File
            \li Moves a QML type into a separate file. Give the new component a
                name and select whether properties are set for the new component
                or for the original one.

                \image qtcreator-move-component-into-separate-file.png

            \li QML type name.
                \if defined(qtdesignstudio)
                This action is also available in the \l {2D} view.
                \endif
        \row
            \li Split Initializer
            \li Reformats a one-line type into a multi-line type. For example,
                rewrites

    \code
    Item { x: 10; y: 20; width: 10 }
    \endcode

                as

    \code
    Item {
        x: 10;
        y: 20;
        width: 10
    }
    \endcode

            \li QML type property
        \row
            \li Wrap Component in Loader
            \li Wraps the type in a Component type and loads it dynamically in a
                Loader type. This is usually done to improve startup time.
            \li QML type name
        \row
            \li Add a message suppression comment
            \li Prepends the line with an annotation comment that stops the
                message from being generated.
            \li Error, warning or hint from static analysis

    \endtable
   */
