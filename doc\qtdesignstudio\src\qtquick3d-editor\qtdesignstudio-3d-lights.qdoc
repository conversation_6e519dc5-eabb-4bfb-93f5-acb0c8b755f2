// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-lights.html
    \previouspage studio-3d-custom-effects-materials.html
    \nextpage studio-3d-camera.html

    \title Lights

    Light components are the primary source of lighting in a \QDS scene.
    As a secondary light source, you can use \l{Setting the Light Probe}
    {image-based lighting}.

    To add a light component to your UI, do one of the following:
    \list
      \li Drag a light component from \uicontrol Components >
      \uicontrol {Qt Quick 3D} to the \l {3D} view or to
      \l Navigator > \uicontrol {View3D} > \uicontrol Scene.
      \li Right-click in the \uicontrol 3D view and select
      \uicontrol Create > \uicontrol Lights from the context menu.
    \endlist

    If you cannot find the light components in \uicontrol {Components}, add the
    \uicontrol {Qt Quick 3D} module to your project as instructed in
    \l {Adding and Removing Modules}.

   \note If you select \uicontrol {Qt 5} as the \uicontrol {Target Qt Version}
    when \l {Creating Projects}{creating your project}, the available light
    components and their properties will be slightly different. The properties
    may also be situated differently in the \uicontrol Properties view.

    By default, all imported scenes are created with one directional light.
    You can use the following components to add lights:

    \table
    \header
        \li Icon
        \li Name
        \li Qt 5 Only
        \li More Information

    \row
        \li \inlineimage directional.png
        \li Directional Light
        \li
        \li \l{DirectionalLight}{Light Directional}

    \row
        \li \inlineimage point.png
        \li Point Light
        \li
        \li \l{PointLight}{Light Point}

    \row
        \li \inlineimage spot.png
        \li Spot Light
        \li
        \li \l{SpotLight}{Light Spot}

    \row
        \li \inlineimage area.png
        \li Area Light
        \li \inlineimage ok.png
        \li \l{AreaLight}{Light Area}
    \endtable

    \note Each additional light negatively effects the rendering performance
    of your scene. Keep scenes as simple as possible and use lights sparingly.
    Use a \l{SceneEnvironment}{Scene Environment} component to apply image-based
    lighting that can produce soft and subtle lighting.

    To edit light properties, select a light component in the \uicontrol Navigator
    or \uicontrol{3D} view, and then adjust its properties in the
    \uicontrol Properties view or by dragging the yellow light gizmo handles in
    the \uicontrol{3D} view.

    To specify an overall multiplier for a light component's effects, adjust the
    \uicontrol Brightness property. The \uicontrol Scope property specifies which
    component, with its children, is illuminated by the light. Set the
    \l{Picking Colors}{Color} property to specify the color applied to models
    illuminated by a light. Set the \uicontrol {Ambient color} property to
    specify the ambient color applied to materials before being illuminated by
    the light.

    You can animate light properties in the \l Timeline view.

    \section1 Directional Light

    A directional light emits light in one direction from an unidentifiable
    source located infinitely far away. This is similar to sunlight.

    Use the \uicontrol Brightness handle of the light gizmo (1) to adjust the
    \uicontrol Brightness property of any of the light components.

    \image studio-3d-directional-light.png "Models lit by a directional light."

    If the \uicontrol {Casts shadow} property is enabled, shadows are positioned
    parallel to the light direction. A directional light has infinite range and
    does not diminish.

    Moving a directional light does not have any effect. The light will always
    be emitted in the direction of the light's z axis. Rotating the light along
    its x or y axis will change the direction in which the light is emitted.

    Scaling a directional light will only have an effect in the following cases:

    \list
        \li If the z scale is set to a negative number, the light will be
            emitted in the opposite direction.
        \li If the scale of any axis is set to 0, the light will be emitted
            along the world's z axis. Rotating the light has no effect.
    \endlist

    \section1 Point Light

    A point light can be described as a sphere that emits light with equal
    strength in all directions from the center of the light. This is similar
    to the way a light bulb emits light.

    \image studio-3d-point-light.png "Models lit by a point light."

    Lighting is applied outwards from the center of a point light, becoming
    increasingly dim away from the center. Moving a point light changes the
    position from where the light is emitted. Rotating or scaling a point
    light does not have any effect.

    To control the fade-off and range of a point light, set the
    \uicontrol {Constant fade}, \uicontrol {Linear fade}, and
    \uicontrol {Quadratic fade} properties. Constant fade is the constant
    factor of the \e attenuation term of the light. Attenuation refers to
    the reduction in the intensity of light as it travels through a medium
    due to absorption or scattering of photons.

    Turn up the linear fade value to increase the rate at which the lighting
    effect dims the light in proportion to the distance to the light. The value
    0.0 means that the light doesn't have linear fade.

    Turn up the quadratic fade to increase the rate at which the lighting effect
    dims on surfaces that are far away from the light. The value 1.0 means that
    the point light fade exactly follows the inverse square law. For example,
    when the distance to a component doubles, the light intensity decreases to
    one fourth. Adjust the \uicontrol {Quadratic fade} in the Properties view,
    or by using the light gizmo handle (2).

    Aside from fade, a point light has the same properties as a directional
    light.

    \section1 Spot Light

    A spot light emits light towards one direction in a cone shape.
    The light intensity diminishes when approaching the value of the
    \uicontrol {Cone angle} property. The angle at which the light
    intensity starts to diminish is defined by the
    \uicontrol {Inner cone angle} property. Both angles are defined in degrees
    in the \uicontrol Properties view. Use the Properties view or the light gizmo
    handles to adjust the \uicontrol {Cone angle} (3),
    \uicontrol {Inner cone angle} (4), and \uicontrol {Quadratic fade} (5)
    properties.

    \image studio-3d-spot-light.png "A model lit by a spot light."

    Inside the inner cone angle, the spot light behaves similarly to the point
    light. There the light intensity diminishes according to inverse-square-law.
    However, the fade-off (and range) can be controlled with the
    \uicontrol {Constant fade}, \uicontrol {Linear fade}, and
    \uicontrol {Quadratic fade} properties.

    \section1 Area Light

    \note The \uicontrol {Area Light} component is only available in Qt 5.

    An area light is similar to the directional light. However, instead of
    emitting an equally bright light across the whole scene, the area light
    emits directional light from a rectangle shaped component. Use the light
    gizmo or the \uicontrol Properties view to set the \uicontrol Width (6)
    and \uicontrol Height (7) properties to determine the size of the area light.

   \image studio-3d-area-light.png "A model lit by two area lights."

    Aside from the size, an area light has the same properties as a directional
    light.

    The image below shows an example on how to light a component with different
    colors using two different area lights.

    You can rotate, scale, and move area lights.

    \section1 Shadows

    To simulate shadows using this light, enable the \uicontrol {Cast shadows}
    check box. Cast shadows work best with area or point lights.

    To specify the darkness of the shadows, set the \uicontrol {Shadow factor}
    property. The value 0 means no shadows are cast.

    To specify the amount of blur applied to the shadows, set the
    \uicontrol {Shadow filter} property.

    Tweak the \uicontrol {Shadow bias} property value by small amounts
    if you see components casting shadows on themselves.

    To specify the quality of the shadow map created for shadow rendering, set
    the \uicontrol {Shadow map quality} property. Lower quality uses less
    resources, but produces lower quality shadows, while higher quality uses
    more resources to produce better quality shadows.

    To specify the maximum distance for the shadow map, set the
    \uicontrol {Shadow map far} property value. Using smaller values
    may improve the precision and effects of the map.
*/
