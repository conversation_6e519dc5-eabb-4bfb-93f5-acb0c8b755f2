// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-editor-codepasting.html
    \page creator-macros.html
    \nextpage creator-diff-editor.html

    \title Using Text Editing Macros

    To record a text editing macro, select \uicontrol Tools >
    \uicontrol {Text Editing Macros} > \uicontrol {Record Macro}
    or press \key {Alt+[}. To stop recording, select \uicontrol Tools >
    \uicontrol {Text Editing Macros} > \uicontrol {Stop Recording Macro} or
    press \key {Alt+]}.

    \note The macro recorder does not support code completion.

    To play the last macro, select \uicontrol Tools >
    \uicontrol {Text Editing Macros} > \uicontrol {Play Last Macro} or
    press \key {Alt+R}.

    To save the last macro, select \uicontrol Tools >
    \uicontrol {Text Editing Macros} > \uicontrol {Save Last Macro}.

    To assign a keyboard shortcut to a text editing macro, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Environment >
    \uicontrol Keyboard. For more information, see
    \l{Configuring Keyboard Shortcuts}.

    You can also use the \c rm locator filter to run a macro. For more
    information, see \l{Searching with the Locator}.

    To view and remove saved macros, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol Macros.
*/
