// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-qml-debugging-example.html
    \if defined(qtdesignstudio)
    \previouspage creator-debugging-qml.html
    \nextpage creator-qml-performance-monitor.html
    \else
    \previouspage creator-debugging-example.html
    \nextpage creator-troubleshooting-debugging.html
    \endif

    \title Debugging a Qt Quick Example Application

    This section uses the
    \l{QML Advanced Tutorial}{Same Game}
    example application to illustrate how to debug Qt Quick applications in the
    \uicontrol Debug mode.

    For more information about all the options you have, see
    \l{Debugging Qt Quick Projects}.

    \if defined(qtdesignstudio)
    \note In this section, you are using advanced menu items. These are not
    visible by default. To toggle the visibility of advanced menu items, see
    \l{Customizing the Menu}.

    \endif

    The Same Game demo shows how to write a game in QML, using JavaScript for
    all the game logic. Open the demo project in \QC to debug it:

    \list 1

        \li To look at the code that starts a new game, place a breakpoint in
            samegame.qml by clicking between the line number and the window
            border on the line where where the \c startNewGame() function is
            called (1).

            \image qtquick-example-setting-breakpoint1.png

            The red circle indicates that a breakpoint is now set on that line
            number.

        \li Select \uicontrol Debug > \uicontrol {Start Debugging} >
            \uicontrol {Start Debugging of Startup Project} or press
            \key{F5}.

        \li Once the Same Game application starts, select \uicontrol {Puzzle}
            to start a new game.

            \image samegame.png

        \li When the debugger hits the breakpoint, it interrupts the
            application. \QC displays the nested function calls leading to the
            current position as a call stack trace (1).

            \image qtquick-example-setting-breakpoint2.png

        \li Click the \inlineimage icons/debugger_stepinto_small.png
            (\uicontrol {Step Into}) button on the toolbar or press \key F11 to step
            into the code in the stack. The samegame.js file opens in the code
            editor at the function that starts a new game.

            \image qtquick-example-stack.png

        \li Examine the local variables in the \uicontrol Locals
            view. Step through the code to see how the information changes in
            the view.

        \li Add a breakpoint at the end of the \c {startNewGame()} function, and
            click \inlineimage icons/qtcreator-debugging-continue.png
            (\uicontrol Continue) to hit the breakpoint.

            \image qtquick-example-setting-breakpoint3.png

        \li To execute JavaScript commands in the current context, open the
            \uicontrol {QML Debugger Console}.

            \image qml-script-console.png

        \li To remove a breakpoint, right-click it and select
            \uicontrol {Delete Breakpoint}.

        \li In the \uicontrol Locals view, explore the object structure at
            runtime.

            \image qtquick-example-qml-inspector.png

        \li Select \uicontrol Debug > \uicontrol {Show Application on Top} to
            keep the application visible while you interact with the debugger.

        \li Select \uicontrol Debug > \uicontrol Select to activate selection
            mode and then click the \uicontrol Menu button to move into the
            \uicontrol menuButton component in the \uicontrol Locals view and
            the code editor.

        \li In the \uicontrol Locals view, double-click the value of a property
            to change it.

    \endlist

*/
