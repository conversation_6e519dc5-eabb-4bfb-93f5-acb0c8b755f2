// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \example Loginui3
    \ingroup gstutorials

    \title Log In UI - States
    \brief Illustrates how to use states to create a second UI page.
    \previouspage {Log In UI - Positioning}
    \nextpage {Log In UI - Timeline}

    \image loginui3.gif "Log In UI"

    \e{Log In UI - States} is the third in a series of tutorials that build
    on each other to illustrate how to use \QDS to create a simple UI with
    some basic UI components, such as pages, buttons, and entry fields. The
    third tutorial in the series describes how to use \e states to add a
    second page to the UI. On the first page, users can enter a username
    and password to log in. On the second page, they can register if they
    do not already have an account.

    Because the second page will contain most of the same UI components as the
    login page, you will use \e states to show and hide UI components as necessary
    when a user selects the \e {Create Account} button.

    The starting point for this tutorial is the completed
    \l{Log In UI - Positioning} project. You can download the project from
    \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/tutorial%20projects/Loginui2}{here}.

    Additionally, you can download the completed project of this tutorial from
    \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/tutorial%20projects/Loginui3}{here}.

    The \e {Learn More} sections provide additional information relevant to the
    task at hand.

    \section1 Adding UI Components

    You will add another entry field for verifying the password that users
    enter to create an account. You are already familiar with the tasks in
    this section from previous parts of the tutorial.

    To preview the changes that you make to the UI while you make
    them, select the \inlineimage icons/live_preview.png
    (\uicontrol {Show Live Preview}) button on the \l {2D} view
    toolbar or press \key {Alt+P}.

    To add the entry field needed on the second page to the \e Screen01
    component:

    \list 1
        \li Double-click \e Screen01.ui.qml in \uicontrol Projects to open it
            in the \uicontrol {2D} view.
        \li Drag-and-drop an instance of the EntryField component from
            \uicontrol Components > \uicontrol {My Components} to \e fields in
            \l Navigator.
        \li Select the EntryField instance in \uicontrol Navigator to modify
        its ID and text in \uicontrol Properties.
        \li In \uicontrol Component > \uicontrol ID, enter
            \e repeatPassword.
        \li In \uicontrol {Button Content} > \uicontrol Text, enter
            \e {Repeat Password} and select \uicontrol tr to mark the text
            translatable.
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    \e Screen01 should now look something like this in the
    \uicontrol {2D} view:

    \image loginui3-base-state.jpg "Login page with a Repeat Password field"

    Next, you will add states for the login page and the account creation page,
    where you use the visibility property to hide the repeat password field on
    the login page and the \e {Create Account} button on the account creation
    page.

    \section1 Using States to Simulate Page Changes

    You will now add \l{Working with States}{states} to the UI to show and hide UI
    components in the \uicontrol {2D} view, depending on the current page:

    \list 1
        \li In the \l States view, select \inlineimage icons/plus.png
        .
        \li Enter \e login as the state name.
            \image loginui3-login-state.jpg "States view"
        \li Select \e repeatPassword in \uicontrol Navigator to display its
            properties in \uicontrol Properties.
        \li In the \uicontrol Visibility section, deselect the
            \uicontrol Visible check box to hide the repeat
            password field in the login state.
            \image loginui3-visibility.png
        \li In \uicontrol States, select \inlineimage icons/action-icon.png
            for \e login to open the \uicontrol Actions menu, and then
            select \uicontrol {Set as Default} to determine that the \e login
            state is applied when the application starts.
        \li With the base state selected, add another state and name it
            \e createAccount. This state should now look identical to the base
            state.
        \li Select \e createAccount in the \uicontrol Navigator to display its
            properties in \uicontrol Properties.
        \li In \uicontrol Visibility, deselect the \uicontrol Visible check box
            to hide the \e {Create Account} button in the account creation
            state.
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    You can now see all the states in the \uicontrol States view:

    \image loginui3-states.jpg "All states in the States view"

    The live preview displays the default state, \e login:

    \image loginui3-login-state-preview.jpg "Preview of the login state"

    Next, you will create connections to specify that clicking the
    \uicontrol {Create Account} button on the login page triggers a
    transition to the account creation page.

    \section1 Connecting Buttons to States

    Components have predefined signals that are emitted when users interact
    with the UI. The \e PushButton component contains a \l{Mouse Area} component
    that has a \e clicked signal. The signal is emitted whenever the mouse
    is clicked within the area.

    You will now use the \l {Connections} view to
    \l{Connecting Components to Signals}{connect} the clicked signal of the
    \e createAccount button to \e createAccount state:

    \list 1
        \li Select \uicontrol View > \uicontrol Views >
            \uicontrol {Connections} to open the \uicontrol Connections view.
        \li Select \e createAccount in \uicontrol Navigator.
        \li In the \uicontrol Connections tab, select the \inlineimage icons/plus.png
            button to add the action that the \c onClicked signal handler of
            \e createAccount should apply.
        \li Double-click the value \uicontrol Action column and select
            \uicontrol {Change state to createAccount} in the drop-down menu.
        \note Or, you can right-click the \e createAccount button in \l Navigator.
              Then select \uicontrol {Connections} > \uicontrol {Add signal handler} >
              \uicontrol {clicked} > \uicontrol {Change State to createAccount}.
        \image loginui3-connections.png "Connections tab"
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.

    \endlist

    In the live preview, you can now click the \uicontrol {Create Account}
    button to go to the account creation page.

    \image loginui3.gif "Moving between login page and account creation page"

    \section1 Learn More
    \section2 States
    The \l{Working with States}{state} of a particular visual component is the set of
    information which describes how and where the individual parts of the visual
    component are displayed within it, and all the data associated with that
    state. Most visual components in a UI will have a limited number of states,
    each with well-defined properties.

    For example, a list item may be either selected or not, and if
    selected, it may either be the currently active single selection or it
    may be part of a selection group. Each of those states may have certain
    associated visual appearance (neutral, highlighted, expanded, and so on).

    Youn can apply states to trigger behavior or animations. UI components
    typically have a default state that contains all of a component's initial
    property values and is therefore useful for managing property values before
    state changes.

    You can specify additional states by adding new states. Each state within a
    component has a unique name. To change the current state of an component,
    the state property is set to the name of the state. State changes can be
    bound to conditions by using the \c when property.

    \section2 Signal and Event Handlers

    UI components need to communicate with each other. For example, a button
    needs to know that the user has clicked on it. In response, the button may
    change color to indicate its state and perform an action.

    To accomplish this, a \e {signal-and-handler} mechanism is used, where the
    \e signal is the event that is responded to through a \e {signal handler}.
    When a signal is emitted, the corresponding signal handler is invoked.
    Placing logic, such as a script or other operations, in the handler allows
    the component to respond to the event.

    For more information, see \l{Signal and Handler Event System}.

    \section1 Next Steps

    For a more complicated UI, you would typically use components that specify
    a view of items provided by a model, such as a \l{List and Grid Views}
    {List View} or \l StackView. For more information, see
    \l{Lists and Other Data Models}.

    To learn how to use a timeline to animate the transition between the login
    and account creation pages, see the next tutorial in the series,
    \l {Log In UI - Timeline}.
*/
