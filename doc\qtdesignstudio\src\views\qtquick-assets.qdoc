// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-assets.html
    \previouspage quick-components-view.html
    \nextpage qtquick-navigator.html

    \title Assets

    The \uicontrol Assets view lists available assets.

    \uicontrol {Assets} displays the images and other files
    that you add to the project folder by dragging-and-dropping external asset
    files to \QDS or by selecting \inlineimage icons/plus.png
    . For more information about importing assets to \QDS, see
    \l {Importing 2D Assets} and \l {Importing 3D Assets}.

    To add assets to your UI, drag-and-drop them from
    \uicontrol Assets to the \l Navigator, \l {2D}, or \l {3D} view.

    To add multiple assets to your UI simultaneously, multiselect them first by
    holding \key Ctrl and clicking the asset files you wish to select.

    \image qtquick-assets-tab.png "Assets view"

    When you drag-and-drop assets from \uicontrol Assets to the \l Navigator
    or \l {2D} view, component instances with a suitable type are
    automatically created for you. For example, instances of the
    \l{Images}{Image} component will be created for graphics files.

    \section1 Context Menu Commands

    \image qtquick-library-context-menu.png "Context menu commands in Assets"

    To use the context menu commands in \uicontrol Assets, right-click the
    name of a folder and select one of the following commands:

    \list
        \li \uicontrol {Expand All}: expands all folders.
        \li \uicontrol {Collapse All}: collapses all folders.
        \li \uicontrol {Rename Folder}: prompts you to enter a new name
            for the folder.
        \li \uicontrol {New Folder}: creates a new folder.
        \li \uicontrol {Delete Folder}: deletes the folder.
    \endlist

*/
