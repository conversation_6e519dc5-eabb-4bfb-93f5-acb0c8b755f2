// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage psqtbridge.html
    \page qtbridge-ps-setup.html
    \nextpage qtbridge-ps-using.html

    \title Setting Up \QBPS

    \QBPS is included in the
    \l{https://www.qt.io/pricing}{Qt Design Studio Enterprise license}.
    You can use the Qt Installer to have the \QBPS plugin package copied to the
    following path in your Qt installation folder:

    \list
        \li On Windows: \c {Tools\QtDesignStudio\photoshop_bridge}
        \li On \macos: \c {QtDesignStudio/photoshop_bridge}.
    \endlist

    \QBPS is delivered as an Adobe extension (ZXP)
    package and requires Adobe Photoshop version 20.0.0, or later
    to be installed. The \QBPS installation process differs depending
    on whether you are installing on
    Windows or \macos.

    \section1 Installing on Windows

    To install \QBPS on Windows:

    \list 1
        \li Copy the \QBPS ZXP package from
            \c {Qt\Tools\QtDesignStudio\photoshop_bridge} to the
            \c Documents directory in your user directory.
        \li Open Windows PowerShell.
        \li Enter the following commands:
        \badcode
            cd "$env:UserProfile\Documents"
            mv .\io.qt.QtBridge.zxp .\io.qt.QtBridge.zip
            expand-archive .\io.qt.QtBridge.zip
            xcopy /E /I .\io.qt.QtBridge  "$env:APPDATA\Adobe\CEP\extensions\io.qt.QtBridge"
        \endcode
    \endlist

    \section1 Installing on \macos

    To install \QBPS on \macos:

    \list 1
        \li Copy the \QBPS ZXP package from
            \c {Qt/QtDesignStudio/photoshop_bridge}
            to your \c Documents directory.
        \li Open Terminal.
        \li Enter the following commands:
        \badcode
            cd ~/Documents
            unzip io.qt.QtBridge.zxp -d io.qt.QtBridge
            sudo mkdir -p /Library/Application\ Support/Adobe/CEP/extensions
            sudo cp -R ./io.qt.QtBridge /Library/Application\ Support/Adobe/CEP/extensions
        \endcode
    \endlist

    \note On \macos \QBPS fails to load when Adobe Photoshop runs natively on an ARM
    processor (Apple silicon). For more information, see \l {Running \QBPS on Apple Silicon}.

    \section1 Enabling Remote Connections

    To set up \QBPS:

    \list 1
        \li Once the installation is completed, restart Adobe Photoshop to make
            \QBPS appear in \uicontrol Window > \uicontrol {Extensions (Legacy)}.
        \li Select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol General > \uicontrol Plug-ins to enable a remote
            connection.
        \li Select the \uicontrol {Enable Remote Connections} check box and
            enter a password in the \uicontrol Password field.
        \li To test that the connection is working properly, start \QBPS and
            select the settings icon in the top right corner.
            \image qt-bridge-settings.png
        \li In the \uicontrol Password field, enter the password you entered in
            Adobe Photoshop and select \uicontrol {Connect}.
        \li In the \uicontrol {Export Path} group, select the folder button
            to specify the location where \QBPS copies the exported files.
        \li In the \uicontrol {Asset format & scale} group, select the default
            asset format (JPG, PNG, or SVG) and DPI to use for each layer.
    \endlist

    Once the connection is successfully created, \QBPS is ready to use.

    \note

    \section1 Running \QBPS on Apple Silicon

    If you are using \macos on an ARM processor (Apple silicon), \QBPS may not be listed
    in Adobe Photoshop under \uicontrol Window > \uicontrol {Extensions (Legacy)}.
    Run the Adobe Photoshop app in the Rosetta emulation mode.
    Right click the Adobe Photoshop.app and select \uicontrol{Get info} >
    \uicontrol {Open using Rosetta}.
    Once the app is running in the Rosetta emulation mode, make sure
    \uicontrol Preferences > \uicontrol Plug-ins > \uicontrol {Legacy Extensions} is selected.
*/
