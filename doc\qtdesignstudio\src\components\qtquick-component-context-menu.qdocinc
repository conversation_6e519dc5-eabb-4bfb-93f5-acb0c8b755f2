// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [context-menu]
    \section1 Context Menu

    The following table summarizes the \uicontrol Navigator and
    \uicontrol {2D} views context menu items and provides links
    to more information about them.

    \table
    \header
        \li To Learn About
        \li Go To
    \row
        \li Arrange
        \li \l{Arranging Components}
    \row
        \li Edit
        \li \l{Showing and Hiding Components}
    \row
        \li Anchors
        \li \l{Setting Anchors and Margins}
    \if defined(qtdesignstudio)
    \row
        \li Group
        \li \l{Organizing Components}
    \endif
    \row
        \li Position
        \li \l{Using Positioners}
    \row
        \li Layout
        \li \l{Using Layouts}
    \row
        \li Stacked Container
        \li \l{Lists and Other Data Models}
    \row
        \li Timeline
        \li \l{Creating a Timeline}
    \if defined(qtdesignstudio)
    \row
        \li Event List
        \li \l{Simulating Events}
    \endif
    \row
        \li Edit Color
        \li \l{Editing Properties Inline}
    \row
        \li Edit Annotation
        \li \l{Annotating Designs}
    \row
        \li Merge File with Template
        \li \l{Merging Files with Templates}
    \row
        \li Move Component Instances into Separate Files
        \li \l{Turning Component Instances into Custom Components}
    \row
        \li Add New Signal Handler
        \li \l{Adding Signal Handlers}
    \row
        \li Go to Implementation
        \li \l{Using UI Files}
    \row
        \li Go into Component
        \li \l{Moving Within Components}
    \endtable
//! [context-menu]
*/
