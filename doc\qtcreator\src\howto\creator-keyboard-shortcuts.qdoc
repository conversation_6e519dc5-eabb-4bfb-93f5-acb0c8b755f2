// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-keyboard-shortcuts.html
    \if defined(qtdesignstudio)
    \previouspage creator-project-managing-sessions.html
    \nextpage studio-projects.html
    \else
    \previouspage creator-cli.html
    \nextpage creator-editor-external.html
    \endif

    \title Keyboard Shortcuts

    \QC provides various keyboard shortcuts to speed up your development
    process. You can add more shortcuts if your favorite combination is
    missing. In addition, you can specify your own keyboard shortcuts for some
    functions that can be easily performed with a mouse, and therefore do not
    appear in menus or have default keyboard shortcuts. For example, selecting
    and deleting words or lines in an editor.

    To view all functions available in \QC and the keyboard shortcuts defined
    for them, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Environment >
    \uicontrol Keyboard. The shortcuts are listed by category. To find a keyboard
    shortcut in the list, enter a function name or shortcut in the \uicontrol Filter
    field.

    \image qtcreator-keyboard-shortcuts.png

    The shortcuts that are displayed in red color are associated with several
    functions. \QC executes the function that is available in the current
    context. If several functions are available for the same shortcut at a
    time, there is a conflict and \QC cannot execute any function.

    A keyboard shortcut might also conflict with a shortcut that a Window
    manager uses for its own purposes. In that case, \QC shortcuts do not work.
    Typically, you can configure the shortcuts in the window manager, but if
    that is not allowed, you can change the \QC shortcuts.
    \if defined(qtcreator)
    For example, Unity on
    Ubuntu 11.10 uses \key F10 in its window manager, and therefore the default
    \QC keyboard shortcut \key F10 (Step Over) does not work on that system.
    \endif


    To override the platform default value that determines whether
    keyboard shortcuts are shown in the labels of context menu items,
    select \uicontrol Edit > \uicontrol Preferences > \uicontrol Environment >
    \uicontrol Interface. The label of the \uicontrol {Show keyboard shortcuts
    in context menus} check box indicates whether the platform default value
    is \c on or \c off.

    \image qtcreator-options-environment-interface.png "Interface tab in the Environment preferences"

    \section1 Configuring Keyboard Shortcuts

    To customize a keyboard shortcut:

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Environment
            > \uicontrol Keyboard.

        \li Select a command from the list.

        \li In the \uicontrol{Key Sequence} field, you have the following
            options:

            \list

                \li Enter the shortcut key you want to associate with the
                    selected command.

                \li Select \uicontrol Record, press the keys to use as the
                    keyboard shortcut, and select \uicontrol {Stop Recording}
                    when you are done.

            \endlist

        \li To assign multiple keyboard shortcuts to a function, select
            \uicontrol Add, and enter or record an additional key combination.

        \li To revert to the default shortcut, select \uicontrol Reset.

    \endlist

    \QC allows you to use different keyboard shortcut mapping schemes:

    \list

        \li To import a keyboard shortcut mapping scheme, click \uicontrol Import
            and select the .kms file containing the keyboard shortcut mapping scheme
            you want to import.

        \li To export the current keyboard shortcut mapping scheme, click
            \uicontrol Export and select the location where you want to save the
            exported .kms file.

    \endlist

    \section1 Default Keyboard Shortcuts

    The following tables list the default keyboard shortcuts. They are
    categorized by actions.

    \section2 General Keyboard Shortcuts

    \table
        \header
            \li  Action
            \li  Keyboard shortcut
        \row
            \li  Open file or project
            \li  Ctrl+O
        \row
            \li  New project
            \li  Ctrl+Shift+N
        \row
            \li  New file
            \li  Ctrl+N
        \row
            \li  Open in external editor
            \li  Alt+V, Alt+I
        \row
            \li  Select all
            \li  Ctrl+A
        \row
            \li  Delete
            \li  Del
        \row
            \li  Cut
            \li  Ctrl+X
        \row
            \li  Copy
            \li  Ctrl+C
        \row
            \li  Paste
            \li  Ctrl+V
        \row
            \li  Redo
            \li  Ctrl+Y
        \row
            \li  Print
            \li  Ctrl+P
        \row
            \li  Save
            \li  Ctrl+S
        \row
            \li  Save all
            \li  Ctrl+Shift+S
        \row
            \li  Close window
            \li  Ctrl+W
        \row
            \li  Close all
            \li  Ctrl+Shift+W
        \row
            \li  Close current file
            \li  Ctrl+F4
        \row
            \li  Go back
            \li  Alt+Left
        \row
            \li  Go forward
            \li  Alt+Right
        \row
            \li  Go to line
            \li  Ctrl+L
        \row
            \li  Next open document in history
            \li  Ctrl+Shift+Tab
        \row
            \li  Go to other split
            \li  Ctrl+E, O
        \row
            \li  Previous open document in history
            \li  Ctrl+Tab
        \row
            \li  Activate \uicontrol Locator
            \li  Ctrl+K
        \row
            \li  Switch to \uicontrol Welcome mode
            \li  Ctrl+1
        \row
            \li  Switch to \uicontrol Edit mode
            \li  Ctrl+2
        \row
            \li  Switch to \uicontrol Design mode
            \li  Ctrl+3
        \row
            \li  Switch to \uicontrol Debug mode
            \li  Ctrl+4
        \row
            \li  Switch to \uicontrol Projects mode
            \li  Ctrl+5
        \row
            \li  Switch to \uicontrol Help mode
            \li  Ctrl+6
        \row
            \li  Toggle \uicontrol{Issues}
            \li  Alt+1 (Cmd+1 on \macos)
        \row
            \li  Toggle \uicontrol{Search Results}
            \li  Alt+2 (Cmd+2 on \macos)
        \row
            \li  Toggle \uicontrol{Application Output}
            \li  Alt+3 (Cmd+3 on \macos)
        \row
            \li  Toggle \uicontrol{Compile Output}
            \li  Alt+4 (Cmd+4 on \macos)
        \row
            \li  Toggle other output views
            \li  Alt+number (Cmd+number on \macos)

            Where the number is the number of the view.
        \if defined(qtcreator)
        \row
            \li  Activate \uicontrol Bookmarks view
            \li  Alt+M
        \endif
        \row
            \li  Activate \uicontrol{File System} view
            \li  Alt+Y
        \row
            \li  Activate \uicontrol{Open Documents} view
            \li  Alt+O
        \row
            \li  Maximize output views
            \li  Alt+9
        \row
            \li  Move to next item in output
            \li  F6
        \row
            \li  Move to previous item in output
            \li  Shift+F6
        \row
            \li  Activate \uicontrol Projects view
            \li  Alt+X
        \row
            \li  Full screen
            \li  Ctrl+Shift+F11
        \row
            \li  Toggle the sidebar
            \li  Alt+0 (Cmd+0 on \macos)
        \row
            \li  Undo
            \li  Ctrl+Z
        \row
            \li  Move to \uicontrol Edit mode

               In \uicontrol Edit mode:
               \list
                \li  The first press moves focus to the editor
                \li  The second press closes secondary windows
               \endlist
            \li  Esc
        \row
            \li  Exit \QC

                 By default, \QC exits without asking for confirmation, unless
                 there are unsaved changes in open files. To always be asked,
                 select the \uicontrol {Ask for confirmation before exiting}
                 check box in \uicontrol Edit > \uicontrol Preferences >
                 \uicontrol Environment > \uicontrol System.
            \li  Ctrl+Q
    \endtable

    \section2 Editing Keyboard Shortcuts

    \table
        \header
            \li  Action
            \li  Keyboard shortcut
        \row
            \li  Auto-indent selection
            \li  Ctrl+I
        \row
            \li  Collapse
            \li  Ctrl+<
        \row
            \li  Expand
            \li  Ctrl+>
        \row
            \li  Trigger a completion in this scope
            \li  Ctrl+Space
        \row
            \li  Display tooltips for function signatures regardless of the
                 cursor position in the function call
            \li  Ctrl+Shift+D
        \row
            \li  Copy line
            \li  Ctrl+Ins
        \row
            \li  Copy line down
            \li  Ctrl+Alt+Down
        \row
            \li  Copy line up
            \li  Ctrl+Alt+Up
        \row
            \li  Paste from the clipboard history
            \li  Ctrl+Shift+V

               Subsequent presses move you back in the history
        \row
            \li  Cut line
            \li  Shift+Del
        \row
            \li  Join lines
            \li  Ctrl+J
        \row
            \li  Insert line above current line
            \li  Ctrl+Shift+Enter
        \row
            \li  Insert line below current line
            \li  Ctrl+Enter
        \row
            \li  Decrease font size
            \li  Ctrl+- (Ctrl+Roll mouse wheel down)
        \row
            \li  Increase font size
            \li  Ctrl++ (Ctrl+Roll mouse wheel up)
        \row
            \li  Reset font size
            \li  Ctrl+0
        \row
            \li  Toggle Vim-style editing
            \li  Alt+Y, Alt+Y
        \row
            \li  Split
            \li  Ctrl+E, 2
        \row
            \li  Split side by side
            \li  Ctrl+E, 3
        \row
            \li  Remove all splits
            \li  Ctrl+E, 1
        \row
            \li  Remove current split
            \li  Ctrl+E, 0
        \row
            \li  Select all
            \li  Ctrl+A
        \row
            \li  Go to block end
            \li  Ctrl+]
        \row
            \li  Go to block start
            \li  Ctrl+[
        \row
            \li  Go to block end and select the lines between the current cursor
                 position and the end of the block
            \li  Ctrl+Shift+]
        \row
            \li  Go to block start and select the lines between the current
                 cursor position and the beginning of the block
            \li  Ctrl+Shift+[
        \row
            \li  Select the current block

                 The second press extends the selection to the parent block. To
                 enable this behavior, select \uicontrol Edit >
                 \uicontrol Preferences > \uicontrol {Text Editor} >
                 \uicontrol Behavior >
                 \uicontrol {Enable smart selection changing}.
            \li  Ctrl+U
        \row
            \li  Undo the latest smart block selection
            \li  Ctrl+Alt+Shift+U
        \row
            \li  Move current line down
            \li  Ctrl+Shift+Down
        \row
            \li  Move current line up
            \li  Ctrl+Shift+Up
        \row
            \li  Trigger a refactoring action in this scope
            \li  Alt+Enter
        \row
            \li  Rewrap paragraph
            \li  Ctrl+E, R
        \row
            \li  Enable text wrapping
            \li  Ctrl+E, Ctrl+W
        \row
            \li  Toggle comment for selection
            \li  Ctrl+/
        \row
            \li  Visualize whitespace
            \li  Ctrl+E, Ctrl+V
        \row
            \li  Adjust size
            \li  Ctrl+J
        \row
            \li  Lay out in a grid
            \li  Ctrl+G
        \row
            \li  Lay out horizontally
            \li  Ctrl+H
        \row
            \li  Lay out vertically
            \li  Ctrl+L
        \row
            \li  Preview
            \li  Alt+Shift+R
        \if defined(qtcreator)
        \row
            \li  Edit signals and slots
            \li  F4
        \row
            \li  Toggle bookmark
            \li  Ctrl+M
        \row
            \li  Go to next bookmark
            \li  Ctrl+.
        \row
            \li  Go to previous bookmark
            \li  Ctrl+,
        \row
            \li  Fetch snippet
            \li  Alt+C, Alt+F
        \row
            \li  Paste snippet
            \li  Alt+C, Alt+P
        \endif
        \row
            \li  Find references to symbol under cursor
            \li  Ctrl+Shift+U
        \row
            \li  Follow symbol under cursor

               Works with namespaces, classes, functions, variables, include
               statements and macros
            \li  F2
        \row
            \li  Rename symbol under cursor
            \li  Ctrl+Shift+R
        \row
            \li  Switch between function declaration and definition
            \li  Shift+F2
        \row
            \li  Open type hierarchy
            \li  Ctrl+Shift+T
            \if defined(qtcreator)
        \row
            \li  Switch between header and source file
            \li  F4
        \endif
        \row
            \li Add a cursor at the next occurrence of selected text for
                multi-cursor editing
            \li  Ctrl+D
        \row
            \li  Turn selected text into lowercase
            \li  Alt+U
        \row
            \li  Turn selected text into uppercase
            \li  Alt+Shift+U
        \row
            \li  Sort selected lines alphabetically
            \li  Alt+Shift+S
        \row
            \li  Run static checks on JavaScript code to find common problems
            \li  Ctrl+Shift+C
        \row
            \li  Find and replace
            \li  Ctrl+F
        \row
            \li  Find next
            \li  F3
        \row
            \li  Find previous
            \li  Shift+F3
        \row
            \li  Find next occurrence of selected text
            \li  Ctrl+F3
        \row
            \li  Find previous occurrence of selected text
            \li  Ctrl+Shift+F3
        \row
            \li  Replace next
            \li  Ctrl+=
        \row
            \li  Open advanced find
            \li  Ctrl+Shift+F
        \if defined(qtcreator)
        \row
            \li  Record a text-editing macro
            \li  Alt+[
        \row
            \li  Stop recording a macro
            \li  Alt+]
        \row
            \li  Play last macro
            \li  Alt+R
        \row
            \li  Show Qt Quick toolbars
            \li  Ctrl+Alt+Space
        \row
            \li  Execute user actions in FakeVim mode
            \li  Alt+Y, n, where n is the number of the user action, from 1 to 9
        \endif
    \endtable

    \if defined(qtcreator)
    \section3 Emacs Shortcuts

    You can specify shortcuts for executing actions in a way that is familiar to
    \l{https://www.gnu.org/software/emacs/manual/html_node/emacs/index.html}
    {Emacs} editor users. The actions are not bound to any key combinations by
    default. The following actions are available:

    \list
        \li Copy
        \li Cut
        \li Delete Character
        \li Exchange Cursor and Mark
        \li Go to File End
        \li Go to File Start
        \li Go to Line End
        \li Go to Line Start
        \li Go to Next Character
        \li Go to Next Line
        \li Go to Next Word
        \li Go to Previous Character
        \li Go to Previous Line
        \li Go to Previous Word
        \li Insert Line and Indent
        \li Kill Line
        \li Kill Word
        \li Mark
        \li Scroll Half Screen Down
        \li Scroll Half Screen Up
        \li Yank
    \endlist

    \section2 Image Viewer Shortcuts

    \table
        \header
            \li  Action
            \li  Keyboard shortcut
        \row
            \li  Switch to background
            \li  Ctrl+[
        \row
            \li  Switch to outline
            \li  Ctrl+]
        \row
            \li  Zoom in
            \li  Ctrl++
        \row
            \li  Zoom out
            \li  Ctrl+-
        \row
            \li  Fit to screen
            \li  Ctrl+=
        \row
            \li  Original size
            \li  Ctrl+0
    \endtable
    \endif

    \if defined(qtdesignstudio)
    \section2 Design Mode Keyboard Shortcuts

    You can use the following keyboard shortcuts when editing QML files in the
    \uicontrol Design mode.

    \table
        \header
            \li  Action
            \li  Keyboard shortcut
        \row
            \li  Open the QML file that defines the selected component
            \li  F2
        \row
            \li  Move between \uicontrol the {Code} and
                 \uicontrol {2D} views
            \li  F4
        \row
            \li  Toggle left sidebar
            \li  Ctrl+Alt+0
        \row
            \li  Toggle right sidebar
            \li  Ctrl+Alt+Shift+0
    \endtable
    \endif

    \section2 Debugging Keyboard Shortcuts

    \table
        \header
            \li  Action
            \li  Keyboard shortcut
        \row
            \li  Start or continue debugging
            \li  F5
        \row
            \li  Exit debugger
            \li  Shift+F5
        \row
            \li  Step over
            \li  F10
        \row
            \li  Step into
            \li  F11
        \row
            \li  Step out
            \li  Shift+F11
        \row
            \li  Set or remove breakpoint
            \li  F9 (F8 on \macos)
        \row
            \li  Enable or disable breakpoint
            \li  Ctrl+F9 (Ctrl+F8 on \macos)
        \row
            \li  Run to selected function
            \li  Ctrl+F6
        \row
            \li  Run to line
            \li  Ctrl+F10
        \row
            \li  Reverse direction
            \li  F12
    \endtable

    \section2 Project Keyboard Shortcuts

    \table
        \header
            \li  Action
            \li  Keyboard shortcut
        \if defined(qtcreator)
        \row
            \li  Build project
            \li  Ctrl+B
        \row
            \li  Build all
            \li  Ctrl+Shift+B
        \endif
        \row
            \li  New project
            \li  Ctrl+Shift+N
        \row
            \li  Open project
            \li  Ctrl+Shift+O
        \if defined(qtcreator)
        \row
            \li  Select the \l{glossary-buildandrun-kit}{kit} to build and run your project with
            \li  Ctrl+T
        \endif
        \row
            \li  Run
            \li  Ctrl+R
    \endtable

    \section2 Help Keyboard Shortcuts

    \table
        \header
            \li  Action
            \li  Keyboard shortcut
        \row
            \li  View context-sensitive help
            \li  F1
        \row
            \li  Activate contents in \uicontrol Help mode
            \li  Ctrl+T
        \row
            \li  Add bookmark in \uicontrol Help mode
            \li  Ctrl+M
        \row
            \li  Activate index in \uicontrol Help mode
            \li  Ctrl+I
        \row
            \li  Reset font size
            \li  Ctrl+0
        \row
            \li  Activate search in \uicontrol Help mode
            \li  Ctrl+S
    \endtable

    \section2 Version Control Keyboard Shortcuts

    \if defined(qtcreator)
    \table
        \header
            \li  {1,2} Action
            \li  {6,1} Version control system
        \header
            \li  Bazaar
            \li  CVS
            \li  Git
            \li  Mercurial
            \li  Perforce
            \li  Subversion
        \row
            \li  Add
            \li
            \li  Alt+C, Alt+A
            \li  Alt+G, Alt+A
            \li
            \li  Alt+P, Alt+A
            \li  Alt+S, Alt+A
        \row
            \li  Commit/Submit
            \li  Alt+Z, Alt+C
            \li  Alt+C, Alt+C
            \li  Alt+G, Alt+C
            \li  Alt+G, Alt+C
            \li  Alt+P, Alt+S
            \li  Alt+S, Alt+C
        \row
            \li  Diff
            \li  Alt+Z, Alt+D
            \li  Alt+C, Alt+D
            \li  Alt+G, Alt+D
            \li  Alt+G, Alt+D
            \li
            \li  Alt+S, Alt+D
        \row
            \li  Diff project
            \li
            \li
            \li  Alt+G, Alt+Shift+D
            \li
            \li  Alt+P, Alt+D
            \li
        \row
            \li  Blame/Annotate
            \li
            \li
            \li  Alt+G, Alt+B
            \li
            \li
            \li
        \row
            \li  Log/Filelog
            \li  Alt+Z, Alt+L
            \li
            \li  Alt+G, Alt+L
            \li  Alt+G, Alt+L
            \li  Alt+P, Alt+F
            \li
        \row
            \li  Log project
            \li
            \li
            \li  Alt+G, Alt+K
            \li
            \li
            \li
        \row
            \li  Status
            \li  Alt+Z, Alt+S
            \li
            \li
            \li  Alt+G, Alt+S
            \li
            \li
        \row
            \li  Undo changes/Revert
            \li
            \li
            \li  Alt+G, Alt+U
            \li
            \li  Alt+P, Alt+R
            \li
        \row
            \li  Edit
            \li
            \li
            \li
            \li
            \li  Alt+P, Alt+E
            \li
        \row
            \li  Opened
            \li
            \li
            \li
            \li
            \li  Alt+P, Alt+O
            \li
    \endtable
    \else
    \table
        \header
            \li Action
            \li Keyboard shortcut
        \row
            \li Stage file for commit
            \li Alt+G, Alt+A
        \row
            \li Commit
            \li Alt+G, Alt+C
        \row
            \li Diff current file
            \li Alt+G, Alt+D
        \row
            \li Diff project
            \li Alt+G, Alt+Shift+D
        \row
            \li Blame
            \li Alt+G, Alt+B
        \row
            \li Log current file
            \li Alt+G, Alt+L
        \row
            \li Log project
            \li Alt+G, Alt+K
        \row
            \li Reset
            \li Alt+G, Alt+U
    \endtable
    \endif
*/
