// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-coding.html
    \if defined(qtdesignstudio)
    \previouspage studio-designer-developer-workflow.html
    \else
    \previouspage creator-usability.html
    \endif
    \nextpage creator-editor-functions.html

    \title Coding

    \if defined(qtcreator)
    \image front-coding.png
    \endif

    \list

        \li \l{Writing Code}

            Writing, editing, and navigating in source code are core tasks in
            application development. Therefore, the code editor is one of the
            key components of \QC. You can use the code editor in the
            \uicontrol Edit mode.

        \li \l{Finding}

            Use the incremental and advanced search to search from currently
            open projects or files on the file system or use the locator to
            browse through projects, files, classes, functions, documentation
            and file systems.

        \li \l{Refactoring}

            Code refactoring is the process of improving and simplifying code
            without modifying the existing functionality of an application. You
            can easily find and rename symbols and apply predefined actions to
            refactor code.

        \if defined(qtcreator)
        \li \l{Beautifying Source Code}

            Beautifying code means applying indentation and style to source
            code files. You can use the Artistic Style, ClangFormat, or
            Uncrustify tool to format source files.
       \endif

        \li \l{Configuring the Editor}

            You can change the fonts, colors, highlighting, and indentation.
            \if defined(qtcreator)
            If you are used to the Vim editor, you can even run the main
            editor in a manner similar to it in the \uicontrol{FakeVim} mode.
            \endif

    \endlist

    \if defined(qtcreator)
    \section1 Related Topics

    \list

        \li \l{Using Language Servers}

            The language client provides code completion, highlighting of the
            symbol under cursor, and jumping to the symbol definition for other
            programming languages besides C++. In addition, it integrates
            diagnostics from the language server.

        \li \l{Editing MIME Types}

            \QC uses the MIME type of a file to determine which mode and editor
            to use for opening the file. If your files do not match the
            predefined MIME types, you can edit the MIME types.

        \li \l{Modeling}

            You can use the model editor to create Universal Modeling Language
            (UML) style models with structured and behavioral diagrams that
            provide different views of your system and store them in XML format.

       \li \l{Editing State Charts}

            You can use \QC to create applications that embed state machines. A
            project wizard creates \l{https://www.w3.org/TR/scxml/}
            {State Chart XML (SCXML)} files with boilerplate code that you can
            edit using an experimental SCXML editor. You can use the classes in
            the Qt SCXML module to embed state machines created from the files
            in Qt applications.

    \endlist
    \endif
*/
