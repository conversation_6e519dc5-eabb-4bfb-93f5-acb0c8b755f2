// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-editor-options-text.html
    \previouspage creator-editor-options.html
    \if defined(qtdesignstudio)
    \nextpage studio-debugging.html
    \else
    \nextpage creator-editor-fakevim.html
    \endif

    \if defined(qtdesignstudio)
    \title Specifying Code View Settings
    \else
    \title Specifying Text Editor Settings
    \endif

    Set the font preferences and apply color schemes for syntax highlighting,
    diff editor, and code analysis results in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol {Font & Colors}.

    \image qtcreator-font-colors.png "Text editor preferences"

    \section2 Configuring Fonts

    You can select the font family and size. You can specify a zoom setting in
    percentage for viewing the text. You can also zoom in or out by pressing
    \key {Ctrl++} or \key {Ctrl+-}, or by pressing \key Ctrl and rolling
    the mouse button up or down. To disable the mouse wheel function, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol Behavior and deselect the
    \uicontrol {Enable scroll wheel zooming} check box.

    To improve the readability of text in the editor, adjust the line spacing in
    the \uicontrol {Line spacing} field.

    Antialiasing is used by default to make text look smoother and more readable
    on the screen. Deselect the \uicontrol Antialias check box to turn off
    antialiasing.

    \section2 Defining Color Schemes

    You can select one of the predefined color schemes or create customized
    color schemes.

    To create a color scheme:

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol {Text Editor} > \uicontrol {Fonts & Color} >
            \uicontrol Copy.

        \li Enter a name for the color scheme and click \uicontrol OK.

        \li In the \uicontrol Foreground field, specify the color of the selected
            code element or message.

        \li In the \uicontrol Background field, select the background
            color for the code element or message.

        The background of the \uicontrol Text element determines the background
        of the code editor.

        \li In \uicontrol Font, select \uicontrol Bold or \uicontrol Italic to
            format the text of the selected code element or message by making it
            bold or italic.

        \li In \uicontrol Underline, select the color and style to use for
            underlining code elements or messages.

    \endlist

    When you copy code from \QC, it is copied in both plain text and HTML
    format. The latter makes sure that syntax highlighting is preserved when
    pasting to a rich-text editor.

    \section2 Exporting and Importing Color Schemes

    To share color schemes with others, export and import them as XML files.
    To export a color scheme, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Text Editor} > \uicontrol {Fonts & Color} > \uicontrol Export,
    and then select the filename and location for the XML file.

    To import a color scheme, select \uicontrol Import, and then select the XML
    file to import. In the \uicontrol {Import Color Scheme} dialog, enter a name
    for the color scheme in \uicontrol {Color scheme name}:

    \image qtcreator-import-color-scheme.png "Import Color Scheme dialog"

    When you select \uicontrol OK, the color scheme is added to the list of
    color schemes in the \uicontrol {Fonts & Color} tab.

    \section2 File Encoding

    To define the default file encoding, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol Behavior, and
    then select a suitable option in \uicontrol {Default encoding}.

    \image qtcreator-options-texteditor-behavior-file-encodings.png "File encoding preferences"

    Qt 5 and Qt 6 require UTF-8 encoded source files, and therefore the default
    encoding is set to \uicontrol UTF-8.
    Detecting the correct encoding is tricky, so \QC will not try to do so.
    Instead, it displays the following error message when you try to edit a file
    that is not UTF-8 encoded: \uicontrol {Error: Could not decode "filename" with
    "UTF-8"-encoding. Editing not possible.}

    To resolve the issue, use a file conversion tool to convert the file
    encoding to UTF-8 when developing Qt 5 applications. Otherwise, conversion
    of string constants to QString might not work as expected.

    If you develop only Qt 4 applications or other than Qt applications, you
    can set other encoding options as the default encoding. Select the
    \uicontrol System option to use the file encoding used by your system.

*/
