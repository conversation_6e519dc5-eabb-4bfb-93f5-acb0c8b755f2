// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \example Loginui2
    \ingroup gstutorials
    \previouspage {Log In UI - Components}
    \nextpage {Log In UI - States}

    \title Log In UI - Positioning
    \brief Illustrates how to position UI components on pages using anchors and
    positioners.

    \image loginui1.jpg "Log In UI"

    \e{Log In UI - Positioning} is the second in a series of tutorials that build
    on each other to illustrate how to use \QDS to create a simple UI with
    some basic UI components, such as pages, buttons, and entry fields. The
    second tutorial in the series describes how to position the UI components
    on pages to create a scalable UI.

    To ensure that the layout is responsive and all the UI components stay in
    their proper places when you resize the UI on the desktop or on devices
    with different screen sizes, you will use anchors and positioners.

    The starting point for this tutorial is the completed
    \l{Log In UI - Components} project. You can download the project from
    \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/tutorial%20projects/Loginui1}{here}.

    Additionally, you can download the completed project of this tutorial from
    \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/tutorial%20projects/Loginui2}{here}.

    The \e {Learn More} sections provide additional information about the
    task at hand.

    \section1 Anchoring UI Components

    First, you will \l {Setting Anchors and Margins}{anchor} the
    static page elements, background image (\e adventurePage), logo
    (\e qt_logo_green_128x128px), and tag line (\e tagLine), to the page.

    When you created the project using the project wizard template
    in \l {Log In UI - Components}, the wizard template anchored \e tagLine to
    the vertical and horizontal center of the page. Therefore, you will only
    need to replace the vertical anchor of \e tagLine with a top anchor and
    adjust the margins to align it with \e qt_logo_green_128x128px on the page.

    To preview the changes that you make to the UI while you make
    them, select the \inlineimage icons/live_preview.png
    (\uicontrol {Show Live Preview}) button on the \l {2D} view
    toolbar or press \key {Alt+P}.

    To anchor component instances on a page:

    \list 1
        \li Open \e {Screen01.ui.qml} for editing in the
            \uicontrol {2D} view.
        \li Select \e adventurePage in the \l Navigator view to display its
            properties in the \l Properties view.
        \li In \uicontrol Properties > \uicontrol Layout, select
            the \inlineimage icons/anchor-fill.png
            anchor button to anchor \e adventurePage to its
            parent in the \uicontrol Target field. This attaches the background
            image to the rectangle on all sides.
            Note: Selecting the anchor button should automatically select the
            four buttons on the left side of it. If it doesn't, refresh the
            \uicontrol{2D} view.
            \image loginui2-layout.png "Layout properties"
        \li Select \e qt_logo_green_128x128px in \l Navigator.
        \li In \uicontrol Properties > \uicontrol Layout, select the
            \inlineimage icons/anchor-top.png
            and \inlineimage icons/anchor-center-horizontal.png
            anchor buttons to anchor \e qt_logo_green_128x128px to the top of
            its parent in the \uicontrol Target field with a 40-pixel margin
            and to center it horizontally. This attaches the logo to the
            rectangle at the top, while keeping its horizontal center aligned
            with that of the rectangle.
        \li Select \e tagLine in \uicontrol Navigator.
        \li In \uicontrol Properties > \uicontrol Layout,
            select the \inlineimage icons/anchor-top.png
            button and then select \e qt_logo_green_128x128px
            as \uicontrol Target to anchor \e tagLine with a 40-pixel margin.
            This attaches the top of the tag line to the
            bottom of the logo, while keeping its horizontal center aligned
            with that of the rectangle.
            \image loginui2-layout-text.png "Text Layout properties"
            \note You can anchor component instances to their parent and sibling
            components. If a component instance is not listed in the
            \uicontrol Target field, check that the component instance is
            located in the correct place in the component hierarchy in
            \uicontrol Navigator. For more information,
            see \l{Arranging Components}.
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    Your page now should look something like this in the \uicontrol Design mode
    and live preview:

    \image loginui2-loginpage.jpg "Login page in the Design mode and live preview"

    \section1 Using Column Positioners

    You will now position the entry fields and buttons in columns
    to learn another method of \l{Using Positioners}{positioning components}.
    Then, you will anchor the columns to the page to enable their positions
    to change when the screen size changes.

    To position the fields and buttons as columns:

    \list 1
        \li Select \e username and \e password in \uicontrol Navigator
            (press and hold the \key Shift or \key Ctrl key for
            \l {Multiselection}{multiple selection}), and right-click
            either of them to open a context menu.
        \li Select \uicontrol Position > \uicontrol {Position in Column}
            to position the fields on top of each other in the
            \uicontrol {2D} view.
        \li Select the column in \uicontrol Navigator and change its ID
            to \e fields in \uicontrol Properties.
        \li In \uicontrol Column > \uicontrol Spacing, set the spacing between
            the fields to 20 pixels.
            \image loginui2-column-properties.png "Column properties"
        \li Select \e login and \e createAccount, and then select
            \uicontrol Position > \uicontrol {Position in Column} to position
            them in a column.
        \li Select the button column, change its ID to \e buttons, and
            set the spacing between the buttons to 20 pixels, as above.
    \endlist

    You will now anchor the field and button columns to the page:

    \list 1
        \li Select \e fields in \uicontrol Navigator.
        \li In \uicontrol Properties > \uicontrol Layout, select the
            \inlineimage icons/anchor-top.png
            button to anchor the top of the fields column to
            the bottom of \e tagLine with a 170-pixel margin.
        \li Select the \inlineimage icons/anchor-center-horizontal.png
            button to anchor the column horizontally to its parent.
        \li Select \e buttons in \uicontrol Navigator.
        \li In \uicontrol Properties > \uicontrol Layout, select the
            \inlineimage icons/anchor-bottom.png
            button to anchor the button column to the bottom of its parent
            with a 50-pixel margin.
        \li Select the \inlineimage icons/anchor-center-horizontal.png
            button to anchor the column horizontally to its parent.
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    The second iteration of your UI is now ready and should look something like
    this in the \uicontrol Design mode and live preview:

    \image loginui2-loginpage-ready.jpg "Login page in the Design mode and live preview"

    \section1 Learn More
    \section2 Anchors
    In an anchor-based layout, each component instance can be thought of as
    having a set of invisible \e anchor lines: top, bottom, left, right, fill,
    horizontal center, vertical center, and baseline.

    Anchors enable placing a component instance either adjacent to or inside of
    another component instance, by attaching one or more of the instance's
    anchor lines to the anchor lines of the other component instance. If a
    component instance changes, the instances that are anchored to it will
    adjust automatically to maintain the anchoring.

    For more information, see \l{Positioning with Anchors}.
    \section2 Positioners

    For many use cases, the best positioner to use is a simple grid, row, or
    column, and \QDS provides components that will position children in these
    formations in the most efficient manner possible. For more information
    about using preset positioners, see \l {Using Positioners}.

    For more complicated UI designs, you can use components from the
    \l {Using Layouts}{Qt Quick Layouts module}.

    \section1 Next Steps

    To learn how to add a second page and move to it from the main page, see
    the next tutorial in the series, \l {Log In UI - States}.
*/

