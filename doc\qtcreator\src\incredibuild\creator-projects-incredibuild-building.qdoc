// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only
/*!
    \previouspage creator-build-settings-meson.html
    \page creator-build-settings-incredibuild.html
    \nextpage creator-build-settings-conan.html

    \title IncrediBuild Build Configuration

    You can specify build steps and clean steps for IncrediBuild.

    For more information about configuring IncrediBuild, see
    \l{Setting Up IncrediBuild}.

    \section1 IncrediBuild Build Steps

    To use IncrediBuild, select \uicontrol {Add Build Step} >
    \uicontrol {IncrediBuild for Linux} or
    \uicontrol {IncrediBuild for Windows}.

    \image qtcreator-incredibuild-build-steps-general.png

    IncrediBuild automatically detects the build step by iterating over the
    build steps you already defined. The initial build step settings will
    be part of your IncrediBuild build step, so your usual build tool will
    still be used, but with the added benefit of IncrediBuild's build
    acceleration and graphical Build Monitor.

    In the \uicontrol {Target and configuration} group, specify the command
    helper and arguments that will be used to construct the build command.

    The build errors and warnings are parsed and displayed in \l Issues.

    Select the \uicontrol {Keep original jobs number} check box to stop
    IncrediBuild from overriding the \c {-j} command line switch, which
    controls the number of processes that the build tools executed by
    \QC run in parallel. The default value set by IncrediBuild is 200.

    The distribution control settings to specify depend on whether you are using
    Linux or Windows.

    \section2 Distribution Control Settings on Linux

    \image qtcreator-incredibuild-build-steps-linux.png

    You can specify the following settings for Linux builds:

    \list
        \li \uicontrol {Nice value} is a numeric value between -20 and 19
        \li \uicontrol {Force remote} forces \c allow_remote tasks to
            remote Helpers.
        \li \uicontrol {Alternate tasks preference}
    \endlist

    \section2 Distribution Control Settings on Windows

    \image qtcreator-incredibuild-build-steps-windows.png

    You can specify the following settings for Windows builds:

    \list
        \li \uicontrol {Profile.xml} defines how Automatic Interception
            Interface handles processes in a distributed job. It is not
            necessary for Visual Studio or Make and Build tools builds, but can
            be used to provide configuration options if those builds use
            additional processes that are not included in those packages. It is
            required to configure distributable processes in Dev Tools builds.
        \li \uicontrol {Avoid local task execution} frees up resources on the
            initiator machine. This might be beneficial for distribution if the
            initiator turns into a bottleneck for the build because of high
            CPU usage.
        \li \uicontrol {Maximum CPUs to utilize in the build} specifies the
            maximum amount of remote cores to use in the build. Overrides the
            corresponding global setting.
        \li \uicontrol {Newest allowed helper machine OS} and
            \uicontrol {Oldest allowed helper machine OS} specify the newest and
            oldest operating system installed on a Helper machine to be allowed
            to participate as a Helper in the build.
        \li \uicontrol {Build title} specifies a custom header line which will
            be displayed in the beginning of the build output text. This title
            will also be used for the Build History and Build Monitor displays.
        \li \uicontrol {Save IncrediBuild monitor file} writes a copy of the
            build progress (\c{.ib_mon}) file to the specified location.
            If only a folder name is given, IncrediBuild generates a GUID for
            the file name. A message containing the location of the saved
            \c{.ib_mon} file is added to the end of the build output.
        \li \uicontrol {Suppress STDOUT} does not write anything to the standard
             output.
        \li \uicontrol {Output Log file} writes build output to a file.
        \li \uicontrol {Show Commands in output} shows the command-line used by
            IncrediBuild to build the file.
        \li \uicontrol {Show Agents in output} shows the Agent used to build
            each file.
        \li \uicontrol {Show Time in output} shows the start and finish time for
             each file built.
        \li \uicontrol {Hide IncrediBuild Header in output} suppresses the
            IncrediBuild header in the build output.
        \li \uicontrol {Internal IncrediBuild logging level} overrides the
            internal Incredibuild logging level for this build. Does not affect
            output or any user accessible logging. Used mainly to troubleshoot
            issues with the help of IncrediBuild support.
        \li \uicontrol {Set an Environment Variable} sets or overrides
            environment variables for the context of the build.
        \li \uicontrol {Stop on errors} stops the execution as soon as an error
            is encountered. This is the default behavior in Visual Studio
            builds, but not for Make and Build tools or Dev Tools builds.
        \li \uicontrol {Additional Arguments} are concatenated to the final
            build console command line.
        \li \uicontrol {Open Build Monitor} opens an IncrediBuild Build Monitor
            that graphically displays the build's progress once the build
            starts.
    \endlist

    \section1 IncrediBuild Clean Steps

    When building with IncrediBuild, you can add arguments and targets for the
    clean command in \uicontrol {Clean Steps}. For more information, see
    \l{Clean Steps}.

    For more information about the settings, see \l{IncrediBuild Build Steps}.

    The build errors and warnings are parsed and displayed in \l Issues.
*/
