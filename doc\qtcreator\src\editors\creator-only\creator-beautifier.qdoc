// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-editor-quick-fixes.html
    \page creator-beautifier.html
    \nextpage creator-editor-options.html

    \title Beautifying Source Code

    You can use the experimental Beautifier plugin to format your source code
    by using the following external tools:

    \list

        \li \l{http://astyle.sourceforge.net}{Artistic Style}

        \li \l{http://clang.llvm.org/docs/ClangFormat.html}{ClangFormat}

        \li \l{http://uncrustify.sourceforge.net}{Uncrustify}

    \endlist

    The Beautifier plugin parses the source code into component structures, such
    as assignment statements, if blocks, loops, and so on, and formats them as
    specified in the Beautifier preferences. You can use a predefined style or
    define your own style.

    To use the Beautifier plugin:

    \list 1

        \li Download and install the tool to use for formatting source code:

            \list
                \li \l{http://sourceforge.net/projects/astyle/files/astyle}
                    {Artistic Style}
                \li \l{http://llvm.org/releases/download.html}{ClangFormat}
                \li \l{http://sourceforge.net/projects/uncrustify/files/uncrustify}
                    {Uncrustify}
            \endlist

            You might have to build the tools from sources for some platforms.

        \li Select \uicontrol Help > \uicontrol {About Plugins} > \uicontrol {C++} >
            \uicontrol Beautifier to enable the plugin.

        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.

        \li Select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol Beautifier to specify settings for beautifying files.

        \li Select the \uicontrol {Enable auto format on file save} check box to
            automatically beautify files when you save them using the tool you
            select in the \uicontrol Tool field.

            \image qt-creator-beautifier-options-general.png "General Beautifier preferences"

        \li In the \uicontrol {Restrict to MIME types} field, define the MIME
            types of the files to beautify, separated by semicolons. Leave the
            field empty to apply the tool on all files.

            This setting is applied only when automatically beautifying files on
            save. To restrict the MIME types when selecting the menu item to
            format the currently open file, specify this option in the
            tool-specific tab.

        \li Select the \uicontrol {Restrict to files contained in the current
            project} check box to only beautify files that belong to the
            current project.

        \li Select \uicontrol {Artistic Style}, \uicontrol {Clang Format}, or
            \uicontrol Uncrustify to specify settings for the tool you want to
            use.

            \image beautifier_options.png "Uncrustify Beautifier preferences"

        \li In the \uicontrol Configuration group, specify the path to
            the tool executable in the \uicontrol {Artistic Style command},
            \uicontrol {Clang Format command}, or
            \uicontrol {Uncrustify command} field.

        \li In the \uicontrol {Restrict to MIME types} field, define the MIME
            types of the files to beautify. This setting is applied when you
            select the menu item to format the currently open file.

        \li In the \uicontrol Options group, select the configuration file that
            defines the style to use in the source files. If you select several
            options, they are applied from top down. The available options
            depend on the tool.

            \list

                \li Select \uicontrol {Use file *.astylerc defined in project files}
                    or \uicontrol {Use file uncrustify.cfg defined in project files},
                    to use the configuration file \l{Specifying Project Contents}
                    {defined in the project file} as the configuration file
                    for the selected tool.

                \li Select \uicontrol {Use specific config file} or
                    \uicontrol {Use file specific uncrustify.cfg} to
                    use the specified file as the configuration file for the
                    selected tool.

                \li Select \uicontrol {Use file uncrustify.cfg in <$HOME>} or
                    \uicontrol {Use file .astylerc or acstylerc in <$HOME>} to
                    use the specified file in the user's home directory as the
                    configuration file for the selected tool.

                \li For Clang Format, you can use a predefined style, by
                    selecting the \uicontrol {Use predefined style} radio
                    button, and then selecting the style to use from the list of
                    available styles.

                    \image qtcreator-beautifier-options-clangformat.png "Clang Format Beautifier preferences"

                    Select \uicontrol File to load the style configuration from
                    the \c .clang-format or \c _clang-format file located in the
                    same directory as the source file or in one of its parent
                    directories.

                    To specify a fallback style to use if the style configuration
                    file is not available, use the \uicontrol {Fallback style}
                    combo box. Select \uicontrol Default to use the default style.
                    Select \uicontrol None to skip formatting.

                \li Select the \uicontrol {Use customized style} option, and
                    then \uicontrol Add to define your own style.

                    Define code formatting in the
                    \uicontrol {Add Configuration} dialog. It provides syntax
                    highlighting, auto-completion, and context-sensitive help.
                    For these features, you must have the tool installed.

                   \image beautifier_editor.png "Add Configuration dialog"

            \endlist

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Beautifier
            > \uicontrol {Artistic Style}, \uicontrol ClangFormat, or
            \uicontrol Uncrustify > \uicontrol {Format Current File} to format
            the currently open file.

            You can \l{Keyboard Shortcuts}{create keyboard shortcuts} for the
            functions.

    \endlist

    In addition,
    ClangFormat provides the \uicontrol {Format at Cursor} command. If you
    select it when no text is selected, the syntactic entity under the cursor
    is formatted. The \uicontrol {Disable Formatting for Selected Text} command
    wraps selected lines within \c {// clang-format off} and
    \c {// clang-format on}.

    Uncrustify provides the \uicontrol {Format Selected Text} command. If you
    select it when no text is selected, the whole file is formatted by default.
    To disable this behavior, deselect the
    \uicontrol {Format entire file if no text was selected} check box.
*/
