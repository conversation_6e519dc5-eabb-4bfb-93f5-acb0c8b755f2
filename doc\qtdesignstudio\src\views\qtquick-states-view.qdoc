// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-states-view.html
    \previouspage qtquick-connection-view.html
    \nextpage studio-translations.html
    \sa {Working with States}

    \title States

    The \uicontrol States view displays the different
    \l{Working with States}{states} of a UI.

    \image qmldesigner-transitions.png "States view"

    To open the \uicontrol States view, select \uicontrol View >
    \uicontrol Views > \uicontrol States.

    Initially, \uicontrol States displays a \e {base state} that shows the
    selected \l{glossary-component}{component} in its initial state. To add
    states, select \inlineimage icons/plus.png
    in the \uicontrol States view.

    For more information, watch the following video:

    \youtube FzmLuRHQXaw

*/
