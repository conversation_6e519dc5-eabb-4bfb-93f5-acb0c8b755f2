version: 2
accept_configuration:
  condition: or
  conditions:
    - condition: property
      property: features
      contains_value: LicenseCheck
    - condition: and
      conditions:
        - condition: property
          property: host.os
          equals_value: Windows
        - condition: property
          property: host
          equals_property: target
        - condition: property
          property: target.compiler
          equals_value: MSVC2019
    - condition: and
      conditions:
        - condition: property
          property: host.os
          equals_value: Linux
    - condition: and
      conditions:
        - condition: property
          property: host.os
          equals_value: MacOS

machine_type:
  Build:
    cores: 8
  Test:
    cores: 8

run_license_check: &run_license_check
     type: Group
     enable_if:
       condition: property
       property: features
       contains_value: LicenseCheck
     instructions:
       - type: EnvironmentVariable
         variableName: QT_MODULE_TO_TEST
         variableValue: "qt-creator/qt-creator"
       - type: ChangeDirectory
         directory: "{{.AgentWorkingDir}}"
       - type: ExecuteCommand
         command: echo " should run perl qt/qtqa-latest/tests/prebuild/license/tst_licenses.pl"
         maxTimeInSeconds: 7200
         maxTimeBetweenOutput: 120
         userMessageOnFailure: >
              Failed ....

common_environment: &common_environment
  type: Group
  instructions:
    - !include "{{qt-creator/qt-creator}}/common_environment.yaml"

make_instructions: &make_instructions
  type: Group
  instructions:
    - !include "{{qt-creator/qt-creator}}/provision.yaml"
    - !include "{{qt-creator/qt-creator}}/build.yaml"

test_instructions: &test_instructions
  type: Group
  instructions:
    - !include "{{qt-creator/qt-creator}}/provision.yaml"
    - !include "{{qt-creator/qt-creator}}/test.yaml"

instructions:
  Build:
    - *common_environment
    - *make_instructions

  Test:
    - *common_environment
    - *test_instructions

  LicenseCheck:
    - *run_license_check
