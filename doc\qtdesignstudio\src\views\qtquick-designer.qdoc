// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-using-qt-quick-designer.html
    \if defined(qtdesignstudio)
    \previouspage creator-modes.html
    \nextpage qtquick-form-editor.html
    \else
    \previouspage quick-projects.html
    \nextpage quick-uis.html
    \endif

    \title Design Views

    \QDS contains views for designing UIs. To open them, select
    \uicontrol View > \uicontrol Views. The following images present the
    views that you are likely to use most often while designing UIs.

    \image studio-design-mode.png "Design views"
    \image studio-design-mode-states-timeline.png "The States and Timeline views"

    You can move the views anywhere on the screen and save them as
    \e workspaces, as instructed in \l {Managing Workspaces}.

    To learn more about using the design views, see the following video:

    \youtube RfEYO-5Mw6s

    \section1 Summary of Design Views

    \table
    \header
        \li View
        \li Purpose
        \li Read More
    \row
        \li \l {2D}
        \li Provides a working area for designing 2D UIs.
            When you are editing 3D scenes, the \uicontrol {2D} view is
            used as a canvas for the 3D scene projected by the camera.
        \li \l {2D}
    \row
        \li \l {3D}
        \li Provides an editor for files you created using 3D graphics
            applications and stored in one of the supported formats.
        \li \l {3D}
   \row
        \li \l Components
        \li Contains preset components and your own components, that you can use
            to design you application.
        \li \l{Using Components}
   \row
        \li \l Assets
        \li Contains assets such as images and fonts that you can use in your
            application.
        \li \l Assets
    \row
        \li \l Navigator
        \li Displays the composition of the current component file as
            a tree structure. A component file can contain references
            to other components and assets.
        \li \l Navigator
    \row
        \li \l Properties
        \li Enables you to modify the properties of the selected component.
        \li \l {Specifying Component Properties}
    \row
        \li \l{Connections}
        \li Enables you to add functionality to the UI by creating
            connections between components, signals, and component properties.
        \li \l{Working with Connections}
    \row
        \li \l States
        \li Displays the different states that can be applied to a component.
            Typically, states describe UI configurations, such as the
            visibility and behavior of components and the available user
            actions.
        \li \l{Working with States}
    \row
        \li \l{Transitions}
        \li Enables you to make movement between states smooth by animating
            the changes between states.
        \li \l{Animating Transitions Between States}
    \row
        \li \l Translations
        \li Provides functionality to add multi-language support to your
        project.
        \li \l{Translations}
    \row
        \li \l Timeline
        \li Provides a timeline and keyframe based editor for animating
            the properties of components.
        \li \l{Creating Timeline Animations}
    \row
        \li \l{Curves}
        \li Enables you to view and modify the whole animation curve by
            inserting keyframes to the curve and dragging them and the point
            handlers to modify the curve.
        \li \l {Editing Animation Curves}
    \row
        \li \l{Code}
        \li Provides a code editor for viewing and modifying the code
            generated by the visual editors.
        \li \l {Working in Edit Mode}
    \row
        \li \l Projects
        \li Shows a list of open projects and the files they contain.
        \li \l Projects
     \row
        \li \l{File System}
        \li Shows all files in the currently selected directory.
        \li \l{File System}
    \row
        \li \l{Open Documents}
        \li Shows currently open files.
        \li \l{Open Documents}
    \endtable

    \section1 Summary of Main Toolbar Actions

    The top level toolbar in the \uicontrol  Design mode contains shortcuts to
    widely used actions.

    \table
    \header
        \li Button/Field
        \li Action
        \li Keyboard Shortcut
        \li Read More
    \row
        \li \inlineimage icons/prev.png
        \li \uicontrol {Go Back}: moves a step backwards in your location history.
            That is, returns the focus to the last location in the last file it
            was on.
        \li \key Alt+< (\key Opt+Cmd+< on \macos)
        \li \l{Navigating Between Open Files and Symbols}
    \row
        \li \inlineimage icons/next.png
        \li \uicontrol {Go Forward}: moves a step forward in your location history.
        \li \key Alt+> (\key Opt+Cmd+> on \macos)
        \li \l{Navigating Between Open Files and Symbols}
    \row
        \li \inlineimage icons/unlocked.png
        \li File is writable: the currently open file can be modified and saved.
        \li
        \li \l{Open Documents}
    \row
        \li File type icon
        \li Indicates the type of the currently open file. Design views cannot
            be split, so the icon cannot be dragged, contrary to the tooltip.
        \li
        \li \l{Open Documents}
    \row
        \li Currently open file
        \li Displays the location and filename of the currently open file. You
            can select another file in the list of open files to view it in
            the \uicontrol {2D} and \uicontrol Navigator views.
        \li
        \li \l{Open Documents}
    \row
        \li \inlineimage icons/close.png
        \li \uicontrol {Close Document}: closes the current file.
        \li \key Ctrl+W (\key Cmd+W on \macos)
        \li
    \row
        \li \inlineimage icons/live_preview.png
        \li \uicontrol {Show Live Preview} shows a preview of the current file
            or the entire UI. The changes you make to the UI are instantly
            visible to you in the preview.
        \li \key Alt+P (\key Opt+P on \macos)
        \li \l{Validating with Target Hardware}
    \row
        \li Preview size
        \li Displays the size of the preview dialog as a percentage. You can
            select another percentage in the list to view the UI in different
            sizes.
        \li
        \li \l{Previewing on Desktop}
    \row
        \li FPS
        \li Displays the frames-per-second (FPS) refresh rate of previewed
            animations.
        \li
        \li \l{Previewing on Desktop}
    \row
        \li Preview language
        \li Displays the language used for a localized application during
            preview. You can select another language in the list of languages
            that the application has been localized to.
        \li
        \li
    \row
        \li \inlineimage icons/qtcreator-reset-position-icon.png
        \li Returns a component to its \e {implicit position} after
            being moved.
        \li \key Ctrl+D (\key Cmd+D on \macos)
        \li \l{Resetting Component Position and Size}
    \row
        \li \inlineimage icons/qtcreator-reset-size-icon.png
        \li Returns a component to its implicit size after it was scaled.
        \li \key Shift+S
        \li \l{Resetting Component Position and Size}
    \row
        \li \inlineimage icons/anchor-fill.png
        \li Fills the selected component to its parent component.
        \li \key Shift+F
        \li \l{Setting Anchors and Margins}
    \row
        \li \inlineimage icons/qtcreator-anchors-reset-icon.png
        \li Resets anchors to their saved state for the selected component.
        \li \key Ctrl+Shift+R (\key Shift+Cmd+R on \macos)
        \li \l{Setting Anchors and Margins}
    \row
        \li \inlineimage icons/copy-formatting.png
        \li Copies property values from the selected component.
        \li
        \li \l{Copying and Pasting Formatting}
    \row
        \li \inlineimage icons/paste-formatting.png
        \li Applies copied property values to one or several selected
            components.
        \li
        \li \l{Copying and Pasting Formatting}
    \row
        \li \inlineimage row.png
        \li Uses a \uicontrol Row component to lay out the selected components.
        \li \key Ctrl+U (\key Cmd+U on \macos)
        \li \l{Using Layouts}
    \row
        \li \inlineimage column.png
        \li Uses a \uicontrol Column component to lay out the selected
            components.
        \li \key Ctrl+L (\key Cmd+L on \macos)
        \li \l{Using Layouts}
    \row
        \li \inlineimage grid.png
        \li Uses a \uicontrol Grid component to lay out the selected
            components.
        \li \key Shift+G
        \li \l{Using Layouts}
    \if defined(qtdesignstudio)
    \row
        \li \inlineimage icons/edit.png
        \li \uicontrol {Show Event List}: opens a dialog for viewing and
            creating an event list for an application flow.
        \li \key Alt+E (\key Opt+E on \macos)
        \li \l{Simulating Events}
    \row
        \li \inlineimage icons/assign.png
        \li \uicontrol {Assign Events to Actions}: assigns events to actions in
            an application flow.
        \li \key Alt+A  (\key Opt+A on \macos)
        \li \l{Simulating Events}
    \endif
    \row
        \li Styling
        \li Displays the UI style used for UI controls.
        \li
        \li \l{Styling Controls}
    \row
        \li Subcomponents
        \li Displays the components referred to in the current file. Select a
            component in the list to open it in the \uicontrol {2D} and
            \uicontrol Navigator views.
        \li
        \li \l{Using Components}
    \row
        \li Workspace
        \li Displays the currently selected workspace. To switch to another
            workspace, select it in the list.
        \li
        \li \l{Managing Workspaces}
    \row
        \li \inlineimage icons/annotation.png
        \li Enables you to add or edit global annotations.
        \li
        \li \l{Annotating Designs}
    \endtable
*/
