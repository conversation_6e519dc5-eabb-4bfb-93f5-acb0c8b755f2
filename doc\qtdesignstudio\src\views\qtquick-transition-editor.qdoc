// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-transition-editor.html
    \previouspage studio-translations.html
    \nextpage qtquick-timeline-view.html

    \title Transitions

    To make movement between states smooth, you can use
    \uicontrol {Transitions} to animate the changes between
    states.

    First, you need to \l{Working with States}{add states} in the \l States view
    and \l{Specifying Component Properties}{edit some properties} that can be
    animated, such as colors or numbers, in the \l Properties view. For example,
    you can animate the changes in the position of a component.

    \image qtquick-transition-editor-view.png "Transitions view"

    In \uicontrol {Transitions}, you can set the start frame, end
    frame, and duration for the transition of each property. You can also
    set an \l{Editing Easing Curves}{easing curve} for each animation and
    the maximum duration of the whole transition.

    \section1 Zooming in Transitions

    Use the slider on the toolbar to set the zooming level in
    \uicontrol {Transitions}. Select the \inlineimage icons/zoom_small.png
    and \inlineimage icons/zoom_big.png
    buttons to zoom out of or into the view.

    \section1 Summary of Transitions Toolbar Actions

    \table
    \header
        \li Button/Field
        \li Action
        \li Read More
    \row
        \li \inlineimage icons/animation.png
        \li Opens \uicontrol {Transition Settings} dialog for editing
            transition settings.
        \li \l{Specifying Transition Settings}
    \row
        \li Transition ID
        \li Displays a list of transitions that you can open in
            \uicontrol {Transitions}.
        \li \l{Animating Transitions Between States}
    \row
        \li \inlineimage icons/curve_editor.png
        \li Opens \uicontrol {Easing Curve Editor} for attaching an easing
            curve to the selected transition.
        \li \l{Editing Easing Curves}
    \row
        \li \inlineimage icons/zoom_small.png
        \li \uicontrol {Zoom Out} (\key Ctrl+-): zooms out of the view.
        \li \l{Zooming in Transitions}
    \row
        \li Slider
        \li Sets the zooming level.
        \li \l{Zooming in Transitions}
    \row
        \li \inlineimage icons/zoom_big.png
        \li \uicontrol {Zoom In} (\key Ctrl++): zooms into the view.
        \li \l{Zooming in Transitions}
    \row
        \li Maximum Duration
        \li Specifies the maximum duration of the transition.
        \li
    \endtable

    \section1 Animating Transitions Between States

    To animate transitions:

    \list 1
        \li Select \uicontrol View > \uicontrol Views >
            \uicontrol {Transition Editor}.
            \image qmldesigner-transition-editor-startup.png "Empty Transitions view"
        \li Select the \inlineimage icons/plus.png
            (\uicontrol {Add Transition}) button to add a transition. This
            works only if you have added at least one state and modified at
            least one property in it.
            \image qtquick-transition-editor-view.png "Transitions view"
        \li Move the blue bar next to the component or property name to set
            the start and end frame of the animation of the property. Pull its
            left and right edges to set the duration of the animation.
        \li To attach an \l{Editing Easing Curves}{easing curve} to the
            selected transition, select the \inlineimage icons/curve_editor.png
            (\uicontrol {Easing Curve Editor (C)}) button.
    \endlist

    \section1 Specifying Transition Settings

    To modify transition settings, select the \inlineimage icons/animation.png
    (\uicontrol {Transition Settings (S)}) button in
    \uicontrol {Transition Editor}.

    \image qtquick-transition-editor-settings.png "Transitions settings"

    To add transitions:

    \list 1
        \li Select the \inlineimage icons/plus.png
            (\uicontrol {Add Transition}) button.
        \li In the \uicontrol {Transition ID} field, enter an ID for the
            transition.
        \li In the \uicontrol From field, select the state to transition from.
        \li In the \uicontrol To field, select the state to transition to.
    \endlist

    To remove the current transition, select the \inlineimage icons/minus.png
    (\uicontrol {Remove Transition}) button.

    \if defined(qtcreator)
    For an example of animating transitions between states, see
    \l {Creating a Qt Quick Application}.
    \endif
*/
