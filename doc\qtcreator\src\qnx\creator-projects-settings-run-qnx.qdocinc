// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [run settings qnx]

    \section1 Specifying Run Settings for QNX Devices

    To run and debug an application on a QNX device, you must
    create connections from the development PC to the device. Click
    \uicontrol {Manage device configurations} to create a connection. For more
    information, see \l{Connecting QNX Devices}.

    Specifying run settings for QNX Neutrino devices is very similar to
    \l{Specifying Run Settings for Linux-Based Devices}.

//! [run settings qnx]
*/
