// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-sidebars.html
    \if defined(qtdesignstudio)
    \previouspage creator-coding-navigating.html
    \else
    \previouspage creator-modes.html
    \endif
    \nextpage creator-views.html


    \title Working with Sidebars

    In the \uicontrol Edit mode, you can use a left and right sidebar to
    organize different views into project contents. Only views that are
    relevant to the \l{Selecting Modes}{mode} you are working in are
    available in it.

    You can select views in the sidebar menu (1):

    \image qtcreator-sidebar.png

    You can change the view of the sidebars in the following ways:

    \list
        \li To toggle the left sidebar, click \inlineimage icons/leftsidebaricon.png
            (\uicontrol {Hide Left Sidebar/Show Left Sidebar}) or press
            \key Alt+0 (\key Cmd+0 on \macos). To toggle the right
            sidebar, click \inlineimage icons/rightsidebaricon.png
            (\uicontrol {Hide Right Sidebar/Show Right Sidebar}) or press
            \key Alt+Shift+0 (\key Cmd+Shift+0 on \macos).
        \li To split a sidebar, click \inlineimage icons/splitbutton_horizontal.png
            (\uicontrol {Split}). Select new content to view in the split view.
        \li To close a sidebar view, click \inlineimage icons/splitbutton_closetop.png
            (\uicontrol {Close}).
    \endlist
*/
