// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

//! [0]
\badcode
otool -L /Developer/Applications/Qt/plugins/designer/libqwt_designer_plugin.dylib
\endcode
//! [0]


//! [1]
\badcode
/Developer/Applications/Qt/plugins/designer/libqwt_designer_plugin.dylib:
	libqwt_designer_plugin.dylib (compatibility version 0.0.0, current version 0.0.0)
	libqwt.5.dylib (compatibility version 5.2.0, current version 5.2.1)
	QtDesigner.framework/Versions/4/QtDesigner (compatibility version 4.6.0, current version 4.6.2)
	QtScript.framework/Versions/4/QtScript (compatibility version 4.6.0, current version 4.6.2)
	QtXml.framework/Versions/4/QtXml (compatibility version 4.6.0, current version 4.6.2)
	QtGui.framework/Versions/4/QtGui (compatibility version 4.6.0, current version 4.6.2)
	QtCore.framework/Versions/4/QtCore (compatibility version 4.6.0, current version 4.6.2)
	/usr/lib/libstdc++.6.dylib (compatibility version 7.0.0, current version 7.9.0)
	/usr/lib/libgcc_s.1.dylib (compatibility version 1.0.0, current version 438.0.0)
	/usr/lib/libSystem.B.dylib (compatibility version 1.0.0, current version 125.0.1)
\endcode
//! [1]

//! [2]
\badcode
otool -L /usr/local/qwt-5.2.1/lib/libqwt.5.dylib
\endcode
//! [2]


//! [3]
\badcode
/usr/local/qwt-5.2.1/lib/libqwt.5.dylib:
	libqwt.5.dylib (compatibility version 5.2.0, current version 5.2.1)
	QtGui.framework/Versions/4/QtGui (compatibility version 4.6.0, current version 4.6.2)
	QtCore.framework/Versions/4/QtCore (compatibility version 4.6.0, current version 4.6.2)
	/usr/lib/libstdc++.6.dylib (compatibility version 7.0.0, current version 7.9.0)
	/usr/lib/libgcc_s.1.dylib (compatibility version 1.0.0, current version 438.0.0)
	/usr/lib/libSystem.B.dylib (compatibility version 1.0.0, current version 125.0.1)
\endcode
//! [3]


//! [4]
\badcode
sudo cp /Developer/Applications/Qt/plugins/designer/libqwt_designer_plugin.dylib \
        /Developer/Applications/Qt/Qt\ Creator.app/Contents/MacOS/designer
sudo cp -R /usr/local/qwt-5.2.1/lib/*  \
        /Developer/Applications/Qt/Qt\ Creator.app/Contents/Frameworks/
\endcode
//! [4]


//! [5]
\badcode
cd /Developer/Applications/Qt/Qt\ Creator.app/Contents/MacOS/designer
sudo install_name_tool -change
    QtCore.framework/Versions/4/QtCore \
    @executable_path/../Frameworks/libQtCore.4.dylib \
    libqwt_designer_plugin.dylib
sudo install_name_tool -change QtGui.framework/Versions/4/QtGui \
    @executable_path/../Frameworks/libQtGui.4.dylib \
    libqwt_designer_plugin.dylib
sudo install_name_tool -change QtXml.framework/Versions/4/QtXml \
    @executable_path/../Frameworks/libQtXml.4.dylib \
    libqwt_designer_plugin.dylib
sudo install_name_tool -change QtScript.framework/Versions/4/QtScript \
    @executable_path/../Frameworks/libQtScript.4.dylib \
    libqwt_designer_plugin.dylib
sudo install_name_tool -change QtDesigner.framework/Versions/4/QtDesigner \
    @executable_path/../Frameworks/libQtDesigner.4.dylib \
    libqwt_designer_plugin.dylib
sudo install_name_tool -change libqwt.5.dylib \
    @executable_path/../Frameworks/libqwt.5.dylib \
    libqwt_designer_plugin.dylib

cd /Developer/Applications/Qt/Qt\ Creator.app/Contents/Frameworks
sudo install_name_tool -change \
    QtCore.framework/Versions/4/QtCore \
    @executable_path/../Frameworks/libQtCore.4.dylib \
    libqwt.5.2.1.dylib
sudo install_name_tool -change \
    QtGui.framework/Versions/4/QtGui \
    @executable_path/../Frameworks/libQtGui.4.dylib \
    libqwt.5.2.1.dylib
\endcode
//! [5]

