// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

    /*!
    \page creator-developing-android.html
    \previouspage creator-connecting-mobile.html
    \nextpage creator-developing-baremetal.html

    \title Connecting Android Devices

    You can connect Android devices to the development PC using USB cables
    to build, run, debug, and analyze applications from \QC. Devices with
    Android version 4.1 (API level 16) or later are supported when developing
    with Qt 5 and devices with Android version 6.0 (API level 23) when
    developing with Qt 6.

    To develop for Android, you must have a tool chain for building applications
    for Android devices installed on the development PC. \QC can automatically
    download and install the tool chain and create a suitable build and run
    \l{glossary-buildandrun-kit}{kit} that contains the tool chain and the Qt
    version for Android for the device's architecture.

    Starting from Qt 5.14.0, the Qt for Android package contains all the
    architectures (ABIs) installed as one.

    To enable helpful code editing features for Java, such as code completion,
    highlighting, function tooltips, and navigating in code, add a
    \l{Java Language Server}{Java language server}.

    The Android Debug Bridge (adb) command line tool is integrated to \QC to
    enable you to deploy applications to connected Android devices, to run
    them, and to read their logs. It includes a client and server that run on
    the development host and a daemon that runs on the emulator or device.

    \section1 Requirements

    To use \QC to develop Qt applications for Android, you need
    \l{Qt for Android} 5.2, or later, and the tool chain that \QC
    can automatically download, install, and configure for you.
    For more information, see \l{Manually Installing the Prerequisites}.

    \section1 Specifying Android Device Settings

    \QC offers to automatically install all the necessary packages and tools and
    to set up your development environment by creating debuggers, tool chains,
    and kits. You can use \QC to:

    \list
        \li Download and extract the Android SDK Command-line Tools.
        \li Install or update the essential packages such as NDKs,
            build tools, and platform tools.
    \endlist

    To set up the development environment for Android:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Devices >
            \uicontrol Android on Windows and Linux or \uicontrol {\QC} >
            \uicontrol Preferences > \uicontrol Devices > \uicontrol Android on
            \macos.
            \image qtcreator-options-android-main.png "Android preferences"
        \li In the \uicontrol {JDK location} field, set the path to the JDK.
            \QC checks the JDK installation and reports errors.

            By default, \QC tries to find a supported \l{AdoptOpenJDK} or
            \l{OpenJDK} installation. If none is found, you must set the path
            manually. If you don't have a supported JDK installed, select
            \inlineimage icons/online.png
            to open the JDK download web page in the default browser.

            \note We recommended using a 64-bit JDK because the 32-bit one
            might cause issues with \c cmdline-tools, and some packages might
            not be listed.
        \li In the \uicontrol {Android SDK location} field, set the path to the
            folder where you want the \l{Android SDK Command-line Tools} to be
            installed.
        \li Select \uicontrol {Set Up SDK} to automatically download and extract
            the Android SDK Command-line Tools to the selected path.

            The SDK Manager checks whether the tool chain is installed.
            If packages are missing or updates are needed, the SDK Manager
            offers to add or remove those packages. Before taking action, it
            prompts you to accept the changes it is about to make. In addition,
            it prompts you to accept Google licenses, as necessary.
        \li The installed NDK versions are listed in
            \uicontrol {Android NDK list}.
            The locked items were installed by the SDK Manager,
            and can only be modified from the \uicontrol {Android SDK Manager}
            dialog. For more information, see \l{Managing Android NDK Packages}.
        \li Select the \uicontrol {Automatically create kits for Android tool chains}
            check box to allow \QC to create the kits for you. \QC displays a
            warning if it cannot find a suitable Qt version.
        \li Optionally, in the \uicontrol {Android OpenSSL Settings} group, set
            the path to the prebuilt OpenSSL libraries.

            For Qt applications that require OpenSSL support, \QC allows to
            quickly add the \l {Android OpenSSL support} to your project.
            For more information, see \l{Adding External Libraries}.
        \li Select \uicontrol {Download OpenSSL} to download the OpenSSL
            repository to the selected path. If the automatic download fails,
            the download web page opens for manual download.
    \endlist

    \section2 Manual Setup

    \note We recommend that you use the latest Android SDK Command-Line Tools.
    Using Android SDK Tools version 25.2.5 or earlier is not supported because
    they cannot be fully integrated with \QC.

    However, if the automatic setup does not meet your needs, you can download
    and install Android SDK Command-line Tools, and then install or update the
    NDKs, tools and packages needed for development. For more information, see
    \l{Getting Started with Qt for Android}.

    \section2 Viewing Android Tool Chain Settings

    The Android SDK Command-Line Tools download URL, the essential
    packages list, and the appropriate NDK for each Qt version are defined in a JSON
    configuration file. The file is located under the user's \QC
    resource folder:

    \badcode
    # Linux and macOS
    ~/.config/QtProject/qtcreator/android/sdk_definitions.json

    # Windows
    C:\Users\<USER>\AppData\Local\QtProject\qtcreator\android\sdk_definitions.json
    \endcode

    For example, the SDK configuration file defines the NDK version 19.2.5345600
    to be used for Qt 5.12.0 to 5.12.5 and Qt 5.13.0 to 5.13.1 versions:

    \badcode
    "specific_qt_versions": [
        {
            "versions": ["5.12.[0-5]", "5.13.[0-1]"],
            "sdk_essential_packages": ["build-tools;28.0.2", "ndk;19.2.5345600"],
            "ndk_path": "ndk/19.2.5345600"
        }
    ]
    \endcode

    You can view the latest version of the configuration file that is up-to-date
    with the Android SDK and NDK changes, \l{sdk_definitions.json}, in Git.

    \section2 Managing Android NDK Packages

    To view the installed \l{Android NDK} versions, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Devices > \uicontrol Android on Windows and
    Linux or \uicontrol {\QC} > \uicontrol Preferences > \uicontrol Devices >
    \uicontrol Android on \macos.

    \image qtcreator-options-android-sdk-tools.png "Android NDK and SDK checks"

    The locked versions were installed by the SDK Manager, and can only
    be modified from the \uicontrol {Android SDK Manager} dialog.
    For more information, see \l{Managing Android SDK Packages}.

    To manually download NDKs, select \inlineimage icons/online.png
    .

    To use the selected NDK version for all Qt versions by default, select
    \uicontrol {Make Default}.

    To add custom NDK paths manually to the global list of NDKs, select
    \uicontrol Add. This creates custom tool chains and debuggers associated
    to that NDK. However, you have to manually create a kit that uses the
    custom NDK. For more information, see \l{Adding Kits}.

    \section2 Managing Android SDK Packages

    Since Android SDK Tools version 25.3.0, only a command-line tool,
    \l {sdkmanager}, is provided by Android for SDK package management.
    To make SDK management easier, \QC provides an SDK Manager for
    installing, updating, and removing SDK packages. You can still use
    sdkmanager for advanced SDK management.

    To view the installed Android SDK packages, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Devices > \uicontrol Android >
    \uicontrol {SDK Manager} on Windows and Linux or \uicontrol {\QC} >
    \uicontrol Preferences > \uicontrol Devices > \uicontrol Android >
    \uicontrol {SDK Manager} on \macos.

    \image qtcreator-android-sdk-manager.png "Android SDK Manager"

    You can show packages for the release channel you select in
    \uicontrol {Show Packages} > \uicontrol Channel. Common channel IDs include
    \uicontrol Stable, \uicontrol Beta, \uicontrol Dev, and \uicontrol Canary.
    To show and update also obsolete packages, select
    \uicontrol {Include obsolete}. To filter packages, select
    \uicontrol Available, \uicontrol Installed, or \uicontrol All.

    To update the installed Android SDK packages, select
    \uicontrol {Update Installed}. Select the packages to update, and then
    select \uicontrol Apply.

    To specify advanced sdkmanager settings, select
    \uicontrol {Advanced Options} and enter arguments in the
    \uicontrol {SDK Manager arguments} field. The available arguments are listed
    and described in \uicontrol {Available arguments}.

    \image qtcreator-android-sdk-manager-arguments.png "Android SDK Manager Arguments dialog"

    \section1 Managing Android Virtual Devices (AVD)

    The available AVDs are listed in \uicontrol Edit > \uicontrol Preferences
    > \uicontrol Devices on Windows and Linux or \uicontrol {\QC} >
    \uicontrol Preferences > \uicontrol Devices > on \macos. You can add more
    AVDs.

    \image qtcreator-android-avd-manager.png "Android device in Devices"

    You can see the status of the selected device in \uicontrol {Current state}.
    To update the status information, select \uicontrol Refresh.

    To start an AVD, select \uicontrol {Start AVD}. Usually, you don't need to
    start AVDs separately because they are automatically started when you
    select them in the \l{Building for Multiple Platforms}{kit selector} to
    \l{Deploying to Android}{deploy applications} to them.

    To remove an AVD from the list and the kit selector, select
    \uicontrol {Erase AVD}.

    To specify options for starting an AVD, select \uicontrol {AVD Arguments}.

    \image qtcreator-android-avd-arguments.png "Startup options for AVDs"

    Specify the options in \uicontrol {Emulator command-line startup options}.
    For available options, see \l{Start the emulator from the command line}.

    \note The Android Emulator has a bug that prevents it from starting on some
    systems. If an AVD does not start, you can try starting it manually by
    running the following commands:

    \badcode
    cd <ANDROID_SDK>/emulator
    ./emulator -avd <AVD_NAME>
    \endcode

    \section2 Creating a New AVD

    To create new virtual devices:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Devices >
            \uicontrol Add > \uicontrol {Android Device} on Windows and Linux
            or \uicontrol {\QC} > \uicontrol Preferences > \uicontrol Devices >
            \uicontrol Add > \uicontrol {Android Device} on \macos to open the
            \uicontrol {Create New AVD} dialog.
            \image qtcreator-android-create-avd.png "Create New AVD dialog"
        \li Set the name, definition, architecture, target API level, and
            SD card size of the device.
        \li Select \uicontrol OK to create the AVD.
    \endlist

    For more advanced options for creating a new AVD, use the command-line tool
    \l{avdmanager} or the Android Studio's native AVD Manager UI.

    \section1 Debugging on Android Devices

    Debugging is enabled in different ways on different Android devices.
    Look for \uicontrol {USB Debugging} under \uicontrol {Developer Options}. On
    some devices \uicontrol {Developer Options} is hidden and becomes visible
    only when you tap the \uicontrol {Build number} field in \uicontrol Settings
    > \uicontrol About several times. For more information, see
    \l {Configure on-device developer options}.

    Select a \l{glossary-build-config}{debug build configuration} to build
    the application for debugging.

    \note \QC cannot debug applications on Android devices if Android Studio is
    running. If the following message is displayed in \l {Application Output},
    close Android Studio and try again:

    \badcode
    Ignoring second debugger -accepting and dropping.
    \endcode
*/
