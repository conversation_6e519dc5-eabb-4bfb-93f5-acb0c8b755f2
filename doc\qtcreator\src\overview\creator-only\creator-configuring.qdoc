// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-output-panes.html
    \page creator-configuring.html
    \nextpage creator-build-example-application.html

    \title Configuring Qt Creator

    If you install \QC as part of a Qt installation, you should be able to use
    it out-of-the-box, with the default settings and configuration options.

    However, if you install the stand-alone \QC package, build \QC from sources,
    or install several Qt versions, you may need to tell \QC where to find the
    Qt versions and compilers by adding the paths to them and by creating
    \l{glossary-buildandrun-kit}{kits} that use them.

    To make \QC behave more like your favorite code editor or IDE, you can
    change the settings for keyboard shortcuts, color schemes, generic
    highlighting, code snippets, and version control systems. In addition,
    you can enable and disable \QC features by managing plugins.

    The following sections summarize the options that you have and point you to
    detailed information to help you specify any required settings and to make
    using \QC a better experience for you.

    \section1 Checking Build and Run Settings

    \QC is an integrated development environment (IDE) that you can use to
    develop Qt applications. While you can use the Qt Installer to install \QC,
    the stand-alone \QC installer never installs Qt or any Qt tools, such as
    qmake. To use \QC for Qt development, you also need to install a Qt version
    and a compiler. If you update the compiler version later, you
    can register it into \QC.

    The Qt Installer attempts to auto-detect compilers and Qt versions. If it
    succeeds, the relevant kits will automatically become
    available in \QC. If it does not, you must add the kits yourself to tell
    \QC where everything is.

    To add kits, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Kits > \uicontrol Add.

    For more information, see \l{Adding Kits}.

    Each kit consists of a set of values that define one environment, such as a
    \l{glossary-device}{device}, compiler, and Qt version. If \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Kits > \uicontrol {Qt Versions} does not
    show all the installed Qt versions, select \uicontrol {Link with Qt}.

    If \uicontrol Auto-detected still does not show the Qt version, select
    \uicontrol {Add} to add it manually.

    For more information, see \l{Adding Qt Versions}.

    Also check that \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
    \uicontrol {Compilers} shows your compiler.

    For more information, see \l{Adding Compilers}.

    You can connect devices to the development PC to run, debug, and
    analyze applications on them from \QC. You can connect the device to the
    development PC via USB. Additionally, you can connect Linux-based devices
    over a WLAN. You must also configure a connection between \QC and the
    development PC and specify the device in a kit.

    To add devices, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Devices > \uicontrol Devices > \uicontrol Add.

    For more information, see \l{Connecting Devices}.

    \section1 Changing Keyboard Shortcuts

    You can use \QC with your favorite keyboard shortcuts.

    To view and edit all keyboard shortcuts defined in \QC, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Environment >
    \uicontrol Keyboard. For more information, see \l{Keyboard Shortcuts}.

    \section1 Changing Color Schemes

    Themes enable you to customize the appearance of the \QC UI: widgets,
    colors, and icons.

    To switch themes, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Environment, and then select a theme
    in the \uicontrol Theme field.

    You can use the \QC text and code editors with your favorite color scheme
    that defines how to highlight code elements and which background color to
    use. You can select one of the predefined color schemes or create custom
    ones. The color schemes apply to highlighting C++ files, QML files, and
    generic files.

    To change the color scheme, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Text Editor} > \uicontrol {Fonts & Color}.

    For more information, see \l{Defining Color Schemes}.

    \l{https://api.kde.org/frameworks/syntax-highlighting/html/index.html}
    {KSyntaxHighlighting} provides generic highlighting. It is the syntax
    highlighting engine for Kate syntax definitions. \QC comes with most of
    the commonly used syntax files, and you can download additional files.

    To download and use highlight definition files, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol {Generic Highlighter}.

    For more information, see \l{Generic Highlighting}.

    \section1 Adding Your Own Code Snippets

    As you write code, \QC suggests properties, IDs, and code snippets to
    complete the code. It provides a list of context-sensitive suggestions to
    the statement currently under your cursor. You can add, modify, and remove
    snippets in the snippet editor.

    To open the snippet editor, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Text Editor} > \uicontrol Snippets.

    For more information, see \l{Editing Code Snippets}.

    \section1 Configuring Version Control Systems

    \QC supports several version control systems. In most cases, you do not need
    to configure the version control in any special way to make it work with
    \QC.

    However, some configuration options are available and you can set them in
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Version Control} >
    \uicontrol General.

    For more information about the supported functions, see
    \l{Using Version Control Systems}.

    \section1 Managing Plugins

    \QC comes with a set of plugins, some of which are disabled by default.
    You can enable disabled plugins if you need them and disable plugins you
    don't need.

    You can download and install additional plugins from
    \l{https://marketplace.qt.io/}{Qt Marketplace} or some
    other source, such as \l{https://github.com/}{GitHub}.

    \section2 Enabling and Disabling Plugins

    New \QC plugins are often introduced as \e {experimental plugins} to let you
    try them out before they are fully supported. Experimental plugins are
    disabled by default and you must enable them for them to become visible
    after you restart \QC. By default, all the plugins that the plugin depends
    on are also enabled.

    You can also disable plugins that you do not use, to streamline \QC.
    By default, all the plugins that depend on the plugin are also disabled.

    To enable and disable plugins, select \uicontrol Help >
    \uicontrol {About Plugins}.

    \image qtcreator-installed-plugins.png "Installed Plugins dialog"

    \section2 Installing Plugins

    Qt Marketplace contains links to \QC plugins that you can download and
    install either for free or for a price set by their publisher. You can
    browse the available plugins in the \uicontrol Marketplace tab in the
    Welcome mode.

    \note You can install only plugins that your \QC version supports.

    To install plugins:

    \list 1
        \li Select \uicontrol Help > \uicontrol {About Plugins} >
            \uicontrol {Install Plugins}.
        \li In the \uicontrol Source dialog, enter the path to the archive
             or library that contains the plugin.
            \image qtcreator-install-plugin-source.png
        \li In the \uicontrol {Install Location} dialog, select
            \uicontrol {User plugins} to make the plugin available for the
            current user in all compatible \QC instances or
            \uicontrol {\QC installation} to make the plugin available for
            all users of a particular \QC instance.
            \image qtcreator-install-plugin-location.png
        \li In the \uicontrol Summary dialog, select \uicontrol Finish to
            install the plugin.
            \image qtcreator-install-plugin-summary.png
        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.
    \endlist
*/
