// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-other.html
    \page creator-project-cmake.html
    \nextpage creator-project-qbs.html

    \title Setting Up CMake

    CMake automates the configuration of build systems. It controls the software
    compilation process by using simple
    configuration files, called \c {CMakeLists.txt} files. CMake generates
    native build configurations and workspaces that you can use in the compiler
    environment of your choice.

    You can use CMake from \QC to build applications for the desktop, as well
    as mobile and embedded devices. You can also build single files to test
    your changes.

    \QC automatically detects the CMake executable specified in the \c PATH.
    You can add paths to other CMake executables and use them in different
    build and run \l{glossary-buildandrun-kit}{kits}.

    CMake documentation is installed in Qt help file format (.qch) when you
    install CMake. It is automatically registered by \QC, and you can view it
    in the Help mode.

    \QC automatically runs CMake to refresh project information when you edit
    a \c CMakeLists.txt configuration file in a project. Project information is
    also automatically refreshed when you build the project.

    \image qtcreator-projects-view-edit.png "CMake project in Projects view"

    If \QC cannot load the CMake project, the \l Projects view shows a
    \uicontrol {<File System>} project node to avoid scanning the file
    system and load the project faster. The node shows the same files
    as the \l {File System} view. Select \uicontrol Build >
    \uicontrol {Clear CMake Configuration}, and then select \uicontrol Build
    > \uicontrol {Run CMake} to reconfigure the project.

    The \uicontrol Projects view shows the names of the subfolders where the
    source files are located. To hide the subfolder names and arrange the files
    only according to their source group, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol CMake > \uicontrol General, and then
    deselect the \uicontrol {Show subfolders inside source group folders} check
    box. The change takes effect after you select \uicontrol Build >
    \uicontrol {Run CMake}.

    \section1 Adding CMake Tools

    \QC requires CMake's \l{https://cmake.org/cmake/help/latest/manual/cmake-file-api.7.html}
    {file-based API}, and therefore you'll need CMake version 3.14, or later.

    For systems with older versions of CMake, only workarounds are available:
    \list

        \li For CMake version 3.5 or later it is possible to generate a
            \l{https://cmake.org/cmake/help/latest/variable/CMAKE_EXPORT_COMPILE_COMMANDS.html}{compilation database}
            and open that in \QC, as described in \l{Using Compilation Databases}.

        \li Create an ad-hoc project file for a qmake build using
            \c{qmake -project} and \l{Opening Projects}{open} that in \QC.
            Be aware that this is typically
            not compilable without further manual changes.

        \li Manually create an ad-hoc project file for a
            \l{Setting Up a Generic Project}{generic project} and
            open that in \QC. Be aware this is typically
            not compilable without further manual changes.

    \endlist


    To view and specify settings for CMake:

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol CMake >
            \uicontrol Tools.

            \image qtcreator-preferences-cmake-tools.png "Tools tab in CMake Preferences"

        \li The \uicontrol Name field displays a name for the CMake
            installation.

        \li The \uicontrol Path field displays the path to the CMake
            executable.

        \li The \uicontrol {Help file} field displays the path to the
            CMake help file (.qch) provided by and installed with CMake.

        \li Deselect the \uicontrol {Autorun CMake} check box if you do not want
            to automatically run CMake every time when you save changes to
            \c {CMakeLists.txt} files.

        \li Select \uicontrol Apply to save your changes.

    \endlist

    To add a path to a CMake executable that \QC does not detect automatically,
    and to specify settings for it, select \uicontrol Add. To make changes to
    automatically detected installations, select \uicontrol Clone.

    \QC uses the \e {default CMake} if it does not have enough information
    to choose the CMake to use. To set the selected CMake executable as the
    default, select \uicontrol {Make Default}.

    To remove the selected CMake executable from the list, select
    \uicontrol Remove.

    To add the CMake tool to a build and run kit, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Kits.
    The kit also specifies the CMake generator that is used for producing
    project files for \QC and the initial configuration parameters:

    \image qtcreator-kits-cmake.png

    For more information, see \l {Adding Kits}.

    \section1 Editing CMake Configuration Files

    To open a CMakeLists.txt file for editing, right-click it in the
    \uicontrol Projects view and select \uicontrol {Open With} >
    \uicontrol {CMake Editor}.

    You can also use the \c cmo filter in the \l {Searching with the Locator}
    {locator} to open the CMakeLists.txt file for the current run configuration
    in the editor. This is the same build target as when you select
    \uicontrol Build > \uicontrol {Build for Run Configuration}.

    The following features are supported:

    \list

        \li Pressing \key F2 when the cursor is on a filename to open the file

        \li Keyword completion

        \li Code completion

        \li Path completion

        \li Auto-indentation

        \li Matching parentheses and quotes

    \endlist

    Warnings and errors are displayed in \l {Issues}.

   \section1 Adding External Libraries to CMake Projects

    Through external libraries, \QC can support code completion and syntax
    highlighting as if they were part of the current project or the Qt library.

    \QC detects the external libraries using the \c {find_package()}
    macro. Some libraries come with the CMake installation. You can find those
    in the \c {Modules} directory of your CMake installation.
    For more information, see
    \l{https://cmake.org/cmake/help/latest/manual/cmake-packages.7.html}
    {cmake-packages(7)}.

    Syntax completion and highlighting work once your project successfully
    builds and links against the external library.

    \section1 Related Topics

    \list
        \li \l {Opening Projects}
        \li \l {CMake Build Configuration}
        \li \l {Specifying Run Settings}
        \li \l {Deploying to Remote Linux}
    \endlist
*/
