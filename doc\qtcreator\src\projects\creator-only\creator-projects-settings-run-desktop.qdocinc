// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [run settings desktop]

    \section1 Specifying Run Settings for Desktop Device Types

    You can specify command line arguments to be passed to the executable
    and the working directory to use. The working directory defaults to
    the directory of the build result.

    For console applications, check the \uicontrol{Run in terminal} check box.
    To specify the terminal to use on Linux and \macos, select \uicontrol Edit
    > \uicontrol Preferences > \uicontrol Environment > \uicontrol System.

    To run with special environment variables set up, select them in the
    \uicontrol {Run Environment} section. For more information, see
    \l {Selecting the Run Environment}.

    \image qtcreator-pprunsettings.png

    When building an application, \QC creates a list of directories where the
    linker will look for libraries that the application links to. By
    default, the linked libraries are made visible to the executable that
    \QC is attempting to run. Usually, you should disable this option only if it
    causes unwanted side-effects or if you use deployment steps, such as
    \c {make install}, and want to make sure that the deployed application will
    find the libraries also when it is run without \QC.

    To disable library linking for the current project, deselect the
    \uicontrol {Add build library search path to PATH} check box. To disable
    library linking for all projects, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Build & Run}, and then deselect the
    \uicontrol {Add linker library search paths to run environment} check box.

    The \uicontrol {Use debug version of frameworks (DYLD_IMAGE_SUFFIX=_debug)} option
    (only available on \macos) enables you to debug (for example, step into)
    linked frameworks, such as the Qt framework itself. You do not need this
    option for debugging your application code.

    On Linux, select the \uicontrol {Run as root user} check box to run the
    application with root user permissions.

    You can also create custom executable run configurations where you
    can set the executable to run. For more information, see
    \l{Specifying a Custom Executable to Run}.

//! [run settings desktop]
*/
