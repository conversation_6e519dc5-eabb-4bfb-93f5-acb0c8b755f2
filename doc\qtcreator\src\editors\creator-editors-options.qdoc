// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-editor-options.html
    \if defined(qtdesignstudio)
    \previouspage creator-editor-quick-fixes.html
    \else
    \previouspage creator-beautifier.html
    \endif
    \nextpage creator-editor-options-text.html

    \title Configuring the Editor

    You can configure the text editor to suit your specific needs by selecting
    \uicontrol Edit > \uicontrol Preferences > \uicontrol{Text Editor}.

    \image qtcreator-font-colors.png "Text Editor preferences"

    The settings you specify apply globally to all projects.

    To specify editor behavior for an open project, select \uicontrol Projects >
    \uicontrol Editor.

    \image qtcreator-editor-settings.png "Editor settings"

    \if defined(qtcreator)
    For more information, see \l{Specifying Editor Settings}.
    \endif

    You can also specify indentation settings separately for C++ and QML files
    either globally or for the open project. For more information, see
    \l{Indenting Text or Code}.

    You can perform the following configuration actions:

    \list
        \if defined(qtdesignstudio)
        \li Set the \l{Specifying Code View Settings}{font preferences and
            apply color schemes} for syntax highlighting in
            \uicontrol {Font & Colors}.
        \else
        \li Set the \l{Specifying Text Editor Settings}{font preferences and
            apply color schemes} for syntax highlighting in
            \uicontrol {Font & Colors}.
        \endif
        \li Specify \l{Generic Highlighting}
            {definition files for syntax highlighting} for other types of files
             than C++ or QML in \uicontrol {Generic Highlighter}.

        \li Set \l{Indenting Text or Code}{tabs, indentation, the handling of
            whitespace, and mouse operations} in \uicontrol Behavior.

        \li Set various display properties, such as
            \l{Highlighting and folding blocks}
            {highlighting and folding blocks} or text
            wrapping in \uicontrol Display.

        \li Add, modify, and remove \l{Editing Code Snippets}{code snippets} in
            \uicontrol Snippets.

        \if defined(qtcreator)
        \li View and remove \l{Using Text Editing Macros}{text editing macros}
            in \uicontrol Macros.
        \endif

        \li Configure \l{Completing Code}{code completion} in \uicontrol Completion.

    \endlist

    \if defined(qtcreator)
    \section1 Related Topics

    \list

        \li \l{Using FakeVim Mode}

            Run the main editor in a manner similar to the Vim editor in the
            FakeVim mode.

    \endlist
    \endif

*/
