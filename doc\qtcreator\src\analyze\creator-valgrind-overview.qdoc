// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-coco.html
    \page creator-valgrind-overview.html
    \nextpage creator-analyzer.html

    \title Using Valgrind Code Analysis Tools

    \QC integrates \l{http://valgrind.org/info/tools.html}{Valgrind code
    analysis tools} for detecting memory leaks and
    profiling function execution. You must download and install them separately
    to use them from \QC.

    You can run the Valgrind tools either \e locally on the development host or
    \e remotely on another host. You can use them to analyze both applications
    for which you set up a project in \QC and applications for which you do not
    have a project.

    Valgrind tools are supported locally only on Linux and \macos. However,
    according to Valgrind.org, support on \macos 10.8 and 10.9 is experimental and
    mostly broken. You can run the tools on a remote Linux machine or device
    from any development host.

    To run the Valgrind tools to analyze an application for which you have a
    project, open the project in \QC and select the kit to run the project. The
    kit specifies whether the Valgrind tools are run locally or remotely.

    For more information about analyzing applications for which you do not have
    a project, see \l{Running Valgrind Tools on External Applications}.

    To set preferences for the Valgrind tools, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Analyzer. You can override the general
    settings for each project in the \uicontrol {Run Settings} for the project.

    The following sections describe how to use the Valgrind tools:

    \list

        \li \l{Detecting Memory Leaks with Memcheck}

        \li \l{Profiling Function Execution}

    \endlist

*/
