// Copyright (C) 1993-2009 NVIDIA Corporation.
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page exporting-3d-assets.html
    \if defined(qtdesignstudio)
    \previouspage figmaqtbridge.html
    \else
    \previouspage quick-states.html
    \endif
    \nextpage exporting-from-blender.html

    \title Exporting 3D Assets

    \if defined(qtcreator)
    \table
    \row
        \li \inlineimage blender-logo.png
        \li \inlineimage maya-logo.png
    \row
        \li \l{Exporting from Blender}{Blender}
        \li \l{Exporting from Maya}{Maya}
    \endtable
    \endif

    You can import files you created using 3D graphics applications and exported
    to several widely-used formats, such as .blend, .dae, .fbx, .glb, .gltf,
    .obj, .uia, or .uip. For a list of formats supported by each \l{Qt Quick 3D}
    version, see the module documentation.

    Depending on the 3D graphics tool, you might need to install export plugins
    to be able to export files to a particular format.

    For complex 3D models, we recommend leaving the files exported from 3D
    graphics tools unmerged, so if there are performance issues, you can easily
    delete individual meshes within \QDS.

    Consider how much detail is necessary for your 3D model prior to exporting
    from 3D graphics tools. If the models will be far from camera view, optimize
    the models to the least amount of polygons necessary without compromising
    the model. For high-detailed models in close distance to the camera, use
    image maps a much as possible for detail. If the model is only viewed from
    certain angles, consider further optimization of parts not in direct camera
    view for optimal performance.

    To get the best results when exporting 3D assets and importing them to
    \QDS, follow the general guidelines in the following sections. For more
    information about using the export plugins for a particular tool, see
    \l{Exporting from Different Tools}.

    \section1 Geometry

    \QDS supports geometry exported as triangles, quads, and pentagons. For
    basic geometry, you mostly need to pay attention to pivot points and
    transformation.

    \section2 Pivot Points

    In \QDS, there is only one pivot per component. It is used as the origin
    for scaling and rotation. Adjust the position of a 3D model's pivot as
    needed.

    However, extreme edits to pivots in 3D modeling tools can cause problems
    when importing to \QDS, especially if animated. This difference is often
    manifested as a difference in the position or orientation of a component.
    You can prevent these kinds of problems by only making simple edits to
    your pivot points. Keep your pivot points to the default (world) alignment,
    don't scale them, and make sure that if you have multiple pivots (Maya)
    that they are all at the same location in space.

    \section2 Transformation

    You can import full 3D transform information including position, rotation,
    scale, and pivot. \QDS can import left and right-handed coordinate systems,
    y-up or z-up, and rotations applied in any order. The principal limitation
    in this area are pivot points. As discussed above, only simple edits to
    pivot points are supported.

    Most 3D graphics tools enable you to apply transformation to components and
    vertices. We highly recommend doing so before importing mesh data into \QDS.
    This ensures that the mesh coming into \QDS has clean transform data and no
    arbitrary transform values which can be confusing or an impediment to your
    work.

    \note After applying transformations, you may have to reposition the pivot
    point in some 3D graphics tools.

    \section1 Animations

    Animations are supported on any imported property. Position, rotation,
    scale, and pivot can all be animated. For example, a hierarchy of items,
    rotated simultaneously on arbitrary axes in arbitrary axis order can be
    imported. \QDS also supports importing bezier tangent value tweaked into
    animations.

    \section2 Time-based Animations

    In many 3D modeling tools, when you create keyframes you associate them with
    frame numbers. This is great in the film industry where frame rates are
    constant, but poses problems in applications where the frame rate may or
    may not be rock solid. If you were to specify that the logo animation will
    play for 180 frames, it might play for 3 seconds at 60 FPS, but if the speed
    drops to 30 fps, the animation will also get much slower.

    Luckily, accounting for this is relatively simple. Many 3D modeling tools
    default to a setting of 24 frames per second, so your keyframes will
    be translated at that ratio. If you want a keyframe at one second, put
    it on frame 24. For two seconds, use frame 48, and so on.

    Usually, configurable frame rates are offered, and the frame rate setting
    should be respected upon import.

    Some tools, such as Maya, start at frame 1, by default. If you have a
    keyframe at frame 1, the time for that keyframe will be 1/24, or 0.041
    seconds. Edit your Maya animation settings to start your animations at
    frame 0, or 0/24 = 0 seconds.

    In \QDS, you can specify the duration of the animation in addition to its
    start and end frame.

    \section2 Deform Animations

    \e {Deform animations}, such as lattice and bend, are not supported by \QDS.
    However, you can work around this limitation by converting deform animations
    into \e {blend shape} animations that are supported in FBX format. Before
    exporting the animations, you need to bake the actions in them into key
    frames.

    \section2 Baking Actions for Animations

    You need to bake actions to export animations that are using custom curves
    or object constraints to control the animation.

    We recommend using the export panel in 3D graphics tools to bake animations
    to the 3D graphics you are about to export. This will only bake your set
    keyframes, rather than each individual keyframe along the timeline.

    \e Actions are data-blocks containing animation data. If you are exporting
    animations, you need to bake actions.

    \section2 Animation Systems

    3D modeling tools offer highly complex and specialized animation systems.
    We recommend using the \QDS \uicontrol Timeline view whenever practical.
    This helps keep mesh information on import clean and reduces conflicts
    between imported mesh animation and \QDS's animation.

    The animation system in \QDS is a full implementation of bezier
    keys, and the full complement of bezier animation that you can create
    with 3D modeling tools and export are represented in \QDS. The more
    extreme differences between the various animation systems are mitigated
    by the limitations imposed by the supported formats.

    \section1 Materials and Textures

    Create and assign material slots in the 3D graphics tool before you export
    3D graphics. If you add several material slots, the first one is assigned
    to the object. Only material slots that have a material and that are
    assigned to a mesh on the exported object are imported into \QDS.

    If no material slots are assigned to an object, a default material is
    attached to the component that is created when you import the assets to
    \QDS.

    UV-unwrapping your model will create a UV layout. Without a UV layout, you
    will not be able to render any textures on your model in \QDS.

    There are many different ways and techniques to unwrap 3D meshes, depending
    on the 3D graphics tool.

    \section1 Lights

    Lights are imported to \QDS. Position, rotation, scale, brightness,
    light color, and the cast shadows property values are preserved.

    If the light type is not supported by Qt Quick 3D, it is converted into
    one of the supported types.

    \section1 Cameras

    Perspective and orthographic cameras are imported to \QDS. Position,
    rotation, and scale property values, as well as start and end clipping
    values are preserved. For perspective cameras, field of view values
    are also preserved.

    \section1 Node Hierarchy

    \QDS supports importing hierarchical information. Hierarchies of arbitrary
    depth are supported, including grouped nodes. Hierarchical transforms are
    applied as expected.
*/
