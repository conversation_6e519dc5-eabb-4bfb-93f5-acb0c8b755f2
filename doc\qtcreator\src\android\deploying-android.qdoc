// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

    /*!
    \previouspage creator-deployment.html
    \page creator-deploying-android.html
    \nextpage creator-deployment-b2qt.html

    \title Deploying to Android

    On Android, applications are distributed in specially structured types of
    ZIP packages called Application Packages (APK) or Android App Bundles (AAB).
    APK files can be downloaded to and executed on a device, whereas AAB is
    intended to be interpreted by the Google Play store and is used to generate
    APK files.

    \l{Qt for Android} has binaries for armv7a, arm64-v8a, x86, and x86-64.
    To support several different ABIs in your application, build an AAB that
    contains binaries for each of the ABIs. The Google Play store uses the
    AAB to generate optimized APK packages for the devices issuing the download
    request and automatically signs them with your publisher key.

    \QC supports the following methods of deployment for Android applications:

    \list
        \li As a stand-alone, distributable application package (APK).
        \li Since Qt 5.14.0, as an app bundle (AAB), intended for distribution
            in the Google Play store.
    \endlist

    \note Since \QC 4.12, Ministro is not supported.

    To \l{Specifying Settings for Packages}{specify settings} for application
    packages, select \uicontrol Projects > \uicontrol Build >
    \uicontrol {Build Android APK} > \uicontrol Details.

    For more information about options that you have for running applications,
    see \l {Specifying Run Settings for Android Devices}.

    \section1 Packaging Applications

    Because bundling applications as APK packages is not
    trivial, Qt 5 provides a deployment tool called \c androiddeployqt.
    When you deploy an application using a \e {Qt for Android kit}, \QC uses
    the \c androiddeployqt tool to create the necessary files and to bundle them
    into an APK:

    \list

        \li Java files, which serve as the entry point into your application and
            that automatically load Qt and execute the native code in your
            application.

        \li AndroidManifest.xml, which provides meta-information about your
            application.

        \li Other XML files, which specify the dependencies of your application.

        \li Resource files.

        \li Libraries and QML files, which can be included in the project
            depending on the deployment method that you select.

        \li Gradle wrappers that are needed to download and use Gradle.

        \li Gradle script that is needed by Java IDEs, such as Android Studio.
            It allows the user to extend the Java part without copying our Java
            sources. It also allows the IDEs to provide code completion, syntax
            highlighting, and so on.

    \endlist

    The Gradle wrappers and scripts are bundled only if you use Gradle to build
    the application packages. For more information, see
    \l{Connecting Android Devices}.

    To view the packages that the \c androiddeployqt tool created, select the
    \uicontrol {Open package location after build} check box.

    \section2 Specifying Deployment Settings

    The available deployment settings are listed in the \uicontrol Method field.
    To add deployment methods for a project, select \uicontrol Add.

    \image qtcreator-android-deployment-settings.png "Deployment settings"

    To rename the current deployment method, select \uicontrol Rename.

    To remove the current deployment method, select \uicontrol Remove.

    The packages are deployed on the Android device that you select in the
    \l{Building for Multiple Platforms}{kit selector}. To add devices, select
    \uicontrol Manage.

    For more information about specifying additional start options for
    applications, see \l{Specifying Run Settings for Android Devices}.

    To remove previously installed files from the device, select
    \uicontrol {Uninstall the existing app before deployment}.

    To install a pre-built APK, such as a 3rd-party application to a device,
    select \uicontrol {Install an APK File}.

    \section2 Specifying Settings for Packages

    To specify settings for the \c androiddeployqt tool, select
    \uicontrol Projects > \uicontrol {Build & Run} > \uicontrol Build >
    \uicontrol {Build Android APK} > \uicontrol Details.

    \image qtcreator-android-build-apk-step.png "Build Android APK step"

    The \c androiddeployqt tool uses the configuration information to
    create APKs. For more information about the available options, see
    \l{androiddeployqt}.

    You can view information about what the \c androiddeployqt tool is doing in
    \l {Compile Output}. To view additional information, select the
    \uicontrol {Verbose output} check box.

    Select \uicontrol {Add debug server} to include the debug server binary
    into a package.

    \section3 Selecting API Level

    In the \uicontrol {Android build platform SDK} field, you can select the
    API level to use for building the application. Usually, you should select
    the highest API level available.

    \note For Qt 5.12.0 to 5.12.5 and Qt 5.13.0 to 5.13.1, Android build
    platform SDK 28 should be used. For more recent versions than Qt 5.13.1,
    build platform SDK 29 or the most recent one should be used.

    This field does not specify the minimum supported API level nor the target
    API level, which you can specify in the Android manifest. See
    \l{Editing Manifest Files}. For more information about Android API levels, see
    \l{What is API Level?}.

    \section3 Building AABs

    For testing the application locally, use the APK format because
    the package can be uploaded directly to the device and run. For
    distribution to the Google Play store, create an AAB by selecting
    the \uicontrol {Build Android App Bundle (*.aab)} check box.

    When building with CMake, you can view the selected ABIs in
    \uicontrol {Initial Configuration} in the \uicontrol CMake section.
    You can set additional ABIs as values of the \c ANDROID_ABI key:

    \image qtcreator-android-cmake-settings.png "CMake settings for building AABs"

    When building with Qbs or qmake, you can select the ABIs in the
    \uicontrol ABIs field in the \uicontrol {Build Steps}:

    \image qtcreator-android-build-steps.png "qmake settings for building AABs"

    \section3 Signing Android Packages

    To publish your application, you must sign it by using a \e {public-private
    key pair} that consists of a \e certificate and a corresponding
    \e {private key} and is identified by an \e alias. The key pair is used to
    verify that the future versions of your application are actually created by
    you.

    \warning Keep the key pair in a safe place and take back up copies because
    you cannot update the application if you lose the key pair.

    You can use \QC to generate a keystore and a \e {self-signed} certificate.
    The generated certificate has the structure of an X.509 v3 digital
    certificate. It contains information about the version, serial number, and
    validity period of the certificate, the ID of the algorithm that is used to
    encrypt the data, the organization that issued the certificate, and the
    \e subject (owner) of the certificate. In case of a self-signed certificate,
    the issuer and owner of the certificate are the same. In addition, the
    certificate contains information about the algorithm that is used to sign
    the certificate, as well as the signature of the certificate.

    The keystore is protected by a password. In addition, you can protect each
    alias with its individual password.

    When you sign an Android application, you must select a keystore that
    contains certificates and a certificate alias from the keystore. The public
    key (certificate) for the alias is embedded into the APK during signing.

    To create a keystore and a self-signed certificate:

    \list 1

        \li In the \uicontrol Keystore field, select \uicontrol Create to create
            a new keystore that contains one key pair in the
            \uicontrol {Create a Keystore and a Certificate} dialog:

            \image qtcreator-android-certificate.png

        \li In the \uicontrol Keystore group, enter a password to protect the
            keystore.

        \li In the \uicontrol Certificate group, specify the key size and validity
            period of the certificate. You can specify a separate password to
            protect the key pair or use the keystore password.

        \li In the \uicontrol {Certificate Distinguished Names} group, enter
            information about yourself and your company or organization that
            identifies the issuer and the owner of the key pair.

        \li Select \uicontrol Save.

        \li In the \uicontrol {Keystore File Name} dialog, enter a name for the
            keystore and select a location for it.

        \li In the \uicontrol Keystore dialog, enter the keystore password to create
            the key pair in the keystore.

    \endlist

    To sign an Android package by using a key pair, set the \uicontrol {Sign package}
    group settings described in \l{Specifying Settings for Packages}:

    \list 1

        \li In the \uicontrol Keystore field, select \uicontrol Choose to select an existing
            keystore.

        \li In the \uicontrol {Certificate alias} field, select an alias from the list
            of key pairs that the keystore contains.

        \li Select the \uicontrol {Sign package} check box to use the alias to
            sign the Android package.

    \endlist

    \section3 Adding External Libraries

    \QC automatically detects which Qt libraries the application uses and adds
    them as dependencies. If the application needs external libraries, specify
    them in \uicontrol Projects > \uicontrol Build > \uicontrol {Build Android APK}
    > \uicontrol {Additional Libraries} field. The libraries are copied into
    your application's library folder and loaded on startup.

    To add OpenSSL libraries, select \uicontrol {Include prebuilt OpenSSL libraries}
    in the \uicontrol {Additional Libraries} group. This will add the OpenSSL
    include project defined in \l{Specifying Android Device Settings}{device settings}
    in \uicontrol {Android OpenSSL} group. This can be used for qmake and CMake
    projects.

    Otherwise, you can manually add the paths to the required \c libssl.so and
    \c libcrypto.so libraries to the \uicontrol {Additional Libraries} field.

    \section1 Editing Manifest Files

    You can use the configuration options to specify all the settings you need
    for the \c androiddeployqt tool. You only need an Android manifest file
    to specify Android-specific settings, such as the application icon.
    However, the manifest file is needed when you want to publish the package
    in the Play Store.

    \section2 Package Names

    Android application packages are usually named by using a hierarchical
    pattern with the levels in the hierarchy separated by periods (.). In
    general, a package name begins with the top level domain name of the
    organization followed by the organization's domain name and any subdomain
    names listed in reverse order. The organization can then choose a specific
    name for their package. Package names should be written in all lowercase
    characters whenever possible. For example, \c {org.qtproject.example}.

    Complete conventions for disambiguating package names and rules for naming
    packages when the Internet domain name cannot be directly used as a package
    name are described in section 7.7 of the Java Language Specification.

    For more information about package names, see
    \l{Android Application Fundamentals}.

    \section2 Styling

    Qt uses different methods to determine how Qt Widgets and Qt Quick Controls
    should be styled:

    \list
        \li Select \uicontrol Default or \uicontrol Full when using
            Qt Widgets or Qt Quick Controls 1 in your project.

            \note This method uses some Android non-SDK interfaces,
                  that are being restricted by Google starting from Android 9.0
                  (API 28).
        \li Select \uicontrol Minimal when using Qt Quick Controls 2
            but no Qt Widgets or Qt Quick Controls 1. This is faster than
            using the default or full options.
        \li Select \uicontrol None when using neither Qt Widgets
            nor Qt Quick Controls 1 or 2.
    \endlist

    \section2 Screen Orientation

    You can specify different options for determining screen orientation,
    depending on sensor readings or user preferences. The following table
    lists the options available.

    \table
        \header
            \li Orientation
            \li Description
        \row
            \li Unspecified
            \li The system chooses the orientation. The policy it uses, and
                therefore the choices made in specific contexts, may differ
                from device to device.
        \row
            \li Behind
            \li Use the same orientation as the activity that's immediately
                beneath it in the activity stack.
        \row
            \li Landscape
            \li Landscape orientation, where the display width is larger than
                its height.
        \row
            \li Portrait
            \li Portrait orientation, where the display height is larger than
                its width.
        \row
            \li Reverse landscape
            \li Landscape orientation in the opposite direction of normal
                landscape.
        \row
            \li Reverse portrait
            \li Portrait orientation in the opposite direction of normal
                portrait.
        \row
            \li Sensor landscape
            \li Landscape orientation, but it can be either normal or reverse
                landscape based on the device sensor. The sensor is used even
                if the user has locked sensor-based rotation.
        \row
            \li Sensor portrait
            \li Portrait orientation, but it can be either normal or reverse
                portrait based on the device sensor. The sensor is used even
                if the user has locked sensor-based rotation.
        \row
            \li User landscape
            \li Landscape orientation, but it can be either normal or reverse
                landscape based on the device sensor and the user's preference.
        \row
            \li User portrait
            \li Portrait orientation, but it can be either normal or reverse
                portrait based on the device sensor and the user's preference.
        \row
            \li Sensor
            \li The orientation is determined by the device orientation sensor.
                The orientation of the display depends on how the user is
                holding the device. It changes when the user rotates the device.
                Some devices, however, will not rotate to all four possible
                orientations by default. To allow all four orientations, select
                the full sensor option. The sensor is used even if the user
                locked sensor-based rotation.
        \row
            \li Full sensor
            \li The orientation is determined by the device orientation sensor
                for any of the four orientations. This is similar to the sensor
                option, except that it allows any of the four possible screen
                orientations, regardless of what the device will normally do.
                For example, some devices won't normally use reverse portrait
                or reverse landscape, but this option enables them.
        \row
            \li No sensor
            \li The orientation is determined without reference to a physical
                orientation sensor. The sensor is ignored, so the display will
                not rotate based on how the user moves the device.
        \row
            \li User
            \li The user's current preferred orientation.
        \row
            \li Full user
            \li If the user has locked sensor-based rotation, this option
                behaves in the same way as the user option. Otherwise, it
                behaves the same as the full sensor option, and allows any
                of the four possible screen orientations.
        \row
            \li Locked
            \li Locks the orientation to its current rotation, whatever that is.
    \endtable

    \section2 Icons and Splash Screens

    You can set different images to be shown as application icons and splash
    screens on low, medium, high, and extra high DPI displays. The following
    list summarizes the DPI values typically associated with each category:

    \list
        \li Low-density (LDPI): ~120dpi
        \li Medium-density (MDPI): ~160dpi
        \li High-density (HDPI): ~240dpi
        \li Extra-high-density (XHDPI): ~320dpi
        \li Extra-extra-high-density (XXHDPI): ~480dpi
        \li Extra-extra-extra-high-density (XXXHDPI): ~640dpi
    \endlist

    Specify settings for icons in the \uicontrol {Application icon} tab. Select
    the image with the highest resolution as the \uicontrol {Master icon}.
    \QC resizes the icon and sets versions of it to be shown on low, medium,
    high, and extra high DPI displays, as needed. Alternatively, set the icons
    for each resolution separately.

    \image qtcreator-android-manifest-editor-app-icon.png "Application icons in Android Manifest Editor"

    Specify settings for splash screens in the \uicontrol {Splash screen} tab.
    Select images to display as splash screens depending on the device
    orientation and screen resolution.

    \image qtcreator-android-manifest-editor-splash-screen.png "Splash screens in Android Manifest Editor"

    By default, the splash screen is hidden automatically
    when an activity is drawn. To keep it visible until
    \l{https://doc.qt.io/qt-6/qnativeinterface-qandroidapplication.html#hideSplashScreen}
    {QNativeInterface::QAndroidApplication::hideSplashScreen()} is
    called, select the \uicontrol {Sticky splash screen} check box.

    In \uicontrol {Image show mode}, select whether to center the splash screen
    on the device display or scale it to fill the display.

    Set a background color in \uicontrol {Background color}.

    Select the images with the highest resolution as the
    \uicontrol {Master image}, \uicontrol {Portrait master image},
    and \uicontrol {Landscape master image}.

    Select \uicontrol {Clear All} to reset all settings or remove all images.

    \section2 Android Manifest Editor

    If you use qmake as the build system, you can create an Android manifest
    file and edit it in \QC.

    To create an Android manifest file and to open it in the Android Manifest
    Editor:

    \list 1

    \li Select \uicontrol Projects > \uicontrol Build >
        \uicontrol {Build Android APK} > \uicontrol {Create Templates}.

    \li Check the path in \uicontrol {Android package source directory}.

        \image qtcreator-android-create-template.png "Create Template dialog"

    \li Select \uicontrol {Copy the Gradle files to Android directory} if you
        plan to extend the Java part of your Qt application.

    \li Select \uicontrol Finish to copy the template files to the \c android
        directory and to open the manifest file for editing.

    \li In the \uicontrol {Package name} field, enter a valid
        \l{Package Names}{package name} for the application.
        For example, \c {org.example.myapplication}.
        The application is launched by an automatically generated Java launcher
        that is packaged with the application into an Android package (.apk).

        \image qtcreator-android-manifest-editor-package.png "Package info in Android Manifest Editor"

    \li You can specify an internal version number for the package in the
    \uicontrol {Version code} field. It is used to determine whether one version of
    the application is more recent than another. In the \uicontrol {Version name}
    field, specify the version number that is shown to users.

    \li In the \uicontrol {Minimum required SDK} field, select the minimum API level
    required to run the application. The minimum supported API level for \QC is
    android-9. However, Qt versions might have different minimum API levels, and
    therefore \QC does not allow you to select an API level that the Qt version
    specified for the kit does not support.

    \li In the \uicontrol {Target SDK} field, select the targeted API level of the
    application. This affects the activation of some compatibility features in
    the OS. The value used by the \c androiddeployqt tool by default is 14, which
    means that the overflow button in the system navigation bar will not be
    enabled by default.

    \li In the \uicontrol {Application name} field, set the application's name.

    \li In the \uicontrol {Activity name} field, set an activity name.

    \li In the \uicontrol {Style extraction} field, set the method that Qt
        uses to \l{Styling}{determine which UI style to use}.

    \li In the \uicontrol {Screen orientation} field, select the option for
        determining \l{Screen Orientation}{screen orientation}.

    \li In \uicontrol {Application icon}, specify images to use as application
        icons depending on screen resolution.

    \li In \uicontrol {Splash screen}, select images to display as splash
        screens depending on the screen orientation and resolution.

    \li In \uicontrol {Android services}, select \uicontrol Add to add a service.
    You must enter at least a service class name for a new service. If you select
    \uicontrol {Run in external process}, you also need to enter a process name.
    If you select \uicontrol {Run in external library}, you need to enter a library name.
    Service arguments are mandatory for a service that is not run in an external
    library. For more information about writing service code and structure of services,
    see \l{Android Services}.

    \image qtcreator-android-manifest-editor-services.png "Android services in Android Manifest Editor"

    \li In the \uicontrol Permissions field, you can specify the permissions that your
    application needs. Starting from Android 6.0 (API 23), permissions have to be
    requested at runtime (see \l{QtAndroidPrivate::requestPermission()}). For
    lower Android API levels, users are asked to grant the permissions when they
    install the application. Android OS then grants the application access to the
    appropriate data and features.

    \image qtcreator-android-manifest-editor-permissions.png "Permissions in Android Manifest Editor"

    \li Select the \uicontrol {Include default permissions for Qt modules} and
    \uicontrol {Include default features for Qt modules} check boxes to add the
    permissions needed by Qt libraries. This can be
    \c {android.permission.WRITE_EXTERNAL_STORAGE} for \l{Qt Core} or
    \c {android.permission.ACCESS_COARSE_LOCATION} for
    \l{https://doc.qt.io/qt-5.15/qtlocation-index.html}{Qt Location}.

    \li To add a permission, select it from the list, and then click \uicontrol Add.

    \endlist

    On the top header, select the \uicontrol {XML Source} tab to edit the file
    in XML format.

*/

