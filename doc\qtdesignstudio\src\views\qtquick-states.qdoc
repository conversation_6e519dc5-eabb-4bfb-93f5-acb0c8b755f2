// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-states.html
    \if defined(qtdesignstudio)
    \previouspage quick-dynamic-properties.html
    \nextpage creator-live-preview.html
    \else
    \previouspage quick-connections-backend.html
    \nextpage exporting-3d-assets.html
    \endif

    \title Working with States

    You can define states for components and component instances in the
    \l States view by selecting \inlineimage icons/plus.png
    .

    \image qmldesigner-transitions.png "States view"

    Click the new state to switch to it in the \l {2D} and \l{3D} views, and
    then modify the values of the properties of components or component
    instances in \l Properties.

    For example, to change the appearance of a button, you can define states in
    the button component to hide the button image and show another image in its
    place or to change the button background or text color. When you use
    instances of the button in other components, you can define states to
    create different screens by hiding or showing button component instances.
    The preset \l Button control in \uicontrol Components
    > \uicontrol {Qt Quick Controls} > \uicontrol Controls has predefined
    \e normal and \e down states.

    \if defined(qtdesignstudio)
    This also applies to the custom button component that you can create
    by using a \l{Creating Custom Controls}{wizard template}. For more
    information about editing the states within the button component and
    hiding and showing buttons to create several screens, see
    \l{Log In UI - Components} and \l{Log In UI - States}.
    \endif

    To add motion to a screen, you can change the position of a component
    instance in the \uicontrol {2D} view and then add animation to the change
    between the states.

    The properties that you change in a state are highlighted with blue color.
    In the \l{Code} view, you can see the changes recorded as changes
    to the base state.

    \image qmldesigner-states.png "States and Properties views"

    \note If you have \l{Locking Components}{locked a component} in
    \l Navigator, and you attempt to remove states where you change the
    values of its properties, you are prompted to confirm the removal.

    For more information, watch the following video:

    \youtube FzmLuRHQXaw

    \section1 Setting the Default State

    The default state determines the startup state of the application.

    To set a state to the default state, select \uicontrol Default.

    \section1 Applying States

    To determine when a state is applied, select \inlineimage icons/edit.png
    in the \uicontrol {When Condition} field. In \uicontrol {Binding Editor},
    specify a \l [QtQuick]{State::when}{when} property for the state. Set the
    value of the property to a boolean expression that evaluates to \c true when
    you want the state to be applied.

    This enables you to evaluate the truthfulness of several components'
    properties and move the UI to the state in which these conditions apply.
    You can evaluate whether something is true or false, greater than or equal
    to something else, and so on. You can also use operators, such as AND or
    OR, to evaluate the truthfulness of several components.

    The when conditions are evaluated from left to right and in order of
    appearance in the code. Therefore, if you have two conditions for two
    different states that both evaluate to \c true, the first state is applied.

    In \uicontrol {Binding Editor}, select the component and property to
    create the expression. For example, to change the state when a button is
    pressed, you could select a button component and its pressed property.

    \image qtquick-states-binding-editor.png "Binding Editor in States view"

    When you compose the expressions in \uicontrol {Binding Editor}, the
    \l{Completing Code}{code completion} feature lists the components and
    their properties you can use in the expressions.

    \include creator-logical-operators.qdocinc logical operators

    \section2 Examples of when Conditions

    To apply a state to a button when the button is pressed, you could simply
    write:

    \badcode
    when: control.pressed
    \endcode

    To apply a state when the button is not pressed, select the \uicontrol NOT
    check box.

    \image qtquick-states-binding-editor-not.png "NOT check box in Binding Editor"

    To apply a state when the button is not pressed, selected, nor hovered on,
    you could combine conditions, as follows:

    \badcode
    when: !control.pressed && !control.checked && !control.hovered
    \endcode

    To apply a state when the button is pressed or selected, but not hovered on,
    you could write:

    \badcode
    when: control.pressed || control.checked && !control.hovered
    \endcode

    \if defined(qtdesignstudio)
    If you are not familiar with writing expressions, you can use preset
    \l{Logic Helpers}{logic helpers} from \uicontrol Components
    > \uicontrol {Qt Quick Studio Logic Helper}.
    \endif

    \section1 Using States

    To keep the code clean, you should create a base state that contains all
    the components you will need in the application. You can then create states,
    in which you hide and show a set of components and modify their properties.
    This allows you to:

    \list
        \li Align components on different views with each other.
        \li Avoid excessive property changes. If a component is invisible in
            the base state, you must define all changes to its child components
            as property changes, which leads to complicated code.
        \li Minimize the differences between the base state and the other states
            to keep the code short and readable and to improve performance.
        \li Avoid problems when using transitions and animation when changing
            states.
    \endlist

    To create views for an application by using states:

    \image qmldesigner-screen-design.png "Designing views"

    \list 1
        \li In the base state, add all components you will need in the
            application (1). While you work on one view, you can click the
            \inlineimage icons/eye_open.png
            icon in \l Navigator to hide components on the canvas that are
            not part of a view.
        \li In \uicontrol States, select the \uicontrol + symbol to create
            a new state and give it a name. For example, \c Normal.
        \li In \l Properties (2), deselect the \uicontrol Visibility
            check box or set \uicontrol Opacity to 0 for each component that
            is not needed in this view. If you specify the setting for the
            parent component, all child components inherit it and are also
            hidden.
        \li Create additional states for each view and set the visibility
            or opacity of the components in the view.
        \li To determine which state is applied when the application starts,
            select \uicontrol Default.
    \endlist

    \if defined(qtcreator)
    \include qtquick-states-scxml.qdocinc scxml state machines
    \endif

    \section1 State Groups

    With state groups, you can change the state of certain components
    independently of other components and their states in the same view.

    Each state group has its own property changes and transitions.

    By default, there is one root state group.

    \section1 Extending States

    When a state extends another state, it inherits all the changes of that
    state. The state being extended is treated as the base state in regards to
    the changes specified by the extending state.

    Using extended states can make the user interface and your QML code
    cleaner.

    Below is an example where the only change between \e State1 and \e State2
    is the brightness of \e directionalLight.

    Here, extended states are not used:

    \image no-extended-state.webp

    Here, \e State2 is extended from \e State1:

    \image extended-state.webp

*/
