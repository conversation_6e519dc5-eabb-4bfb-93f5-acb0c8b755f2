// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page exporting-from-qt3ds.html
    \previouspage exporting-from-maya.html
    \if defined(qtdesignstudio)
    \nextpage studio-importing-designs.html
    \else
    \nextpage studio-importing-3d.html
    \endif

    \title Exporting from Qt 3D Studio

    Use the following guidelines to achieve the best results when converting
    projects from \Q3DS to \QDS.

    \section1 Best Practices

    \list
    \li If something in UI is clearly 2D, do not import it from \Q3DS
        presentation. Implement it directly in \QDS.
    \li QML streams cannot be imported as QML elements directly into \QDS.
        They need to be copied manually since there is no import functionality
        for QML files in \QDS.
    \li If you do import the whole \Q3DS project (\c .uia file), use only the
        parts you actually need from it. Use \uicontrol
        {Move Component Into Separate File} for the parts you need, and
        eventually delete all the rest from the project structure.
    \li There are extra levels of \c Node elements in most imported QML files,
        and they should be removed for improved performance. As a rule of thumb,
        each Node that has only one child is considered an extra level that
        should be deleted.
    \li \Q3DS \e slides become \QDS \e states, and there may be problems
        with state changes. Always go through each of the state changes to make
        sure they work correctly.
    \endlist

    \section1 Importing Assets

    \section2 Custom Materials
    Custom materials (\e something.shader files in \Q3DS) are not imported
    correctly, and have to be fixed manually.
    \list
    \li They are imported as \c {CustomMaterial { id: something; source:
    "something" }} that neither works nor does anything.
    \li Remove the \e {source: "something"} altogether.
    \li Vertex shader part from the \e something.shader needs to be copied to
        \c {Shader { stage: Shader.Vertex; shader: "vertex shader code here" }}
        inside the \c CustomMaterial. Fragment shader part needs to be copied
        to \c {Shader { stage: Shader.Fragment; shader: "fragment shader code
        here" }}.
    \li Properties in \e Metadata of the \e something.shader need to be
        introduced manually as QML properties of the same name and correct type
        inside the \c CustomMaterial element.
    \li Add \c {import QtQuick3D.Materials 1.15} import statement to files that
        have \c CustomMaterial elements.
    \li If custom material uses textures, they are not imported automatically
        and must be manually added to the project structure.
    \li Many custom materials are not rendered in the \uicontrol{3D} or
        \uicontrol {2D} view due to an open bug in \QDS 1.5. The
        project needs to be run for them to appear.
    \endlist

    \section2 Standard Materials
    Some properties of standard materials may not be imported correctly.
    \list
    \li The sensible value ranges of some properties may have changed between
        \Q3DS and \QDS and need to be redefined manually.
    \li Extra properties may be added and can be removed manually.
    \endlist

    \section2 Models
    Models have some extra properties (tessellations), which can be removed.

    \section1 Example: Converting MyOwnCluster Project from \Q3DS to \QDS

    \image exporting-from-qt3ds/porting-example-myowncluster.png "My Own Cluster project in Qt 3D Studio"
    \e {My Own Cluster project in \Q3DS}

    The following steps describe how to convert the My Own Cluster project from
    \Q3DS to \QDS.

    \section2 Creating a New Project in \QDS

    \list 1
    \li To create a new project in \QDS, select \uicontrol {File > New File or
        Project}, or select \uicontrol {New Project} in the Welcome mode.
        \image exporting-from-qt3ds/01-welcome-screen.png "Welcome mode in Qt Design Studio"

    \li Creating a new project in \QDS is aided by a wizard that contains
        templates for creating different types of projects. Choose the
        \uicontrol {Qt Quick 3D Application} template to get started with your
        new 3D project.
        \image exporting-from-qt3ds/02-create-new-project.png "Create a new Project in Qt Design Studio"

    \li In the \uicontrol Name field, enter a name for the project.
        In the \uicontrol {Create in} field, enter the path for the project
        files, and then select \uicontrol Next.
        \image exporting-from-qt3ds/03-folder-and-project-name.png "Project location and name"

    \li In the \uicontrol {Screen resolution} field, select the screen
        resolution for previewing the UI on the desktop or on a device.
        You must select one of the predefined screen resolutions, which will
        later be altered to match the width and height of the original project.
        In the \uicontrol {Qt Quick Controls Style} field, select one of the
        predefined UI styles to use, and then select \uicontrol Finish.
        \image exporting-from-qt3ds/04-project-size.png "Select the screen resolution for the project"

    \li Your new project has now been created. For more information on
        creating projects in \QDS, see \l {Creating Projects}.
        For more information on how to get started with \QDS, see
        \l {Getting Started}.
        \image exporting-from-qt3ds/05-new-project-created.png "New project in Qt Design Studio"
        \endlist

    \section2 Getting Started with MyOwnCluster

    \list 1
    \li Return to your project in \Q3DS to check the size of the presentation.
        Select \uicontrol {Edit} > \uicontrol {Presentation Settings} to see the
        values for \uicontrol {Width x Height}.
        \image exporting-from-qt3ds/06-check-screen-size-in-qt3ds-project.png "Check screen size in Qt 3D Studio presentation"

    \li Adjust the canvas size of your project in \QDS according to the screen
        size in \Q3DS presentation by editing the \c Constants.qml file in the
        \l{Code} view. If you cannot see the \uicontrol {Code}
        view, select \uicontrol Window > \uicontrol {Views}, and then select the
        \uicontrol {Code} checkbox. In the \uicontrol Projects view, open
        the imports subfolder, then the subfolder named after your project, and
        double-click \c Constants.qml. Edit the values for \e {readonly property
        int width} and \e {readonly property int height} to match the \uicontrol
        {Width x Height} values in \Q3DS presentation.
         \image exporting-from-qt3ds/07-screen-size-in-text-editor.png "Adjust the canvas size"

    \li To delete the placeholders created by the wizard, multiselect \uicontrol
        Text and \uicontrol view3D components (\key Ctrl + mouse left click) in
        \uicontrol Navigator, then right click on the selected items, and select
        \uicontrol Edit > \uicontrol Delete.
        \image exporting-from-qt3ds/08-delete-placeholders.png "Delete placeholders in Navigator"
        \endlist

    \section2 Importing Assets
    \list 1
    \li Select \uicontrol Assets > \inlineimage icons/plus.png
        .
        \image exporting-from-qt3ds/09-add-new-assets.png

    \li Select the \c .uia file for the \Q3DS project you wish to import, and
        then select \uicontrol Open.
        \image exporting-from-qt3ds/10-find-project-file.png "Find the uia file in file explorer"

    \li Select \uicontrol Import, and after the import is complete, select \uicontrol
        Close.
        \image exporting-from-qt3ds/11-import.png "Import the file"

    \li The 2D assets imported from \Q3DS now appear in > \uicontrol Assets.
        \image exporting-from-qt3ds/12-imported-image-assets.png "Imported assets in Components"

    \li The QML components generated from the imported \Q3DS project now appear
        as available imports in \uicontrol Components under \uicontrol {QML Types}.
        Select \uicontrol Quick3DAssets.MyOwnCluster to import them to your
        project.
        \image exporting-from-qt3ds/13-myowncluster-in-qml-types.png "Available imports in QML Types"

    \li The imported QML types now appear in \uicontrol Components and can be
        added to the project.
        \image exporting-from-qt3ds/14-add-myowncluster-to-project.png "MyOwnCluster in Components"
    \endlist

    \section2 Adding Components to the Project

    \list 1
    \li Drag \uicontrol MyOwnCluster from \uicontrol {My Quick3D Components}
        in \uicontrol Components to the \uicontrol {2D} view.
        \image exporting-from-qt3ds/15-drag-to-project.png "Drag MyOwnCluster to the 2D view"

    \li In \uicontrol Navigator, right click on myOwnCluster and select \uicontrol
        {Go into Component}.
        \image exporting-from-qt3ds/16-go-into-component.png "Go into component My Own Cluster"

    \li Find the offending line in the \uicontrol {Code} view.
        \image exporting-from-qt3ds/17-offending-line.png

    \li Comment out the offending line by placing two slashes in the beginning
        of it (or remove the line).
        \image exporting-from-qt3ds/17b-commented-out.png

    \li In Navigator, go to each component of the project and comment out
        (or remove) any offending lines you find.
        \image exporting-from-qt3ds/18-repeat-where-needed.png

    \li You should now see some parts of the project in the \uicontrol {2D} view.
        \image exporting-from-qt3ds/19-see-form-editor.png "Project in the 2D view"
    \endlist

    \section2 Converting 3D Elements

    \list 1
    \li Drag subpresentation_ADAS from \uicontrol Components
        > {My 3D Components} into layer folder in \uicontrol Navigator.
        \image exporting-from-qt3ds/20-drag-subpresentation-adas-into-layer.png "Drag into layer"

    \li To delete the old subpresentation rectangle, right-click on the file
        name (subpresentation_ADAS2_u52017 in this project), select
        \uicontrol Edit > \uicontrol Delete.

    \li Go into the component subpresentation_ADAS.
        \image exporting-from-qt3ds/22-go-into-subpresentation-adas.png "Go into component"

    \li Reposition objects and/or camera in the \uicontrol{3D} view according to
        the original \Q3DS project. The desired scene may be achieved
        simply by changing the z position sign from positive to negative, or
        vice versa, in some cases.

        See the \l {3D} view for more information on how to edit 3D scenes.
        \image exporting-from-qt3ds/23-replace-objects.png "Replace objects in the 3D view"

    \li Recreate animations in subpresentation_ADAS according to the original
        project. For more information on creating animations in \QDS, see
        \l {Creating Timeline Animations}.
        \image exporting-from-qt3ds/24-recreate-animations.png "Recreate animations"

    \li Go to the \uicontrol Timeline View to review the timeline for the
        project.
        \image exporting-from-qt3ds/25-adas-timeline.png "ADAS timeline view"
    \endlist

    \section2 Converting 2D Elements

    \list 1
    \li Recreate the 2D elements of the original project (in all layers) using
        the 2D QML elements available in \QDS. You can use the imported 2D layer
        as a guide for recreating the elements. If the 2D elements in the
        original project have rotations, especially in a 3D layer, make sure to
        add rotations that mimic the original ones to the \QDS project. For
        example, rotation on one axis with perspective camera requires rotation
        on two axes in pure 2D. You may need to use the \uicontrol {Code} view
        to achieve rotation similar to the rotation of the object in \Q3DS.
        For more information on specifying advanced transformations on
        \uicontrol Items, see \l Transform.
        \image exporting-from-qt3ds/26-recreate-2d-elements.png "Recreate 2D elements"

    \li To delete the 2D layer (telltaleLayer_u39332) in \uicontrol Navigator
        after recreating the 2D elements,  right-click on the component, and
        select \uicontrol Edit > \uicontrol Delete.
        \image exporting-from-qt3ds/27-delete-2d-layer.png "Delete the 2D layer"

    \li Next, you need to delete the 2D elements from the 3D layer (speed_u20335
        and rPM_u10371 from layer_32325). Right-click on the element, and
        select \uicontrol Edit > \uicontrol Delete.
        \image exporting-from-qt3ds/28-delete-2d-elements-from-3d-layer.png "Delete old 2D elements from the 3D layer"

    \li Recreate the animations for 2D elements the same way it was done for the
        3D elements.
    \endlist

    \section2 Converting QML Streams

    \list 1
    \li Find QML stream files in file explorer.
        \image exporting-from-qt3ds/30-find-qml-stream-files-in-file-explorer.png "Find the QML stream file"

    \li Move the QML stream files under the import's main folder (one level up
        in this example).
        \image exporting-from-qt3ds/31-move-under-myowncluster.png "Move the QML stream files under the import's main folder"

    \li Make sure that the QML stream file names start with a capital letter
        to enable \QDS to recognize them as QML component files.
        \image exporting-from-qt3ds/32-rename-the-qml-stream-file.png "The QML stream file names should start with a capital letter"

    \li Return to \QDS and enter the MyOwnCluster component. The QML stream
        component now appears in \uicontrol {My 3D Components}.
        \image exporting-from-qt3ds/33-see-qml-stream-component-in-myqmlcomponents.png "QML stream in My QML Components"

    \li Drag-and-drop the QML stream component to MyOwnCluster in \uicontrol
        Navigator.
        \image exporting-from-qt3ds/34-drag-to-myowncluster-in-navigator.png "Drag the QML stream component to MyOwnCluster"

    \li Go to the \uicontrol States view and use the \uicontrol Visibility tab
        in the \uicontrol Properties view to make the QML stream component
        visible only in the correct state.
        \image exporting-from-qt3ds/35-visible-states.png "Make components visible in correct states"

    \li Next, recreate the animations for the QML stream according to the original project.
        \image exporting-from-qt3ds/36-recreate-qml-animations.png "Recreate animations"

    \li Recreate all the keyframes for the imported QML stream that is now a
        component.
        \image exporting-from-qt3ds/37-recreate-keyframes.png "Recreate keyframes"
    \endlist

    \section2 Cleaning Up the Project Structure

    \list 1
    \li Go to file explorer and clean up the project structure deleting
        the folder and files that are not used in the \QDS project.
        \image exporting-from-qt3ds/38-delete-unnecessary-folders.png "Delete folders in file explorer"

    \li Individual files can also be deleted in the \uicontrol {Projects} view
        in \QDS. To delete a file, right-click on it, select  \uicontrol
        {Delete File}, and then select \uicontrol Yes.
        \image exporting-from-qt3ds/39-delete-individual-files-in-qds.png "Delete individual files"

    \li This is the project structure after clean-up.
        \image exporting-from-qt3ds/40-done.png "Project structure after clean-up"
    \endlist

    \section2 Finished

    You have now converted your \Q3DS project to \QDS.
        \image exporting-from-qt3ds/41-finished-qds-project.png
*/
