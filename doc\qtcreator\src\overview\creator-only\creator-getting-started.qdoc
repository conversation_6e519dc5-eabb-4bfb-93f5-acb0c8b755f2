// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage index.html
    \page creator-getting-started.html
    \nextpage creator-overview.html

    \title Getting Started

    \table
        \row
            \li \inlineimage front-gs.png
            \li \inlineimage front-ui.png
            \li \inlineimage front-advanced.png
        \row
            \li \b {\l{IDE Overview}}

                If you have not used an integrated development environment (IDE)
                before, or want to know what kind of IDE \QC is, go to
                \l{IDE Overview}.
            \li \b {\l{User Interface}}

                If you have not used \QC before, and want to become familiar
                with the parts of the user interface, go to \l{User Interface}.
            \li \b {\l{Configuring Qt Creator}}

                To make \QC behave more like your favorite code editor or IDE,
                you can change the settings for keyboard shortcuts, color
                schemes, generic highlighting, code snippets, and version
                control systems. For an overview of the options you have, go to
                \l{Configuring Qt Creator}.
        \row
            \li \inlineimage front-preview.png
            \li \inlineimage front-help.png
            \li
        \row
            \li \b {\l{Building and Running an Example}}

                To check that the \l{https://www.qt.io/download-qt-installer}
                {Qt Online Installer} created \l{glossary-buildandrun-kit}
                {build and run kits}, open an example application and run it.
                If you have not done so before, go to
                \l{Building and Running an Example}.
            \li \b {\l{Tutorials}}

                Now you are ready to start developing your own applications.
                Pick a tutorial to follow in \l{Tutorials}. To start developing
                for mobile devices, select \l{Creating a Mobile Application}.
            \li
    \endtable

*/
