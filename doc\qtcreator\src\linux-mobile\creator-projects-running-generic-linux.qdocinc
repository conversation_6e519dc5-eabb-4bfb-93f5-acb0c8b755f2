// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [running on embedded linux]

    \section1 Running on Remote Linux Devices

    To build the application and run it on a device:

        \list 1

            \li Specify a connection to the device. For more information, see
                \l{Connecting Remote Linux Devices}.

            \li Click the \uicontrol Run button.

        \endlist

    \QC uses the compiler specified in the project build settings
    (tool chain) to build the application.

    \QC copies the application files to the connected device and runs the
    application. The application views are
    displayed on the device. Command-line output is visible in the \QC
    \uicontrol {Application Output} view.

    In the \uicontrol {Projects} mode, select the remote Linux kit and then
    select \uicontrol {Run} to view the settings for deploying the application
    to the connected device. For more information, see
    \l{Specifying Run Settings for Linux-Based Devices}.

    Debugging works transparently if GDB server is installed on the device and
    it is compatible with the GDB on the host.

//! [running on embedded linux]
*/
