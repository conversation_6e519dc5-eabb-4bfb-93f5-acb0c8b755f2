// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-camera.html
    \previouspage studio-3d-lights.html
    \nextpage studio-3d-scene-environment.html

    \title Cameras

    A camera is always necessary to view the content of a 3D scene. A camera
    defines how to project the content of a 3D scene into a 2D coordinate space,
    which can then be used on a 2D surface. When a camera is present in the
    scene, it can be used to direct what is displayed in a \l {3D Views}
    {3D view}.

     \image studio-qtquick-3d-components.png "Qt Quick 3D components in Components"

    To add a camera component to your UI, do one of the following:
    \list
      \li Drag a camera component from \uicontrol Components >
      \uicontrol {Qt Quick 3D} to the \l {3D} view or to
      \l Navigator > \uicontrol {View3D} > \uicontrol Scene.
      \li Right-click in the \uicontrol 3D view and select
      \uicontrol Create > \uicontrol Cameras from the context menu.
      \note You can only create \uicontrol {Camera Perspective} and
      \uicontrol {Camera Ortographic} this way.
    \endlist

    If you cannot find the camera components in \uicontrol {Components}, add the
    \uicontrol QtQuick3D module to your project, as described in
    \l {Adding and Removing Modules}.

    You can use the following components in your scenes to determine camera
    projection:

    \list
        \li \l{PerspectiveCamera}{Camera Perspective} is the standard camera
            type, which gives a realistic projection of the scene since distant
            objects are perceived as smaller. It uses field of view and near and
            far clip planes to specify the projection.
        \li \l{OrthographicCamera}{Camera Orthographic} renders all contents
            with no perspective. It is ideal for rendering 2D elements because
            your images are guaranteed to be the right size on the screen, and
            you can use the z position of components to bring them closer to or
            take them farther from the camera (\e z-sorting) with no
            foreshortening artifacts.
        \li \l{FrustumCamera}{Camera Frustum} enables finer grain control of
            how the frustum is defined, by setting the number of degrees between
            the top and bottom or left and right edges of the camera frustum.
            This is useful when creating asymmetrical frustums.
        \li \l{CustomCamera}{Camera Custom} provides  full control over how
            the projection matrix is created.
    \endlist

    You can position the camera in the scene and set the direction it is facing.
    The default direction of the camera is such that the forward vector is
    looking up the +z axis, and the up direction vector is up the +y axis. You
    can apply transforms to the camera and its parent components to define
    exactly where your camera is located and in which direction it is facing.

    The second part of determining the projection of the camera is defining the
    field of view (\e frustum) of the camera that defines which parts of the
    scene are visible, as well as how they are visible.

    You can edit the camera properties in the \uicontrol Properties view.

   \image studio-qtquick-camera-properties "Properties view for Perspective Camera"

    \section1 Setting Camera Field of View

    \note If you select \uicontrol {Qt 5} as the \uicontrol {Target Qt Version}
    when \l {Creating Projects}{creating your project}, the camera properties
    will be slightly different.

    The camera frustum can be obtained by taking a frustum (that is, a
    truncation with parallel planes) of the cone of vision that a camera or eye
    would have to the rectangular viewports typically used in computer graphics.
    The shape of the cone depends on the camera lens that is being simulated.
    Typically, it is a rectangular pyramid with the top cut off.

    The planes that cut the frustum perpendicular to the viewing direction are
    called the \e {near plane} and the \e {far plane}. Components in front of
    the near plane or behind the far plane are not drawn.

    The \uicontrol {Clip near} and \uicontrol {Clip far} properties determine
    the position of the near plane and the far plane. We recommend that
    you place the near and far planes as close to each other as possible to
    optimize depth accuracy. Components are clipped at pixel level instead of
    element level. This means that a model crossing a plane may be only
    partially rendered.

    The \uicontrol {Field of view} (FOV) property specifies the number of
    degrees between the edges of the camera frustum. The larger the value,
    the stronger the sense of 3D in your scene. By default, the
    \uicontrol {FOV orientation} property is set to use the vertical FOV.
    This value is the number of degrees between the top and bottom edges
    of the camera frustum.

    The \uicontrol {FOV orientation} property specifies either a
    \uicontrol Vertical (the default setting) or a \uicontrol Horizontal
    orientation for the field of view. The horizontal FOV determines the number
    of degrees between the left and right edges of the camera frustum. It is
    automatically calculated based on the aspect ratio of the scene when the
    FOV orientation is set to vertical. You can set the orientation to horizontal
    to translate FOV values from graphics tools such as Maya and Blender, which
    use horizontal FOV by default.

    The \uicontrol {Horizontal magnification} and \uicontrol {Vertical magnification}
    properties determine the horizontal and vertical magnification of the camera
    frustum.
    \note The \uicontrol {Horizontal magnification} and
    \uicontrol {Vertical magnification} properties are not available in Qt 5.

    The \uicontrol {Frustum culling enabled} property determines whether the
    objects outside the camera frustum will be culled, which means they will not
    be passed to the renderer.
    \note The \uicontrol {Frustum culling enabled} property is not available in
    Qt 5.

    The default values are intended to cause anything within the view
    of the camera to be rendered. Aside from special clipping effects, you
    may need to adjust these values to more closely contain your content for
    better results with ambient occlusion or with effects that use the depth
    buffer of the camera, such as the \e {depth of field} effect.

    \note Orthographic cameras don't have the FOV property.
*/
