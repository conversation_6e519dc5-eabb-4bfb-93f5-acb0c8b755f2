// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// Note: The \page value is hard-coded as a link in Qt Bridge for Figma.

/*!
    \previouspage qtbridge-sketch-using.html
    \page figmaqtbridge.html
    \nextpage qtbridge-figma-setup.html

    \title Exporting Designs from Figma

    You can use \QBF to export designs from Figma to a \e {.qtbridge}
    archive that you can \l{Importing 2D Assets}{import} to projects in \QDS.

    \image studio-figma-export.png

    The following topics describe setting up and using \QBF:

    \list

        \li \l{Setting Up Qt Bridge for Figma}

            You must install Figma and the \QBF export tool before you can use
            the tool to export designs.

        \li \l{Using Qt Bridge for Figma}

            To get the best results when you use \QBF to export designs from
            Figma, you should follow the guidelines for working with Figma and
            organizing your assets.
    \endlist
*/
