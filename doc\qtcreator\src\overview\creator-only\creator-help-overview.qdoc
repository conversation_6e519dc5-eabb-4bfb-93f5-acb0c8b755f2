// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-help-overview.html
    \previouspage collecting-usage-statistics.html
    \nextpage creator-help.html

    \title Getting Help

    \image front-help.png

    \list

        \li \l{Using the Help Mode}

            \QC comes fully integrated with Qt documentation and examples using
            the Qt Help plugin. You can add external documentation to the
            \uicontrol Help mode and filter the documents displayed to find relevant
            information faster. In addition, you can add bookmarks to help
            pages.

        \li \l{FAQ}

            Contains answers to some frequently asked questions about \QC.

        \li \l{How-tos}

            Lists useful \QC features.

        \li \l{Known Issues}

            Lists known issues in \QC version \qtcversion. The development team
            is aware of them, and therefore, you do not need to report them as
            bugs.

        \li \l{Glossary}

            Lists special terms used in \QC.

    \endlist

    \section1 Related Topics

    \list

        \li \l{Technical Support}

            Lists Qt support sites and other useful sites.

        \li \l{Acknowledgements}

            Lists the third-party components in \QC.

    \endlist

*/
