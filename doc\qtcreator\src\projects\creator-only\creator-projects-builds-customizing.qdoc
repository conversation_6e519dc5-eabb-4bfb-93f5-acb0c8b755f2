// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-build-process-customizing.html
    \previouspage creator-setup-webassembly.html
    \nextpage creator-testing.html

    \title  Customizing the Build Process

    To configure how projects are built, deployed, and run, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Build & Run}
     > \uicontrol General.

    \image qtcreator-project-options-deploy.png "Project General preferences"

    By default, the \uicontrol {Always deploy project before running it} (1) and
    \uicontrol {Build the Whole Project} (2) options are enabled. Therefore,
    when you select the \uicontrol Run function, \QC checks for changes in the
    project files and also builds and deploys the project if necessary.

    To deploy applications without building them or to just build the
    application that you want to run, select the appropriate options
    in the \uicontrol {Build before deploying} field.

    By default, the applications that the project contains are stopped
    before rebuilding the project. To stop just the current application,
    the applications in the same build directory, all applications,
    or no applications, select the appropriate option in the
    \uicontrol {Stop applications before building} field.

    On Windows, you can use \c jom instead of \c nmake for building the project
    to distribute the compilation process to multiple CPU cores. You can download
    \c jom from \l{http://download.qt.io/official_releases/jom}{Qt Downloads}.
    To use \c jom, select the \uicontrol {Use jom instead of nmake} check box.
    Deselect the check box if you experience build problems.

*/
