// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qt-design-viewer.html
    \previouspage creator-live-preview-android.html
    \nextpage studio-exporting-and-importing.html

    \title Sharing Applications Online

    \image qt-design-viewer.webp

    In \QDS, you can share your applications in most widely-used web browsers,
    such as Apple Safari, Google Chrome, Microsoft Edge, and Mozilla Firefox,
    on the desktop and on mobile devices.

    The application shared online runs in Qt Design Viewer, which is a QML
    viewer that runs in your web browser.

    The startup and compilation time depend on your browser and configuration.
    However, the actual performance of the application once started is
    indistinguishable from the same application running on the desktop.

    The loaded applications remain locally in your browser. No data is uploaded
    into the cloud.

    \section1 Sharing your Application Online

    To share your \QDS application online:

    \list 1
      \li Open the application in \QDS.
      \li Select \uicontrol File > \uicontrol {Share Application Online}.
      \li In the dialog, select \uicontrol Share.
      \image share-online.webp
    \endlist

    In the dialog, you can now open the application in a web
    browser, copy the link to share with others, or manage your shared
    applications.

    \image share-online-manage.webp
*/
