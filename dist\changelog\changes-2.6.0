Qt Creator version 2.6 contains bug fixes and new features.

The most important changes are listed in this document. For a complete
list of changes, see the Git log for the Qt Creator sources that
you can check out from the public Git repository. For example:

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --cherry-pick --pretty=oneline v2.5.2..origin/2.6

General
   * Added a wizard for creating a temporary text file
   * Added a menu for showing and hiding the output pane buttons
   * Added a visual hint for canceled searches (QTCREATORBUG-6820)
   * Fixed the New dialog for dark themes (QTCREATORBUG-7435)
   * Added support for jumping to a line in a specific file to Locator
     (with "+" or ":" appended to the file name, for example "myfile.cpp:41")
   * Fixed that several settings where saved every few seconds
     even without changes (QTCREATORBUG-7718)

Editing
   * Added a context menu for adding and removing UTF-8 bom
   * Added shortcuts for searching for next and previous occurrence
     of selected text without opening the find tool bar (QTCREATORBUG-464)
   * Made resource files searchable (Ctrl+F)
   * Integrated VCS support into the resource editor
   * Added file type icons to the resource editor
   * Added opening of files from the resource editor
   * Added renaming of files from the resource editor
   * Added highlighting of missing files in the resource editor
   * Added support for animated images in the image viewer
   * Fixed problems when closing documents in a split view (QTCREATORBUG-7361)

Managing Projects
   * Introduced "Kits" that supersede the previous "Targets". They bundle
     the settings for the target device, compiler, debugger, Qt version and
     more into a user definable, reusable setting.
   * Moved the debugger setting from tool chains to kits
     and renamed tool chains to compilers
   * Added experimental support for Android (enable the plugin in Help > About Plugins)
   * Added support for QNX
   * Made it possible to disable deploy configurations
   * Added double-clicking of file names in compile errors to open the file
   * Added a Cancel Build button to the Compile Output pane
   * Added CurrentProject::BuildPath variable for external tools (QTCREATORBUG-4885)

QMake Projects
   * Added an action for compiling a single file (QTCREATORBUG-106)
   * Added actions for (re)building and cleaning the current subproject

CMake Projects
   * Added CMake specific context menu items to the CMakeLists.txt editor

Debugging
   * Updated dumpers to internal changes in Qt 5 (structure layout, namespaces)
   * Adjusted state engine to changes in GDB/MI notifications
   * Made all views searchable (Ctrl+F)
   * Made extensive use of HistoryCompleter
   * Consolidated the special start options in Debug > Start Debugging
     after the Kits changes
   * Renamed "Watcher" into "Expression Evaluator"
   * Generalized process listing and attaching facilities
   * Adjusted to changed code generation in MinGW 4.6
   * Added GUI support for temporary breakpoints
   * Added a shortcut (F10) for start-and-stop-at-main
   * Added direct loading of remote core files
   * Added an option to create watch points in the Memory view context menu
   * Added GDB pretty-printers for QFiniteStack, QHash::{const_}iterator,
     std::{map,set}::iterator
   * Added support for IPv6-enabled builds of GDB
   * Improved logging and output pane performance
   * Improved performance of retrieving large arrays of plain data
   * Fixed use of non-xterm terminals (QTCREATORBUG-1633)
   * Fixed use of multi-line breakpoint commands
   * Fixed off-by-one error in the Address field in the Symbols view

Debugging QML/JS
   * Merged the (experimental) QML/JS Inspector plugin into the Debugger:
   * Added the QML object tree to the Expressions window
   * Added Console window to evaluate JS expressions at runtime
   * Added QML specific buttons to the toolbar

Analyzing Code
   * Added an option to shorten template names in function profiler output
   * Simplified the remote start dialogs

C++ Support
   * Fixed lambda formatting issues
   * Added support for variadic arguments (__VA_ARGS__)
   * Added support for raw string literals (QTCREATORBUG-6722)
   * Fixed the display of results when searching for macro usages (QTCREATORBUG-7217)
   * Added highlighting of macro usages
   * Implemented renaming of macro usages (QTCREATORBUG-413)
   * Fixed detection of C++11 features for MSVC

QML/JS Support
   * Added support for adding the file to VCS when moving a component into
     a separate file (QTCREATORBUG-7246)

GLSL Support
   * Fixed a crash on declaration without type (QTCREATORBUG-7548)
   * Fixed a freeze when using the conditional operator (QTCREATORBUG-7517)

Qt Quick Designer

Help
   * Made hiding the navigation side bar in the Help mode possible (QTCREATORBUG-1533)

Qt Designer

Version control plugins
   * Added experimental support for ClearCase (enable the plugin in
     Help > About Plugins)

Git
   * Added a customizable repository browser command
   * Fixed commit and amend when not on a branch
   * Added partial support for Gerrit (Tools > Git > Gerrit)

SVN
   * Fixed project status command when no document is open
   * Fixed completion in the submit editor

FakeVim
   * Added support for smartcase searching
   * Added support for last selection operations
   * Added support for counts in block selections (for example "2vi)", "3da{")
   * Added support for special registers "+ and "*
   * Added selection commands for strings (for example "di'", "ca`")
   * Improved emulation of Vim regexps
   * Fixed :!cmd if there is no selection
   * Fixed handling of "ci(", "di[", "ca{" inside nested blocks
   * Fixed search without matches
   * Fixed pasting text [count] times and in selection
   * Fixed pasting from clipboard
   * Made clipboard data format compatible with Vim

Platform Specific

Mac
   * Added support for fullscreen (Lion and later)
   * Changed the VCS shortcuts to use the Ctrl modifier instead of the Opt modifier
     (the Opt modifier inserts special characters on Mac)
   * Made the shortcut modifiers (for example Cmd+...) searchable in the Filter
     functionality of the keyboard shortcut settings

Linux (GNOME and KDE)
   * Worked around a problem in the KDE file dialog that prevented selecting
     qmake (QTCREATORBUG-7771)

Windows

Symbian Target
   * Removed support for Symbian development because of missing maintainer

Remote Linux Support

Madde
   * Removed generic MeeGo support due to complete irrelevance

Credits go to:
    Aleksey Sidorov
    Alessandro Portale
    Andreas Holzammer
    Andre Hartmann
    André Pönitz
    Aurélien Gâteau
    Aurindam Jana
    axasia
    Bill King
    BogDan Vatra
    Bojan Petrovic
    Bradley T. Hughes
    Campbell Barton
    Casper van Donderen
    Christiaan Janssen
    Christian Kamm
    Christian Kandeler
    Christian Stenger
    cnavarro
    Daniel Molkentin
    Daniel Teske
    David Schulz
    Dmitry Savchenko
    Eike Ziller
    Erik Verbruggen
    Fawzi Mohamed
    Flex Ferrum
    Francois Ferrand
    Franklin Weng
    Friedemann Kleint
    hluk
    Hugues Delorme
    Jarek Kobus
    Jędrzej Nowacki
    Jörg Bornemann
    Jonathan Liu
    Juei-ray Tseng
    Juhapekka Piiroinen
    Kaffeine
    Kai Köhne
    Kevin Krammer
    Karsten Heimrich
    Knut Petter Svendsen
    Konstantin Ritt
    Konstantin Tokarev
    Leandro Melo
    Leena Miettinen
    Lukas Geyer
    Lukas Holecek
    Marc Mutz
    Marco Bubke
    Marius Storm-Olsen
    Martin Aumüller
    Mathias Hasselmann
    Mehdi Fekari
    Montel Laurent
    Morten Johan Sorvig
    Nicolas Arnaud-Cormos
    Nikolai Kosjar
    Orgad Shaneh
    Oswald Buddenhagen
    Oto Magaldadze
    Peter Kümmel
    Pierre Rossi
    Przemyslaw Gorszkowski
    raidsan
    Robert Löhning
    Ryan May
    Sergey Belyashov
    Sergey Shambir
    Sergio Ahumada
    Simjees Abraham
    Stephen Kelly
    Takumi Asaki
    Theo J.A. de Vries
    Thiago Macieira
    Thomas Hartmann
    Thorbjørn Lindeijer
    Tim Jenssen
    Tobias Hunger
    Tobias Nätterlund
    Tommi Asp
    Tyler Mandry
    Vladislav Navrocky
    Yuchen Deng
