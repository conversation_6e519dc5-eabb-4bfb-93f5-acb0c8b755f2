// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// Note: The \page value is hard-coded as a link in Qt Bridge for Sketch.

/*!
    \previouspage qtbridge-xd-using.html
    \page sketchqtbridge.html
    \nextpage qtbridge-sketch-setup.html

    \title Exporting Designs from Sketch

    You can use \QBSK to export designs from Sketch to \e {.metadata}
    format that you can \l{Importing 2D Assets}{import} to projects in \QDS.

    \image studio-sketch-export.png

    The following topics describe setting up and using \QBSK:

    \list

        \li \l{Setting Up Qt Bridge for Sketch}

            You must install Sketch and the \QBSK export tool before you can use
            the tool to export designs.

        \li \l{Using Qt Bridge for Sketch}

            To get the best results when you use \QBSK to export designs from
            Sketch, you should follow the guidelines for working with Sketch and
            organizing your assets.
    \endlist

    \include qtbridge-tutorial-links.qdocinc qtsketchbridge tutorials
*/
