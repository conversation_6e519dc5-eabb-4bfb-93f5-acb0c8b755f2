// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-editing-easing-curves.html
    \previouspage studio-timeline.html
    \nextpage qtquick-production-quality-animation.html

    \title Editing Easing Curves

    Easing curves define the progress of animation to make motion appear more
    natural because real objects don't usually move at a constant speed nor
    start and stop instantly. You can add and edit easing curves for nonlinear
    interpolation to make components appear to pick up speed, slow down, or
    bounce back at the end of an animation.

    You can attach easing curves to:

    \list
        \li Keyframes in timeline animations
        \li Transitions
        \li Property animation component instances
    \endlist

    You can use \uicontrol {Easing Curve Editor} to select and edit easing
    curves.

    \section1 Selecting Easing Curves

    You can use the preset curves or modify them by dragging the curve handles
    around. You can add points to the curve and drag them and the point handles
    to modify the curve. When you are happy with the curve, you can save it as
    a custom curve. For more information about easing curve types, see the
    technical documentation for \l [QML] {PropertyAnimation}{easing curves}.

    \image studio-easing-curve-editor.png "Easing Curve Editor"

    To select an easing curve:

    \list 1
        \li Select an easing curve in the \uicontrol Presets tab.
        \li In the \uicontrol {Duration (ms)} field, select the
            duration of the easing function in milliseconds.
        \li Select \uicontrol Preview to preview the curve.
        \li Select \uicontrol OK to attach the easing curve and
            return to the view where you are adding the curve.
    \endlist

    To zoom into and out of the easing curve editor, use the mouse
    roller. To reset the zoom factor, right-click in the picker and
    select \uicontrol {Reset Zoom}.

    \section1 Easing Curves in Timeline Animations

    For timeline animations, you can also use the more advanced
    \l {Curves} view that shows the interpolated values of an animated
    property over the \l{Creating Timeline Animations}{animation} range.

    The animation curves present a more readable view of the animation by
    showing the effective values of the animated properties over the animation
    range. In order to do so, they need to be converted to and from easing
    curves. This comes with the following side effects:

    \list
        \li Interpolation between identical current and previous keyframe values
            is meaningless regardless of what the easing curve looks like.
        \li Some easing curves are not trivially mappable to animation curves
            and are therefore painted in a different color and without handles.
    \endlist

    \section2 Attaching Easing Curves to Keyframes

    To attach easing curves to keyframes:

    \list 1
        \li Right-click a keyframe in \l Timeline and select
            \uicontrol {Easing Curve Editor} in the context menu.
        \li Select an easing curve, as described in \l{Selecting Easing Curves}.
    \endlist

    When you attach easing curves to keyframes, the shape of the
    \l{keyframe_marker}{keyframe marker} on a keyframe track in
    \l Timeline changes from \inlineimage icons/keyframe_linear_active.png
    to a marker that describes the type of the selected easing curve.

    \section1 Attaching Easing Curves to Transitions

    To attach easing curves to transitions:

    \list 1
        \li In \l{Transitions}, select the \inlineimage icons/curve_editor.png
            (\uicontrol {Easing Curve Editor}) button.
        \li Select an easing curve, as described in \l{Selecting Easing Curves}.
    \endlist

    \section1 Attaching Easing Curves to Property Animations

    To attach easing curves to property animations:

    \list 1
        \li In \l Navigator, select an \l{Animations}{Animation} component
            instance.
        \li In \l Properties, select the \inlineimage icons/curve_editor.png
            (\uicontrol {Easing Curve Editor}) button.
        \li Select an easing curve, as described in \l{Selecting Easing Curves}.
    \endlist

    \section1 Customizing Easing Curves

    To customize easing curves:

    \list 1
        \li In \uicontrol {Easing Curve Editor}, select an easing curve in
            the \uicontrol Presets tab.
        \li Drag the curve handles to modify the curve.
        \li Right-click in the editor, and select \uicontrol {Add Point} to add
            points to the curve.
        \li Drag the points or the point handles to modify the curve. If the
            curve becomes invalid, it turns red in the editor and the
            \uicontrol Save button is disabled.
        \li Select \uicontrol Save to save your changes to the curve.
        \li In the \uicontrol Name field, enter a name for the custom curve,
            and then select \uicontrol OK to save the curve in the
            \uicontrol Custom tab.
    \endlist

    To paste easing curve definitions to \uicontrol {Easing Curve Editor} as
    text, select the \uicontrol Text tab.
*/
