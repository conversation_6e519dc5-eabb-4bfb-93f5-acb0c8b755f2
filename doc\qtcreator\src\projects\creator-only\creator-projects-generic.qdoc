// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-projects-autotools.html
    \page creator-project-generic.html
    \nextpage creator-project-nimble.html

    \title Setting Up a Generic Project

    Generic project support allows you to use \QC as a code editor. You
    can change the way your project is built by modifying the \c make command
    in the \uicontrol{Projects} mode under \uicontrol{Build Settings}.

    When you import a project, \QC creates the following files that
    allow you to specify which files belong to your project and which include
    directories or defines you want to pass to your compiler:
    \tt{.files}, \tt{.includes}, and \tt{.config}.

    \section1 Importing a Generic Project

    To import an existing generic project:

    \list 1

        \li Select \uicontrol File > \uicontrol{New Project} > \uicontrol{Import Project}
            > \uicontrol{Import Existing Project}.

        \li In \uicontrol{Import Existing Project}, enter the project name
            and select the location of the project file you want to import.

           \QC automatically generates the following files in the
           project directory:

           \list

               \li  \l{Specifying Files}{.files}

               \li  \l{Specifying Include Paths and Framework Paths}{.includes}

               \li  \l{Specifying Defines}{.config}

               \li  .creator

               \li  \l{Forwarding Flags to Clang Code Model}{.cflags}

               \li  \l{Forwarding Flags to Clang Code Model}{.cxxflags}

           \endlist

    \endlist

    When the project is successfully imported, \QC creates the project
    tree in the sidebar.

    After importing a generic project into \QC, open it by selecting the
    \tt{.creator} file.

    \section1 Working with Generic Project Files

    For a generic project, you have to manually specify which files belong to
    your project and which include directories or defines you want to pass to
    your compiler.

    \section1 Specifying Files

    The list of files for a generic project is specified in the \tt{.files}
    file. When you first create a generic project, \QC adds any files it
    recognizes to your project.

    To add or remove files, edit the \tt{.files} file in \QC.
    \QC recreates your project tree when you save the \tt{.files} file.
    Alternatively, you can add and remove files or directories using the
    context menu in the project tree.

    If you frequently need to update the \tt{.files} file, you can do so
    efficiently by using a script that updates the file for you. If the file
    is modified externally, you have to restart \QC for the changes to
    take effect.

    To update the \tt{.files} on the \uicontrol Git repository use the following
    script:

    \code
    git ls-files "*.cpp" "*.h" > MyProject.files
    \endcode

    \section2 Precompiled Headers

    To use precompiled headers in a generic project, add the \c pch tag after
    a file path in the \c .files file, separated by the pipe character (|).
    For example:

    \badcode
    src/pch.h|pch
    \endcode

    \section1 Specifying Include Paths and Framework Paths

    The include paths are specified in the \tt{.includes} file, one include
    path per line. The paths can be either absolute or relative to the
    \tt{.includes} file.

    Lines starting with "-F" are interpreted as framework paths.

    \section1 Specifying Defines

    The defines are specified in the \tt{.config} file. The \tt{.config} file is
    a regular C++ file, prepended to all your source files when they are parsed.
    Only use the \tt{.config} file to add lines as in the example below:

    \code
    #define NAME value
    \endcode

    \section1 Forwarding Flags to Clang Code Model

    The \c {.cxxflags} and \c {.cflags} files contain command line flags for the
    Clang code model on a single line.

    For example, specify the \c {-std=c++11} to set the language version
    for parsing as C++11.

    \section1 Providing Deployment Information

    If you want to run your application on a remote Linux device,
    you first need to deploy your executable and possibly other files.
    \QC does that for you automatically if you provide the necessary
    information. This works the same way as explained for CMake in
    \l {Deploying to Remote Linux},
    except that you also need to include your application binary in the list.

    \section1 Creating a Run Configuration

    \QC cannot automatically determine which executable to run.

    In the \uicontrol{Projects} mode under \uicontrol{Run Settings}, define the executable
    file to run:

    \list 1

        \li Click \uicontrol Add and select \uicontrol{Custom Executable}.

        \li Define the configuration name, the location of the executable, any
            additional arguments and the working directory.

    \endlist

*/
