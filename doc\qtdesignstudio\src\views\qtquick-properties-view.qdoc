// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-properties-view.html
    \previouspage qtquick-navigator.html
    \nextpage qtquick-connection-view.html

    \title Properties

    The \uicontrol Properties view displays all the properties of the selected
    \l{glossary-component}{component}. The properties are grouped by type. The
    top part of the view displays properties that are common to all components,
    such as component type, ID, name, geometry, and visibility.

    \image qtquick-item-properties-common.png "Basic component properties"

    The bottom part of the view displays properties that have been defined for
    the component type. For example, the following image displays the predefined
    properties you can set for \l{basic-rectangle}{Rectangle} and \l Text
    components.

    \image qmldesigner-element-properties.png "Rectangle and Text properties"

    \section1 Custom Properties

    Custom Properties are properties that the user has added to the component.

    There are two types of custom properties:

    \table
      \header
        \li Custom Property Type
        \li Description
      \row
        \li Local Custom Property
        \li A property that has been added for a \l{Preset Components}{preset component}.
      \row
        \li Exposed Custom Property
        \li A property that has been added inside a component.
    \endtable

    \image custom-properties.png

    \section1 Summary of Properties View Buttons

    The following table lists the \uicontrol Properties view buttons:

    \table
    \header
        \li Icon
        \li Tooltip
        \li Read More
    \row
        \li \inlineimage icons/alias.png
        \li Adds a property alias that you can use from outside of the
            component for the root component. You can use a menu item
            in the actions menu to add property aliases for property
            values of child components.
        \li \l{Adding Property Aliases}
    \row
        \li \inlineimage icons/action-icon.png
        \li Opens a menu with actions for:
            \list
                \li Resetting property values to their default values
                \li Setting property bindings
                \li Creating property aliases
                \li Inserting keyframes for timeline animations
            \endlist
        \li
        \list
                \li \l{Viewing Changes in Properties}
                \li \l{Adding Bindings Between Properties}
                \li \l{Adding Property Aliases}
                \li \l{Setting Keyframe Values}
        \endlist
    \row
        \li \inlineimage icons/action-icon-binding.png
        \li Indicates that the value of the property is bound to the value
            of another property. Opens the same menu as the action icon.
        \li \l{Adding Bindings Between Properties}

    \endtable

    \section1 Viewing Changes in Properties

    The default values of properties are displayed in white color, while the
    values that you specify explicitly are highlighted with blue color. In
    addition, property changes in \l{Working with States}{states} are highlighted
    with blue.

    This allows you to easily see which values are set in the component
    file for this component instance and which values are default
    characteristics of a component.

    When editing states, you can easily see which values are explicitly set in
    the current state and which values are derived from the base state.

    The following images illustrate this. In the base state, the
    \uicontrol Position and \uicontrol Size values are explicitly set
    and highlighted.

    \image qmldesigner-properties-explicit-base.png "Explicitly set properties"

    In \uicontrol State1, only \uicontrol Position is explicitly set and
    highlighted.

    \image qmldesigner-properties-explicit-state1.png "Explicitly set properties"

    Resetting a property sets it back to the default value and removes the value
    from the component file.

    \note As a result, all boolean values can be visualized in four different
    ways.

    For example, visibility can be visualized as follows:

    \table
        \row
            \li \image qmldesigner-boolean-true.png
            \li TRUE
            \li The component is visible by default. The visibility might be
                overridden by the visibility set in the base state.
        \row
            \li \image qmldesigner-boolean-true-blue.png
            \li TRUE (highlighted)
            \li The component is explicitly set to visible.
        \row
            \li \image qmldesigner-boolean-false.png
            \li FALSE
            \li The component is hidden by default. The visibility might be
                overridden by the visibility set in the base state.
        \row
            \li \image qmldesigner-boolean-false-blue.png
            \li FALSE (hightlighted)
            \li The component is explicitly set to hidden.
    \endtable

    \section1 Multiselection

    To modify the values of common properties of multiple components
    simultaneously, select the components in the \l Navigator, \l {2D}
    or \uicontrol{3D} view:

    \list
        \li On Windows, press and hold \key Ctrl and \key Shift, and then click
            the components to select them.
        \li On \macos, press \key Shift to select a range of components or
            \key Cmd to select multiple single components.
    \endlist

    You can \l{Locking Components}{lock other components} in
    \uicontrol Navigator to make multiselection easier.

    \section1 Getting Help for Properties

    \list
        \li For information about setting common component properties, see
            \l{Specifying Component Properties}.
        \li For more information about the properties available for a
            component, press \key {F1} or see \l{Component Types}
            and \l{Scalable Layouts}.
    \endlist
*/
