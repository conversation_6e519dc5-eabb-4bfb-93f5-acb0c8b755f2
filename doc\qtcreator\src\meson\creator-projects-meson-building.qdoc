// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only
/*!
    \previouspage creator-build-settings-qbs.html
    \page creator-build-settings-meson.html
    \nextpage creator-build-settings-incredibuild.html

    \title Meson Build Configuration

    \image qtcreator-meson-build-settings.png "Meson build settings"

    Meson builds projects in the directory specified in the
    \uicontrol {Build directory} field.

    Build settings are grouped by category. You can modify all settings,
    except \c backend, which is forced to Ninja, \c {buildtype}, \c debug,
    and \c optimization to ensure compatibility with \QC.

    To modify a setting, double-click it. Modified settings are formatted in
    bold until you select \uicontrol {Apply configuration changes} to apply
    them. This triggers \c {meson configure}. If problems arise, select
    \uicontrol {Wipe Project} to fix the build directory configuration.

    Meson supports cross-compiling in addition to native building. \QC
    generates a native build file for you. To use a custom native file or a
    cross file instead, specify the file name in \uicontrol Parameters.
    For example, \c {--cross-file cross_file.txt}.

    For more information about using Meson, see \l{Setting Up Meson}.

    \section1 Meson Build Steps

    \QC builds Meson projects by running \c {ninja -v target}.

    You can add arguments and targets for the build command in
    \uicontrol {Build Steps}.

    \image qtcreator-meson-build-steps.png "Meson build steps"

    The build errors and warnings are parsed and displayed in \l Issues.

    \section1 Meson Clean Steps

    When building with Meson, you can add arguments and targets for the clean
    command in \uicontrol {Clean Steps}.

    \image qtcreator-meson-clean-steps.png "Meson clean steps"

    The build errors and warnings are parsed and displayed in \uicontrol Issues.

*/
