Qt Creator version 2.2 contains bug fixes and new features.

The most important changes are listed in this document. For a complete
list of changes, see the Git log for the Qt Creator sources that
you can check out from the public Git repository. For example:

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --cherry-pick --pretty=oneline origin/2.1...origin/2.2

General
   * Add support for running user defined external tools (Tools > External)
   * Move tool chain definitions out of Qt versions:
      * You can now define tool chains in Tools > Options > Tool Chains
      * Qt Creator now supports more than one instance of a tool chain
   * Add support for customizing MIME types by editing patterns and
     magic headers
   * Integrate the memcheck tool from Valgrind for Linux and Mac OS X
   * Create the infrastructure to set up "soft dependencies" between plugins
   * Add the qtpromaker tool that has the same functionality as
     "qmake -project", but is much faster. It provides a way to easily set up
     dummy .pro files that you need to quickly inspect and navigate "alien"
     codebases with Qt Creator
   * Document Qt Creator coding style

Editing
   * Add an Inkpot color scheme
   * Add UTF-8 BOM support
   * Move the Open in External Editor item to Tools > External > Text
   * Add the Expand/Collapse All context menu item to the Outline pane
   * Add support for user-defined macros (contributed by <PERSON>)
   * Add a snippet editor with syntax highlighting, indentation, and basic
     auto-completion for C++ and QML
   * Add GLSL support with:
     - Syntax checking
     - Highlighting
     - Code completion
   * Binary editor:
     - Visualize data differences after updates
     - Fix selection if cursor is left of anchor
     - Allow copying also if selection is "backwards"
     - Do not draw blinking cursor when there is a selection
     FakeVim:
     - Implement Ctrl-N/Ctrl-P
     - Implement 's' in visual block mode
     - Fix Alt-Gr handling on Windows
     - Handle more Ctrl-W + <x> commands

Managing Projects
   * Improve the robustness of qmake project support against syntax errors
   * Change the handling of command line arguments of processes that are
     started from build and run configurations:
     - Argument lists use native shell syntax; this affects quoting and
       environment variable expansion most
     - The SOURCEDIR and BUILDDIR environment variables were replaced
       with %{sourceDir} and %{buildDir} variables
     - This affects working directories and executable paths as well
   * Support syntax highlighting and completion for qmake project files
   * CMake: Add UI completion, changes in .ui files are picked up without
     a rebuild
   * Specify text editor behavior settings per project
   * New project and file wizards:
     - Support new keywords %MONTH% and %DAY% for C++ template file
     - Add wizards for creating OpenGL fragment and vertex shaders
     - Add a wizard for creating HTML5 based applications
     - Add a wizard for creating JavaScript files, QTCREATORBUG-3070

Debugging
   * Rewrite debugging engine using the Microsoft Console Debugger (CDB)
     for debugging executables built with Microsoft Visual Studio,
     resulting in significant speed-up and better display of data
     types, supporting both 32-bit and 64-bit versions of the
     Debugging Tools for Windows
   * Make it possible to pin debugger tooltips
   * Rework global architecture to allow multiple debugging sessions (for
     different projects, combined QML/C++ debugging, project snapshots,
     and so on)
   * Rework breakpoint handling: add special breakpoint types for break
     on throw and break on main, as well as data breakpoints
   * Introduce combined QML/C++ debugging (combined stack, cross-step) (GDB only)
   * Rework per-type and per-item selection of display formats
   * Add dumpers for QTextCursor, QTextDocument, __m128, QScriptValue
     QBasicAtomicPointer, bit fields, boost::shared_ptr (GDB only)
   * Improve dumpers for QRegion, QVariant (custom types), QSharedPointer,
     QMultiMap (QTCREATORBUG-3122), QObject, QWeakPointer
   * Make alphabetic structure member sorting optional
   * Make dumpers robust in case of missing debug information
   * Improve Python dumper profiling support
   * Improve stepping performance
   * Enable breakpoint setting from a disassembler view, QTCREATORBUG-3667
   * Fix display of shadowed variables
   * Fix display of data containing quote characters, QTCREATORBUG-3084
   * Fix display of C-style typedef struct X { ... } X;
   * Fix stack/break view updates after manual module loading, QTCREATORBUG-3427
   * Fix Jump To/Run To when in instruction-wise mode
   * Fix the watcher handling
   * Allow re-running debugger engines, QTCREATORBUG-3073
   * Robust process control after -exec-interrupt errors, QTCREATORBUG-2850
   * Robust detection of Qt-in-namespace builds
   * Handle "live updates" in the memory view
   * Make memory view writable (GDB only)
   * Improve starting debugger session directly from the command line
   * Improve Debugging Helpers dialog

Debugging QML/JS
   * Compile qmljsdebugger code as static library, and link it according to the
     setting in the qmake step (replaces QMLJSDEBUGGER_PATH / QMLJSDEBUGGER define)
   * Qt Quick Application: Allow debugging also in release builds, improve error
     diagnostics
   * GUI cosmetics in JS debugger: Show stack frame level, only valid
     line numbers in stack view, do not show function code, show dummy
     child entry for empty arrays
   * Added QML tooltips for property inspection at debug time
   * Extend QML Observer for inspection and tweaking of item properties
   * Add navigation of the object tree in the QML Observer through a crumble path
   * Add the Show Application on Top option
   * Move the toolbar from the Observer tab to the main toolbar
   * Support setting of breakpoints in .qml files embedded as qrc resources
   * Improve linking of QML errors in Application Output pane

C++ Support
   * Add a refactoring action for implementing setters and getters for
     a Q_PROPERTY
   * Add documentation for refactoring actions
   * Fix switch statement indentation for GNU and Whitesmiths
     style, QTCREATORBUG-2994
   * Fix indentation of labels
   * Support highlighting for virtual functions
   * Navigate to correct overloaded function or method

QML/JS Support
   * Add local-file completion for URL properties, QTCREATORBUG-2619
   * Add warning about invalid files in URL properties
   * Add file name completion in imports, QTCREATORBUG-3067
   * Add a locator filter for JS functions, QTCREATORBUG-2607
   * Add JSlint-style warnings about common JS pitfalls, QTCREATORBUG-3071
   * Add completion for attached properties like Component.onCompleted
   * Allow QML modules to ship predumped type description files
   * Reenable instantiating component scope detection, QTCREATORBUG-2835
   * Improve error reporting of failed plugin dumps
   * Complete a trailing '{' for 'A on b' object bindings, QTCREATORBUG-2642
   * Highlight capitalized types, QTCREATORBUG-2658
   * Re-dump a plugin if the shared library/DLL changes, QTCREATORBUG-3047
   * Build qmldump in debug and release version on Windows, QTCREATORBUG-3549
   * Change qmldump to output QML instead of XML
   * Fix automatic '.' import to also pick up QML modules, QTCREATORBUG-3768
   * Fix code model update when files are renamed, QTCREATORBUG-2986
   * Fix incorrect completion when brace is on a separate line, QTCREATORBUG-2658
   * Fix quote autoinsertion in string literals, QTCREATORBUG-2155, QTCREATORBUG-3244
   * Fix '{' not being an electric character, QTCREATORBUG-3405
   * Fix indentation of 'foo: Namesp.Type {', QTCREATORBUG-3371
   * .qmlproject format: Allow specifying the 'mainFile' qml file, bump version to 1.1
   * Qt Quick UI project: allow user to change run environment

Help

Platform Specific

Mac

Linux (GNOME and KDE)

Windows

Symbian Target
   * Add support for the new on-device debugging agent (CODA)

Maemo Target
   * Add MeeGo support
   * Add a wizard for configuring connections to Maemo and MeeGo devices
   * Add a wizard for publishing applications to Fremantle/extras-devel
   * Create desktop files on demand
   * Allow disabling packaging for Fremantle

Qt Designer

Version control plugins
   * Support Bazaar (contributed by Hugues Delorme)

Additional credits go to:
   * The Inkpot color scheme was based on the Vim color scheme by
     Ciaran McCreesh, adapted for Qt Creator by Jan Kundrát and
     subsequently updated by Campbell Barton
   * Hugues Delorme for the Bazaar plugin which he continues to improve
   * Nicolas Arnaud-Cormos for the Macro plugin
