// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-editor-options-text.html
    \page creator-editor-fakevim.html
    \nextpage creator-language-servers.html

    \title Using FakeVim Mode

    In the \uicontrol FakeVim mode, you can run the main editor in a manner
    similar to the Vim editor. To run the editor in the \uicontrol FakeVim
    mode, select \uicontrol Edit > \uicontrol Advanced >
    \uicontrol {Use Vim-style Editing} or press \key {Alt+V,Alt+V}.

    \section1 Supported Modes and Commands

    In the \uicontrol FakeVim mode, most keystrokes in the main editor will be
    intercepted and interpreted in a way that resembles Vim. Most of the
    supported commands can be followed by a motion command or executed in visual
    mode, or they work with registers or can be prefixed with a number of
    repetitions.

    The following sections describe the commands emulated in the supported modes
    and how they diverge from Vim in functionality:

    \list
        \li Normal
        \li Visual
        \li Command line (:)
        \li Insert and replace
    \endlist

    For more information on using Vim, see \l{http://www.vim.org/docs.php}
    {Documentation} on the Vim web site.

    \section2 Normal and Visual Modes

    \list
        \li Basic movement, such as \c h/j/k/l, \c <C-U>, \c <C-D>, \c <C-F>,
            \c <C-B>, \c gg, \c G, \c 0, \c ^, \c $
        \li Word movement, such as \c w, \c e, \c b
        \li \e Inner/a movement, such as \c ciw, \c 3daw, ya{
        \li \c f and \c t movement
        \li \c [ and \c ] movement
        \li { and } paragraph movement
        \li Delete/change/yank/paste with register
        \li Undo and redo
        \li \c <C-A> and \c <C-X> increase or decrease a number in decimal,
            octal, or hexadecimal format (for example \c 128<C-A> on or before
            \c "0x0ff" changes it to \c "0x17f")
        \li \c . repeats the last change
        \li \c /search, \c ?search, \c *, \c #, \c n, \c N - most of regular
            expression syntax is used in Vim except that \c \< and \c \> are
            the same as \c {\b} in QRegExp
        \li \c @ and \c q (macro recording and execution) special keys are saved
            as \c <S-Left>
        \li Marks
        \li \c gv goes to last visual selection; can differ if text is edited
            around it
        \li Indentation using \c =, \c <<, \c >>, with movement, count, and in
            visual mode
        \li \e {to upper/lower}, such as \c ~, \c gU, \c gu
        \li \c i, \c a, \c o, \c I, \c A, and \c O enter insert mode
        \li Scroll window, such as \c zt, \c zb, \c zz
        \li Wrap line movement, such as \c gj, \c gk, \c g0, \c g^, \c g$
    \endlist

    \section2 Command Line Mode

    \list
        \li \c :map, \c :unmap, \c :inoremap, and so on
        \li \c :source provides very basic line-by-line sourcing of vimrc files
        \li \c :substitute substitutes an expression in a range
        \li \c :'<,'>!cmd filters through an external command (for example,
            sorts the lines in a file with \c :%!sort)
        \li \c :<range>sor[t][!]
        \li \c :.!cmd inserts the standard output of an external command
        \li \c :read
        \li \c :yank, \c :delete, \c :change
        \li \c :move, \c :join
        \li \c :20 goes to an address
        \li \c :history
        \li \c :registers, \c :display
        \li \c :nohlsearch
        \li \c :undo, \c :redo
        \li \c :normal
        \li \c :<, \c :>
        \li \c{set formatoptions=}, see \c{:h fo-table} in the Vim documentation.
            Currently supported letters: \c fo-j
    \endlist

    \section2 Plugin Emulation

    FakeVim also emulates some popular vim plugins. To enable plugin emulation
    for particular plugins, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol FakeVim > \uicontrol General > \uicontrol {Plugin Emulation}.

    \image qtcreator-fakevim-options-general-plugin-emulation.png "FakeVim Plugin Emulation preferences"

    Currently emulated plugins:
    \list
        \li \l{https://github.com/tpope/vim-commentary}{vim-commentary}: \c gc
        action to comment code regions. For example, \c gcc, \c gc2j, \c gcip
        \li
        \l{https://github.com/vim-scripts/ReplaceWithRegister}{ReplaceWithRegister}:
        \list
            \li \c [count]["x]gr{motion} to replace \c {motion} with the contents of
        register \c x.
            \li ["x]grr to replace the current line.
        \endlist
        \li \l{https://github.com/vim-scripts/argtextobj.vim}{argtextobj.vim}:
        Defines the \c ia and \c aa text objects for function parameters.
        \li \l{https://github.com/tommcdo/vim-exchange}{vim-exchange}:
        A text exchange operator for vim.
        \li \l{https://github.com/tpope/vim-surround}{vim-surround}:
        Adds mappings for deleting, adding and changing surroundings.
    \endlist

    \section2 Insert Mode

    \list
        \li \c <C-O> executes a single command and returns to insert mode
        \li \c <C-V> inserts a raw character
        \li \c <insert> toggles replace mode
    \endlist

    \section2 Options

    Use \c {:set ...} to set the options listed in the following table:

    \table
    \header
        \li Long Name
        \li Short Name
        \li Arguments
    \row
        \li \c autoindent
        \li \c ai
        \li
    \row
        \li \c backspace
        \li \c bs
        \li \c indent, \c eol, \c start
    \row
        \li \c blinkingcursor
        \li \c bc
        \li
    \row
        \li \c clipboard
        \li \c cb
        \li
    \row
        \li \c expandtab
        \li \c et
        \li
    \row
        \li \c hlsearch
        \li \c hls
        \li
    \row
        \li \c ignorecase
        \li \c ic
        \li
    \row
        \li \c incsearch
        \li \c is
        \li
    \row
        \li \c iskeyword
        \li \c isk
        \li A combination of the following characters: \c @, \c 48-57, \c _,
            \c 192-255, \c a-z, \c A-Z
    \row
        \li \c relativenumber
        \li \c rnu
        \li
    \row
        \li \c scrolloff
        \li \c so
        \li
    \row
        \li \c shiftwidth
        \li \c sw
        \li
    \row
        \li \c showcmd
        \li \c sc
        \li
    \row
        \li \c smartcase
        \li \c scs
        \li
    \row
        \li \c smartindent
        \li \c si
        \li
    \row
        \li \c smarttab
        \li \c sta
        \li
    \row
        \li \c startofline
        \li \c sol
        \li
    \row
        \li \c tabstop
        \li \c ts
        \li
    \row
        \li \c tildeop
        \li \c top
        \li
    \row
        \li \c usecoresearch
        \li \c ucs
        \li
    \row
        \li \c wrapscan
        \li \c ws
        \li
    \endtable

    \section2 Vimrc Example

    \code
    " highlight matched
    set hlsearch
    " case insensitive search
    set ignorecase
    set smartcase
    " search while typing
    set incsearch
    " wrap-around when searching
    set wrapscan
    " show pressed keys in lower right corner
    set showcmd
    " tab -> spaces
    set expandtab
    set tabstop=4
    set shiftwidth=4
    " keep a 5 line buffer for the cursor from top/bottom of window
    set scrolloff=5
    " X11 clipboard
    set clipboard=unnamed
    " use ~ with movement
    set tildeop

    " mappings
    nnoremap ; :
    inoremap jj <Esc>

    " clear highlighted search term on space
    noremap <silent> <Space> :nohls<CR>

    " reselect visual block after indent
    vnoremap < <gv
    vnoremap > >gv

    " MOVE LINE/BLOCK
    nnoremap <C-S-J> :m+<CR>==
    nnoremap <C-S-K> :m-2<CR>==
    inoremap <C-S-J> <Esc>:m+<CR>==gi
    inoremap <C-S-K> <Esc>:m-2<CR>==gi
    vnoremap <C-S-J> :m'>+<CR>gv=gv
    vnoremap <C-S-K> :m-2<CR>gv=gv
    \endcode

    \section1 Mapping FakeVim Commands

    To map commands entered on the \uicontrol FakeVim command line to
    \QC functions, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol FakeVim > \uicontrol {Ex Command Mapping}.
    Enter a string in the \uicontrol Filter field to search for a specific
    \QC function.

    \image qtcreator-fakevim-options-ex-command-mapping.png "FakeVim Ex Command Mapping preferences"

    Select a function in the list, and enter a string that will trigger the
    function in the \uicontrol {Regular expression} field. You can view the
    trigger expression in the \uicontrol {Ex Trigger Expression} field. To
    remove the trigger expression, select \uicontrol Reset.

    To reset the trigger expressions for all functions, select
    \uicontrol {Reset All}.

    To map \e {user commands} to keyboard shortcuts, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol FakeVim >
    \uicontrol {User Command Mapping}. The user command mapped to the shortcut
    is executed by FakeVim as if you were typing it (as when replaying a macro).

    \image qtcreator-fakevim-options-user-command-mapping.png "FakeVim User Command Mapping preferences"

    \section1 Setting FakeVim Preferences

    To make changes to the Vim-style settings, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol FakeVim > \uicontrol General.

    \image qtcreator-fakevim-options.png "FakeVim preferences"

    To preselect the indentation settings specified for the text editor, select
    \uicontrol {Copy Text Editor Settings}. To preselect the Qt coding style,
    select \uicontrol {Set Qt Style}. To preselect a simple indentation style,
    select \uicontrol {Set Plain Style}. You can then change any of the
    preselected settings.

    To use a Vim-style color scheme, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol {Fonts & Color}.
    In the \uicontrol {Color Scheme} list, select \uicontrol {Vim (dark)}.

    \section1 Quitting FakeVim Mode

    To quit the FakeVim mode, deselect \uicontrol Edit > \uicontrol Preferences >
    \uicontrol FakeVim > \uicontrol {Use FakeVim} or press \key {Alt+V,Alt+V}.

    You can temporarily escape FakeVim mode to access the normal \QC keyboard
    shortcuts like \key {Ctrl-R} for \uicontrol Run by first pressing the comma
    key (\key {,}).
*/
