// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-finding-overview.html
    \if defined(qtdesignstudio)
    \previouspage creator-diff-editor.html
    \else
    \previouspage creator-clang-codemodel.html
    \endif

    \nextpage creator-editor-finding.html

    \title Finding

    \list

        \li \l{Finding and Replacing}

            The incremental search highlights the matching strings in the
            window while typing and the advanced search enables you to
            search from currently open projects or files on the file system.
            You can conduct incremental and advanced searches in parallel.

            In addition, you can search for symbols when you want to
            refactor code.

        \li \l{Searching with the Locator}

            The locator provides one of the easiest ways in \QC to browse
            through projects, files, classes, functions, documentation and
            file systems.

    \endlist

*/
