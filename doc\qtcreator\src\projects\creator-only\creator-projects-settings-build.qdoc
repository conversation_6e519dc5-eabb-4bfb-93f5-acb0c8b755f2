// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-debuggers.html
    \page creator-build-settings.html
    \nextpage creator-build-settings-cmake.html

    \title Specifying Build Settings

    Different build configurations allow you to quickly switch between
    different build settings. By default, \QC creates \e debug, \e release, and
    \e profile build configurations. A debug build contains additional
    debug symbols that you need for debugging the application but that you
    can leave out from the release version. Generally, you use the debug
    configuration for testing and the release configuration for creating
    the final installation file.

    If you selected CMake as the build system for the project, you can
    use a \e {minimum size release} build configuration to create the
    final installation file. It is a release build that makes the size
    of the binary package as small as possible, even if this makes the
    application slower.

    A profile build (which is called \e {release with debug information}
    when using CMake) is an optimized release build that is delivered
    with separate debug information. It is best suited for analyzing
    applications.

    \section1 Managing Build Configurations

    Specify build settings in \uicontrol Projects > \uicontrol {Build & Run}
    > \uicontrol Build > \uicontrol {Build Settings}.

    \image qtcreator-build-configurations.png "Build Settings"

    To add a new build configuration, click \uicontrol Add and select the type of
    configuration you would like to add. The options you have depend on the
    build system that you selected for the project. You can add as many build
    configurations as you need. You can also select \uicontrol Clone to
    add a build configuration that is based on the currently selected one.

    Select \uicontrol Rename to give the currently selected build configuration
    a new name.

    To delete the build configuration currently selected, click \uicontrol Remove.

    \section1 Editing Build Configurations

    Select the build configuration to edit in the
    \uicontrol {Edit build configuration} field.

    The available build settings depend on the build system that you selected
    for the project:

    \list
        \li \l{CMake Build Configuration}{CMake}
        \li \l{qmake Build Configuration}{qmake}
        \li \l{Qbs Build Configuration}{Qbs}
        \li \l{Meson Build Configuration}{Meson}
        \li \l{IncrediBuild Build Configuration}{IncrediBuild}
    \endlist

    This topic describes the build settings at a general level.

    For more information about debugging Qt Quick projects, see
    \l{Setting Up QML Debugging}.

    \section1 Starting External Processes

    \QC executes external processes to accomplish tasks such as building
    and running applications. To execute the processes, \QC uses shell
    commands that are native to the system. It constructs the commands from
    an executable name and optional command line arguments.

    The executable name is specified in the executable fields: \uicontrol qmake,
    \uicontrol Make, \uicontrol Command, or \uicontrol Executable. It is either
    derived from the project or specified manually. When you specify executables
    manually, you can reference environment variables and \QC variables.
    However, no quoting rules apply.

    You can specify command-line arguments in the arguments fields:
    \uicontrol Arguments, \uicontrol {Additional arguments},
    \uicontrol {CMake arguments}, \uicontrol {Command arguments},
    \uicontrol {Default arguments}, \uicontrol {Extra arguments},
    \uicontrol {Make arguments}, or \uicontrol {Tool arguments}.
    You can create shell command lines
    that can contain redirection and other advanced constructs. However, some
    more complex use cases, such as piping test data into the application being
    tested or grouping commands, are not supported because the value of the
    \uicontrol Executable field is always placed first when constructing the command.

    You can use \l {Using Environment Variables}{environment variables} as
    values in the fields. In addition, you can \l {Using Qt Creator Variables}
    {use \QC variables} in arguments, executable paths, and working
    directories.

    \section1 Build Steps

    \image qtcreator-cmake-build-steps.png "CMake build steps"

    In \uicontrol{Build Steps}, you can change the settings for the build system
    selected for building the project:

    \list
        \li \l{CMake Build Steps}{CMake}
        \li \l{qmake Build Steps}{qmake}
        \li \l{Qbs Build Steps}{Qbs}
        \li \l{Meson Build Steps}{Meson}
        \li \l{Conan Build Steps}{Conan}
    \endlist

    You can use \l{IncrediBuild Build Steps}{IncrediBuild} to accelerate the
    build process when using qmake or CMake.

    \section2 Adding Custom Build Steps

    To add custom steps to the build settings, select
    \uicontrol {Add Build Step} > \uicontrol {Custom Process Step}.

    By default, custom steps are enabled. To disable a custom step, select
    the \inlineimage icons/buildstepdisable.png
    (\uicontrol Disable) button.

    \image qtcreator-build-steps-custom.png "Custom Process Step"

    \section2 Executing Custom Commands

    To execute custom commands when building for embedded devices, select
    \uicontrol {Add Build Step} > \uicontrol {Custom Remote Command
    (via adb shell)} (commercial only) and enter the command to execute.

    \section1 Clean Steps

    \image qtcreator-clean-steps.png "Clean steps"

    You can use the cleaning process to remove intermediate files. This process
    might help you to fix obscure issues during the process of building a
    project using:

    \list
        \li \l{CMake Clean Steps}{CMake}
        \li qmake
        \li \l{Qbs Clean Steps}{Qbs}
        \li \l{Meson Clean Steps}{Meson}
        \li \l{IncrediBuild Clean Steps}{IncrediBuild}
    \endlist

    You can define the clean steps for your builds in \uicontrol {Clean Steps}.

    \list

        \li To add a clean step using make or a custom process, click
            \uicontrol{Add Clean Step} and select the type of step you want to add.

            By default, custom steps are enabled. To disable a custom step,
            select the \uicontrol Disable button.

        \li To remove a clean step, click \uicontrol{Remove Item}.

        \li To change the order of steps, click \inlineimage icons/arrowup.png
            (\uicontrol {Move Up}) and \inlineimage icons/arrowdown.png
            (\uicontrol {Move Down}).
    \endlist
*/
