// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-checking-code-syntax.html
    \page creator-completing-code.html
    \nextpage creator-indenting-code.html

    \title Completing Code

    As you write code, \QC suggests properties, IDs, and code snippets to
    complete the code. It provides a list of suggestions to
    the statement currently under your cursor. Press \key Tab
    or \key Enter to accept the selected suggestion and complete the code.

    \if defined(qtcreator)
    The following image displays suggestions for completing C++ code:

    \image qtcreator-codecompletion.png
    \endif

    The following image displays suggestions for completing QML code:

    \image qml-code-completion.png

    To open the list of suggestions at any time, press \key {Ctrl+Space}.
    If only one option is available, \QC inserts it automatically.

    \section1 Specifying Completion Settings

    To specify settings for code completion, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol Completion.

    \image qtcreator-options-texteditor-completion.png "Text Editor Completion preferences"

    By default, code completion does not consider case. To apply full or
    first-letter case-sensitivity, select \uicontrol Full or
    \uicontrol {First Letter} in the \uicontrol {Case-sensitivity} field.

    By default, code completion is always invoked, but you can change this
    behavior in the \uicontrol {Activate completion} field to invoke
    it \uicontrol Manually or \uicontrol {When Triggered}.

    You can set a timeout in milliseconds for code completion in the
    \uicontrol {Timeout in ms} field.

    In the \uicontrol {Character threshold} field, specify the number of
    characters you need to enter before code completion is triggered.

    When completion is invoked manually, \QC completes the common prefix of the
    list of suggestions. This is especially useful for classes with several
    similarly named members. To disable this functionality, deselect the
    \uicontrol {Autocomplete common prefix} check box.

    Select the \uicontrol {Automatically split strings} check box to split
    a string to two lines by adding an end quote at the cursor position when
    you press \key Enter and a start quote at the beginning of the next line,
    before the rest of the string. In addition, pressing \key {Shift+Enter}
    inserts an escape character at the cursor position and moves the rest of
    the string to the next line.

    \section1 Summary of Available Types

    The following table lists available types for code completion and icon used
    for each.

    \table
        \header
            \li  Icon
            \li  Description
        \if defined(qtcreator)
        \row
            \li \inlineimage completion/class.png
            \li A class
        \row
            \li \inlineimage completion/enum.png
            \li An enum
        \row
            \li \inlineimage completion/enumerator.png
            \li An enumerator (value of an enum)
        \row
            \li \inlineimage completion/func.png
            \li A function
        \row
            \li \inlineimage completion/func_priv.png
            \li A private function
        \row
            \li \inlineimage completion/func_prot.png
            \li A protected function
        \row
            \li \inlineimage completion/var.png
            \li A variable
        \row
            \li \inlineimage completion/var_priv.png
            \li A private variable
        \row
            \li \inlineimage completion/var_prot.png
            \li A protected variable
        \row
            \li \inlineimage completion/signal.png
            \li A signal
        \row
            \li \inlineimage completion/slot.png
            \li A slot
        \row
            \li \inlineimage completion/slot_priv.png
            \li A private slot
        \row
            \li \inlineimage completion/slot_prot.png
            \li A protected slot
        \row
            \li \inlineimage completion/keyword.png
            \li A C++ keyword
        \row
            \li \inlineimage completion/snippet.png
            \li A C++ code snippet
        \endif
        \row
            \li \inlineimage completion/element.png
            \li A QML type
        \row
            \li \inlineimage completion/qmlsnippet.png
            \li A QML code snippet
        \if defined(qtcreator)
        \row
            \li \inlineimage completion/macro.png
            \li A macro
        \row
            \li \inlineimage completion/namespace.png
            \li A namespace
        \endif
    \endtable

    \section1 Completing Code Snippets

    Code snippets can consist of multiple variables that you specify values for.
    Select an item in the list and press \key Tab or \key Enter to complete the
    code. Press \key Tab to move between the variables and specify values for
    them. When you specify a value for a variable, all instances of the variable
    within the snippet are renamed.

    \if defined(qtcreator)
    The following image shows a C++ code snippet:

    \image qmldesigner-code-completion.png "Completing C++ code"
    \endif

    The following image shows a QML code snippet:

    \image qml-code-snippet.png

    \section2 Editing Code Snippets

    Code snippets specify code constructs. You can add, modify,
    and remove snippets in the snippet editor. To open the editor, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol Snippets.

    \if defined(qtcreator)
    The following image shows built-in C++ code snippets:

    \image qtcreator-edit-code-snippets.png "C++ snippet preferences"
    \endif

    The following image shows built-in QML code snippets:

    \image qml-snippet-editor.png

    \QC provides you with built-in snippets in the following categories:

    \list

        \li Text snippets, which can contain any text string. For example, code
            comments

        \if defined(qtcreator)
        \li C++ code snippets, which specify C++ code constructs

        \li CMake code snippets that you can use when editing \c CMakeLists.txt
            files in the CMake editor
        \endif

        \li QML code snippets, which specify QML code constructs

        \if defined(qtcreator)
        \li Nim code snippets, which specify Nim code constructs
        \endif

    \endlist

    \section2 Adding and Editing Snippets

    Select a snippet in the list to edit it in the snippet editor. To add a new
    snippet, select \uicontrol Add. Specify a trigger and, if the trigger is
    already in use, an optional variant, which appear in the list of suggestions
    when you write code.
    \if defined(qtcreator)
    Also specify a text string or C++ or QML code construct
    in the snippet editor, depending on the snippet category.
    \else
    Also specify a text string or QML code in the snippet editor, depending on
    the snippet category.
    \endif
    You can use \l{Using Qt Creator Variables}{predefined variables} in
    snippets.

    The snippet editor provides you with:

    \list

        \li Highlighting

        \li Indentation

        \li Parentheses matching

        \li Basic code completion

    \endlist

    Specify the variables for the snippets in the following format:

    \code
    $variable$
    \endcode

    \if defined(qtcreator)
    Specify \QC variables in the following format:

    \code
    %{variable}
    \endcode

    For example, the following variable expands to the name of the
    project containing the file that is currently open in the editor:
    \c {%{CurrentDocument:Project:Name}}.

    Use unique variable names within a snippet because all instances of a
    variable are renamed when you specify a value for it.

    To determine the case of values you enter in snippets, use the following
    modifiers:

    \list

        \li \c {:c} converts the initial letter of the string to upper case

        \li \c {:l} converts the string to lower case

        \li \c {:u} converts the string to upper case

    \endlist

    For example, add the following line to the \c class snippet to specify that
    the function name is converted to all lower case characters regardless of
    how you specify the value of the \c{$name$} variable:

    \code
        void $name:l$() {}
    \endcode

    \image qtcreator-snippet-modifiers.png
    \endif

    The snippet editor does not check the syntax of the snippets that you edit
    or add. However, when you use the snippets, the code editor marks any
    errors by underlining them in red.

    To discard the changes you made to a built-in snippet, select
    \uicontrol {Revert Built-in}.

    \section2 Removing Snippets

    Several similar built-in snippets might be provided for different use cases.
    To make the list of suggestions shorter when you write code, remove the
    built-in snippets that you do not need. If you need them later, you can
    restore them.

    To remove snippets, select a snippet in the list, and then select
    \uicontrol Remove. To restore the removed snippets, select
    \uicontrol {Restore Removed Built-ins}.

    \section2 Resetting Snippets

    To remove all added snippets and to restore all removed snippets, select
    \uicontrol {Reset All}.

    \note If you now select \uicontrol OK or \uicontrol Apply, you permanently
    lose all your own snippets.

    \if defined(qtcreator)
    \section1 Completing Nim Code

    You can use the \l{https://nim-lang.org/docs/nimsuggest.html}{Nimsuggest}
    tool to query \c {.nim} source files and obtain suggestions for code
    completion.

    To use Nimsuggest, you must install it on the development PC. Then select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Nim > \uicontrol Tools,
    and enter the path to the tool executable in the \uicontrol Path field.
    \else
    \include qtcreator-variables.qdocinc qtcreator variables
    \endif
*/
