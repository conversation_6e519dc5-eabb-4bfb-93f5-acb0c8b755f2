// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-help.html
    \page creator-faq.html
    \nextpage creator-how-tos.html

    \title FAQ

    This section contains answers to some frequently asked questions about \QC.
    You might also find answers to your questions in the
    \l{Known Issues} and \l{How-tos} sections, or the Troubleshooting
    sections for a special area, such as
    \l{Troubleshooting Debugger}{debugging}.

    \section1 General Questions


    \b {How do I reset all \QC settings?}

    Remove the settings files created by \QC.

    For more information about where the files are located on each supported
    platform, see \l {Location of Settings Files}.

    \b {\QC comes with \MinGW, should I use this version with Qt?}

    Use the version that was built against the Qt version.

    \b {\QC does not find a helper application, such as Git or a
    compiler. What should I do?}

    Make sure that the application is in your system PATH when starting \QC.
    Also select \uicontrol Edit > \uicontrol Preferences to check the settings
    specified for the application. Many plugins specify either the path to the
    tool they need or the environment they run in.

    This is especially relevant for the \macos where \c {/usr/local/bin} might
    not be in the path when \QC is started.

    \b {How do I change the interface language for \QC?}

    \QC has been localized into several languages. If the system
    language is one of the supported languages, it is automatically selected.
    To change the language, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Environment and select a language in the \uicontrol Language
    field. Select \uicontrol {Restart Now} to restart \QC and have the change
    take effect.

    \b {Has a reported issue been addressed?}

    You can look up any issue in the
    \l{https://bugreports.qt.io/}{Qt Project Bug Tracker}.

    \include widgets/creator-faq-qtdesigner.qdocinc qt designer faq

    \section1 QML and Qt Quick Questions

    \b {Why is there a red line below my QML import, even though I have the module?}

    By default, \QC looks in the QML import path of Qt for QML modules.
    Sometimes, it does not get it right and you need to tell it where the
    modules are. When using qmake as the build system, specify the
    \c{QML_IMPORT_PATH} in the \c{.pro} file of your application. When
    using CMake, add the \c set command to the CMakeLists.txt file.

    This also enables code completion of QML code and removes error messages.

    The following example illustrates how to specify the import path for qmake
    projects so that it works when switching between build and run kits for
    different target platforms:

    \code
    TEMPNAME = $${QMAKE_QMAKE}
    MY_QTPATH = $$dirname(TEMPNAME)
    QML_IMPORT_PATH += $$MY_QTPATH/../qml
    message("my QML Import Path: "$$QML_IMPORT_PATH)
    \endcode

    For more information about how to set the import path for CMake projects,
    see \l {Importing QML Modules}.

    \b {What should I do when \QC complains about missing OpenGL support?}

    Some parts of \QC, such as QML Profiler, use Qt Quick 2, which
    relies on OpenGL API for drawing. Unfortunately, the use of OpenGL can cause
    problems, especially in remote setups and with outdated drivers. In these
    cases, \QC displays OpenGL-related error messages on the console or records
    them in the Windows debugger log.

    The fixes and workarounds differ, depending on your setup. As a last resort,
    you can disable the affected plugins.

    \e{Virtual Machines}

    Try to enable \e{3D acceleration} in your virtual machine's settings. For
    VirtualBox, also make sure you have installed the Guest Addons, including
    experimental \e{Direct3D support}.

    \e{Windows}

    Check whether \QC has been compiled with OpenGL/Desktop, or ANGLE as
    a backend. The official binaries are always built with ANGLE (a library that
    maps OpenGL ES API to DirectX).

    \list

      \li ANGLE backend: This requires a Windows version newer than Windows XP.
          If you have problems, try updating your graphics drivers or update
          your DirectX version. Run \c dxdiag.exe to check whether
          \e{Direct3D Acceleration} is indeed enabled.

      \li OpenGL backend: Make sure your graphics driver supports OpenGL 2.1 or
          newer. Try to update your graphics driver.

    \endlist

    \e{Unix}

    Run \c glxgears for a quick check whether OpenGL works in general. Check the
    output of \c glxinfo to get more details like the OpenGL driver and renderer
    (search for \e OpenGL in the application's output).

    If you are using the Mesa driver, you can force OpenGL to be rendered in
    software by setting the \c LIBGL_ALWAYS_SOFTWARE environment variable.

    \e{Disabling plugins}

    You can disable the \QC plugins, at the expense of losing functionality:

    \list

      \li Launch \QC from command line, with the
          \c {-noload QmlProfiler -noload QmlDesigner} arguments.

      \li Disable the plugins permanently by selecting \uicontrol Help >
          \uicontrol{About Plugins}.

    \endlist

    \section1 Help Questions

    \b {The Qt API Reference Documentation is missing and context help does
    not find topics. What can I do?}

    \QC comes fully integrated with Qt documentation and examples using
    the Qt Help plugin. The integrated Qt Reference Documentation is available
    for Qt 4.4 and later. \QC and other Qt deliverables contain
    documentation as .qch files. All the documentation is accessible in the
    \uicontrol Help mode.

    To view the documentation that is available and to add documentation,
    select \uicontrol Edit > \uicontrol Preferences > \uicontrol Help >
    \uicontrol Documentation. For more information, see
    \l{Adding External Documentation}.

    \section1 Debugger Questions

    For information on troubleshooting debugger, see
    \l{Troubleshooting Debugger}.

    \b {If I have a choice of GDB versions, which should I use?}

    On Linux and Windows, use the Python-enabled GDB versions that are
    installed when you install \QC and \QSDK. On \macos, GDB is no longer
    officially supported. To build your own Python-enabled GDB, follow the
    instructions in
    \l{https://wiki.qt.io/QtCreator_Build_Gdb}{Building GDB}.

    You must use Python version 2.6 or 2.7.

    For more information on setting up debugger, see \l{Setting Up Debugger}.

    \b {How do I generate a core file in \QC?}

    To trigger the GDB command that generates a core file while debugging,
    select \uicontrol View > \uicontrol Views > \uicontrol {Debugger Log}.
    In the \uicontrol Command field, type \c gcore and press \key Enter. The
    core file is created in the current working directory. You can specify
    another location for the file, including a relative or absolute path, as an
    argument of the command.

    \omit
    To generate a temporary core file, select \uicontrol {Create Snapshot} in the
    context menu in the \uicontrol Snapshot view. The core file is deleted when you
    stop debugging.
    \endomit

    \section1 Compiler Questions

    \b {How can I make use of my multi-core CPU with \QC?}

    On Linux and \macos, go to \uicontrol Projects mode, select your configuration
    in the \uicontrol {Build Settings}, locate the \uicontrol {Build Steps}, and add the
    following value, where \c{<num>} is the amount of cores in your CPU:
    \c{-j <num>}

    On Windows, nmake does not support the \c{-j} parameter. Instead, we
    provide a drop-in replacement called jom. You can download a precompiled
    version of jom from \l{https://download.qt.io/official_releases/jom/}{Qt Downloads}.
    Put jom.exe in a location in the %PATH%. Go to the \uicontrol {Build Settings}
    and set jom.exe as the make command.

    \note Unlike GNU make, jom automatically detects your cores and spawns as
    many parallel processes as your CPU has cores. You can override this
    behavior by using the \c{-j} parameter as described above.

    \section1 Qt Installation Questions

    \b {I cannot use QSslSocket with the Qt I installed from binary packages.
        What should I do?}

    The Qt in the binary packages is built with QT_NO_OPENSSL defined. Rebuilding it
     is possible. For more information, see
     \l{http://www.qtcentre.org/threads/19222-Qssl}.

    \b {Which development packages from the distribution are needed on
    Ubuntu or Debian?}

    \code
    sudo apt-get install libglib2.0-dev libSM-dev libxrender-dev libfontconfig1-dev libxext-dev
    \endcode

    If you use QtOpenGL, you also need:

    \code
    sudo apt-get install libgl-dev libglu-dev
    \endcode

    \section1 Platform Related Questions

    \b {Where is application output shown in \QC?}

    \b {On Unix (Linux and \macos):} \c qDebug() and related functions use
    the standard output and error output. When you run or debug the
    application, you can view the output in \l{Application Output}.

    For console applications that require input, select \uicontrol Projects >
    \uicontrol {Run Settings} > \uicontrol {Run in terminal}. To specify the
    terminal to use, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Environment > \uicontrol System.

    \b {On Windows:} Output is displayed differently for \e{console
    applications} and \e{GUI applications}.

    For qmake projects, the \c {CONFIG += console} setting in the .pro file
    specifies that the application is built as a console application using
    some other runtime.

    This is the standard behavior for CMake projects. To create a GUI
    application on Windows or an application bundle on \macos, you must
    specify the \c WIN32 or \c MACOSX_BUNDLE property for the
    \l{https://doc.qt.io/qt-6/qt-add-executable.html}{qt_add_executable}
    command in the CMakeLists.txt file.

    When you run a console application, you can view the output in the console
    window of the calling application. If the
    calling application is a GUI application (for example, a release-built
    version of \QC), a new console window is opened.  For this
    type of application, \c qDebug() and related functions use standard output
    and error output.

    We recommend that you select \uicontrol Projects > \uicontrol {Run Settings} >
    \uicontrol {Run in terminal} for console applications.

    For GUI applications, \c qDebug() and related functions use the Windows API
    function \c OutputDebugString(). The output is displayed in
    \uicontrol{Application Output}. However, \QC can show output from only one
    source at the time for it to be displayed correctly. You can use an
    external debug output viewer, such as the
    \l{https://technet.microsoft.com/en-us/sysinternals/bb896647}
    {DebugView for Windows} to display output from GUI applications.

    \section1 Questions about New Features

    \b {Will a requested feature be implemented?}

    If it is a scheduled feature, you can see this in the task tracker. If a
    feature already has been implemented, it is mentioned in the
    \l{https://code.qt.io/cgit/qt-creator/qt-creator.git/tree/dist}{changes file}
    for the upcoming release.

    \b {Why does \QC not use tabs for editors?}

    This question comes up from time to time, so we have considered it
    carefully. Here are our main reasons for not using tabs:

    \list

        \li Tabs do not scale. They work fine if you have 5 to 6 editors open,
            they become cumbersome with 10, and if you need more horizontal
            space than the tab bar, the interface does not work at all.

        \li Tabs do not adapt to your working set.

        \li The common solution is to give the user the ability to reorder
            tabs. Now user has to manage tabs instead of writing code.

        \li Tabs force you to limit the amount of open editors because
            otherwise you get confused.

    \endlist

    Consider the following use case: \e {Developers want to switch editors.}

    In fact, developers do not want to switch editors, but might have to do so
    to accomplish their tasks. We need to figure out what the tasks are to
    provide developers with better ways to navigate while performing the tasks.

    One common factor in many use cases is switching editors while working on a
    set of open files. While working on files A and B, users sometimes need to
    look at file C. They can press \key Ctrl+Tab to move between the files and
    have the files open in the correct editor according to file type. The list
    is sorted by last used.

    Typically, users also work on multiple classes or functions that are
    related, even though they are defined or declared in different files.
    \QC provides two shortcuts for that: \key F2 to follow the symbol under
    cursor and \key Ctrl+Shift+U to find references to it.

    In addition, developers can:

    \list

        \li Press \key F4 to switch between header and source.

        \li Press \key Alt+Left to move backwards in the navigation history.

        \li Use the locator (Ctrl+K) to simply tell \QC where to go.

    \endlist

    The locator can be used to open files, but opening files is also just a
    step on the way to accomplish a task. For example, consider the following
    use case: \e {Fix AFunction in SomeClass which comes from
    someclass.cpp/someclass.h}.

    With a tabbed user interface, developers would search for someclass.cpp in
    the tab bar, and then search for \c {::AFunction}, only to find out that the
    function is not located in that file. They would then search for someclass.h
    in the tab bar, find our that the function is inline, fix the problem, and
    forget where they came from.

    With \QC, developers can type \c {Ctrl+K m AFun} to find the function.
    Typically, they only need to type 3 to 4 characters of the function name.
    They can then fix the problem and press \key Alt+Back to go back to where
    they were.

    Other locator filters include \c c for classes, \c : for all symbols, and
    (thanks to a community contribution) \c . for symbols in the current file.

*/
