// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-os-supported-platforms.html
    \page creator-project-other.html
    \nextpage creator-project-cmake.html

    \title Build Systems

    Most \QC project wizards enable you to choose the build system to use for
    building the project: qmake, CMake, <PERSON>son, or Qbs. qmake is installed and
    configured when you install Qt. To use one of the other supported build
    systems, you need to set it up, as described in the the following sections:

    \list

        \li \l{Setting Up CMake}

            CMake is an alternative to qmake for automating the generation of
            build configurations.

        \li \l{Setting Up Qbs}

            \l{Qbs Manual}{Qbs} is an all-in-one build
            tool that generates a build graph from a high-level project
            description (like qmake or CMake do) and executes the commands in
            the low-level build graph (like make does).

        \li \l{Setting Up an Autotools Project}

            \QC can open projects that use the Autotools build system. You can
            build and run the projects directly from \QC.

        \li \l{Setting Up a Generic Project}

            \QC supports generic projects, so you can import existing projects
            that do not use qmake or CMake. This enables you to use \QC as a
            code editor and to fully control the steps and commands used to
            build the project.

        \li \l{Setting Up Nimble}

            The experimental \l{https://nim-lang.org/}{Nim} plugin integrates
            the Nimble package manager for generating Nim application
            executables that are supported on Windows, Linux, and \macos.

        \li \l{Setting Up Meson}

            Meson is an open source build system meant to be both extremely fast,
            and, even more importantly, as user friendly as possible.

        \li \l{Setting Up IncrediBuild}

            IncrediBuild decreases the time it takes to build C++ code.

        \li \l{Setting Up Conan}

            The experimental Conan plugin integrates the Conan package manager
            that speeds up the integration of C or C++ libraries into your
            project. You can use Conan with most build systems integrated into
            \QC.

    \endlist

*/
