/* File generated by Qt Creator */

import QmlProject 1.1

Project {
    mainFile: "ProgressBar.ui.qml"

    /* Include .qml, .js, and image files from current directory and subdirectories */
    QmlFiles {
        directory: "."
    }
    JavaScriptFiles {
        directory: "."
    }
    ImageFiles {
        directory: "."
    }
    /* List of plugin directories passed to QML runtime */
    // importPaths: [ "../exampleplugin" ]
}
