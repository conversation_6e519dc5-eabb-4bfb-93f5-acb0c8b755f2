// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-build-settings.html
    \page creator-run-settings.html
    \nextpage creator-editor-settings.html

    \title Specifying Run Settings

    The run settings to specify depend on the type of the project and on the
    \l{glossary-buildandrun-kit}{kit} that you build and run the project with.

    \QC automatically creates run configurations for your project.
    To view and modify them, select \uicontrol Projects >
    \uicontrol {Build & Run} > \uicontrol Run.

    \image qtcreator-settings-run.png "Run Settings"

    To prevent \QC from automatically creating run configurations, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Build & Run},
    and then deselect the \uicontrol {Create suitable run configurations
    automatically} check box.

    \section1 Managing Run Configurations

    The available run configurations are listed in the
    \uicontrol {Run configuration} field.
    To add run configurations for a project, select \uicontrol Add. To
    add a run configuration that is based on the current one, select
    \uicontrol Clone.

    To rename the current run configuration, select \uicontrol Rename.

    To remove the current run configuration, select \uicontrol Remove.

    The run configurations for qmake projects derive their executable from the
    parsed .pro files. For more information on how the commands are constructed,
    see \l{Starting External Processes}.

    \section1 Selecting Default Run Target

    If a project has multiple executables, you need to tell \QC which one it
    should run.

    \section2 CMake Run Targets

    When using CMake, you can filter the run target list by setting
    \c qtc_runnable as the value of the \c FOLDER property
    in the \c {CMakeLists.txt} file for the project. For example:

    \badcode
    set_target_properties(main_executable PROPERTIES FOLDER "qtc_runnable")
    \endcode

    If you do not specify \c qtc_runnable for any project, \QC automatically
    adds run configurations for all targets specified in \c {CMakeLists.txt}.

    \section2 qmake Run Targets

    When using qmake, you can prevent \QC from automatically creating run
    configurations for subprojects by specifying the \c qtc_runnable
    variable in the .pro files of the application projects (\c TEMPLATE=app)
    that you want to run. For example

    \badcode
    CONFIG += qtc_runnable
    \endcode

    If none of your application projects specifies \c qtc_runnable, \QC creates
    run configurations for all application projects.

    If any of your application projects specifies \c qtc_runnable,
    \QC creates run configurations only for subprojects that also have
    \c {CONFIG += qtc_runnable} set in their .pro files.

    For more information about qmake project templates, see \l {TEMPLATE}.

    \section2 Meson Run Targets

    \QC automatically adds run configurations for all targets declared with
    \c {executable()} function in Meson build descriptions.

    \include creator-projects-settings-run-desktop.qdocinc run settings desktop
    \include creator-projects-settings-run-analyze.qdocinc settings valgrind
    \include creator-projects-settings-run-debug.qdocinc run settings debugger
    \include creator-projects-settings-run-android.qdocinc run settings android
    \include creator-projects-settings-run-linux.qdocinc run settings linux
    \include creator-projects-settings-run-qnx.qdocinc run settings qnx
    \include linux-mobile/creator-projects-settings-run-b2qt.qdocinc run settings boot2qt

    \section1 Selecting the Run Environment

    \QC automatically selects the environment used for running the application
    based on the \l{glossary-device}{device} type. You can edit the environment
    or select another environment in the \uicontrol {Run Environment} section.

    You can edit existing environment variables or add, reset and unset new
    variables.

    \image qtcreator-run-environment.png "Run Environment section"

    When running on the desktop, the \uicontrol {Build Environment} is used by
    default, but you can also use the \uicontrol {System Environment} without the
    additions made to the build environment. For more information, see
    \l {Build Environment} and \l{Specifying Environment Settings}.

    To run in a clean system environment, select \uicontrol {Clean Environment}.

    When running on a mobile device connected to the development host, \QC
    fetches information about the \uicontrol {Device Environment} from the device.
    Usually, it does not make sense to edit the device environment.

    To modify the environment variable values for the run environment, select
    \uicontrol {Batch Edit}. For more information, see \l{Batch Editing}.

    \section1 Specifying a Custom Executable to Run

    If you use CMake, Meson or the generic project type in \QC, or want
    to run a custom desktop executable, create a \uicontrol {Custom Executable}
    run configuration for your project. For example, when working on a library,
    you can run a test application that links against the library.

    Specify the executable to run, command line arguments, working directory,
    and environment variables to use.

    \image qmldesigner-run-custom-exe.png "Run settings for custom executables"

    \include qtquick/creator-projects-settings-run-qtquick.qdocinc run settings qt quick ui
    \include python/creator-python-run.qdocinc run settings python

*/
