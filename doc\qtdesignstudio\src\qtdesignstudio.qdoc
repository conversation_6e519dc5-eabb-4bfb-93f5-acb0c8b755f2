// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page index.html
    \nextpage studio-getting-started.html

    \title Qt Design Studio Manual

    Define the look and feel of the UI from wireframe to final implementation
    with preset UI components. Import UI design files from 2D and 3D tools to
    \QDS, which can turn them into code for developers. \QDS prototyping
    features bring your designs to life and simulate and validate interactions
    and dynamic behavior. You can test, preview, and fine-tune your designs to
    pixel-perfection live on the desktop or target device.

    View \l{All Topics}{all topics} or select a topic from below.

    \table
        \row
            \li \inlineimage front-gs.png
            \li \inlineimage front-ui.png
            \li \inlineimage studio-3d-scenes.png
            \li \inlineimage studio-animation.png
        \row
        \li \b {\l{Getting Started}}
            \list
                \li \l{Installation}
                \li \l{Tutorials}
                \li \l{User Interface}
                \li \l{Creating Projects}
                \li \l{Use Cases}
                \li \l{Concepts and Terms}
                \li \l{Examples}
            \endlist
        \li \b {\l{Wireframing}}
            \list
                \li \l{Designing Application Flows}
                \li \l{Using Components}
                \li \l{Specifying Component Properties}
                \li \l{Scalable Layouts}
                \li \l{Annotating Designs}
            \endlist
        \li \b {\l{Prototyping}}
            \list
                \li \l{Creating UI Logic}
                \li \l{Simulating Complex Experiences}
                \li \l{Dynamic Behaviors}
                \li \l{Validating with Target Hardware}
                \li \l{Asset Creation with Other Tools}
            \endlist
        \li \b {\l{Motion Design}}
            \list
                \li \l{Introduction to Animation Techniques}
                \li \l{Creating Timeline Animations}
                \li \l{Editing Easing Curves}
                \li \l{Production Quality}
                \li \l{Optimizing Designs}
            \endlist
        \row
            \li \inlineimage front-preview.png
            \li \inlineimage front-advanced.png
            \li \inlineimage front-projects.png
            \li \inlineimage front-help.png
        \row
        \li \b {\l{Implementing Applications}}
            \list
                \li \l{Designer-Developer Workflow}
                \li \l{Coding}{Cross-Platform Development}
                \li \l{Debugging and Profiling}
            \endlist
        \li \b {\l{Advanced Designer Topics}}
            \list
                \li \l{UI Files}
                \li \l {Managing Data Collection}
                \li \l{Packaging Applications}
            \endlist
        \li \b {\l{Developer Topics}}
            \list
                \li \l{Using Git}
                \li \l{Converting Qt 5 Projects into Qt 6 Projects}
                \li \l{Converting UI Projects to Applications}
                \li \l{Using External Tools}
            \endlist
        \li \b {\l Help}
            \list
                \li \l{Using the Help Mode}
                \li \l{Frequently Asked Questions}
                \li \l{Supported Platforms}
            \endlist
        \row
            \li {4,1} \note To report bugs and suggestions to the
                \l{https://bugreports.qt.io/}{Qt Project Bug Tracker},
                select \uicontrol {Help > Report Bug} in \QDS.
                To copy and paste detailed information about your system to the
                bug report, select \uicontrol Help >
                \uicontrol {System Information}.

                For credits and a list of third-party libraries, see
                \l {Acknowledgements}.
    \endtable
*/
