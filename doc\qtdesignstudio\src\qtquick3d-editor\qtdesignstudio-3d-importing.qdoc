// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-importing-3d.html
    \if defined(qtdesignstudio)
    \previouspage qtquick-fonts.html
    \else
    \previouspage exporting-from-maya.html
    \endif
    \nextpage creator-exporting-qml.html

    \title Importing 3D Assets

    In \QDS, you can import 3D assets in formats such as .stl, .dae, .fbx, .glb,
    .gltf, .obj, .uia, and .uip. For a list of formats supported by each
    \l{Qt Quick 3D} version, see the module documentation.

    During the import, you can optimize the files for \QDS. You can remove
    components from meshes to reduce the cache size, find and fix issues in
    the files, optimize graphs and meshes, and so on. The available options
    depend on whether you are importing files that you created with Qt 3D Studio
    or with other 3D graphics tools. See the tooltips in the options dialog
    for more information about a particular option.

    For more information about exporting 3D graphics, see
    \l{Exporting 3D Assets}.

    \image studio-import-3d.png

    \section1 Importing a 3D Asset

    To import a 3D asset to a \QDS project:

    \list 1
        \li Drag-and-drop an external file containing the 3D asset from,
            for example, File Explorer (on Windows), to the \uicontrol{3D} view.
        \li In the \uicontrol {3D Scene Options} tab, select options for
            importing the file.
            \note To see all options, select \uicontrol{Show All Options}.
        \li Select \uicontrol Import to import the 3D asset.
    \endlist

    The 3D asset is now added to your scene, and you can see it in the
    \uicontrol{3D} and \uicontrol Navigator views. It is also
    available in \uicontrol Components > \uicontrol {My 3D Components}.

    Alternatively, you can initiate the import dialog from the
    \uicontrol Assets view:

    \list 1
        \li Select \l Assets > \inlineimage icons/plus.png
            .
        \li Select \uicontrol {3D Assets} in the dropdown menu to filter 3D
            graphics files.
        \li Select a file to import, and then select \uicontrol Open.
        \li In the \uicontrol {3D Scene Options} tab, select options for
            importing the file.
            \note To see all options, select \uicontrol{Show All Options}.
        \li Select \uicontrol Import to import the 3D asset.
    \endlist

    The 3D asset now appears in \uicontrol Components >
    \uicontrol {My 3D Components}. You can add it to the scene by
    drag-and-dropping it to the \uicontrol{3D} view.

*/
