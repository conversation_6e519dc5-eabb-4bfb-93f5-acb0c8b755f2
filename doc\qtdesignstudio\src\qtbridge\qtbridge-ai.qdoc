// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage qtbridge-overview.html
    \page qtbridge-ai.html
    \nextpage psqtbridge.html

    \title Exporting Designs from Adobe Illustrator

    Even though \QDS does not provide a specific export bridge for Adobe
    Illustrator, you can desing UIs in it and export your designs to \QDS in
    the following ways:

    \list
        \li Place your Illustrator content into Adobe Photoshop and
            use \QBPS to export the assets to \e {.metadata} format that
            you can import to \QDS.
        \li Use the Illustrator \e {export for screens workflow} to export your
            assets into formats supported by \QDS, such as PNG and JPEG.
    \endlist

    \section1 Placing Illustrator Content into Photoshop

    You can place Illustrator content into Photoshop in several ways. If you
    select \uicontrol File > \uicontrol {Place linked} in Illustrator, the
    content updates automatically. However, the whole file content is placed
    on a single Photoshop layer, which means that you cannot use \QBPS to
    export the individual parts of the Illustrator design.

    Therefore, you have to copy-paste your assets to Photoshop as
    \e {smart objects}, one by one, at the layer level. You can then
    specify export settings for \QBPS.

    If you need to edit smart objects after copying them to Photoshop, you can
    double-click them in Photoshop to open them in Illustrator. Because you use
    smart objects, all your changes are propagated to all instances of the
    objects used in your designs in Photoshop. For more information about using
    smart objects, see the Illustrator and Photoshop documentation.

    For more information about exporting designs from Photoshop, see
    \l{Exporting Designs from Adobe Photoshop}.

    \section1 Exporting Assets for Screens

    Sometimes it is easier to just export layers and artboards from Illustrator
    and to create scalable layouts and UI flows in \QDS. You can export assets
    to multiple formats and sizes, such as PNG, JPEG, and SVG.

    For more information, see
    \l{https://helpx.adobe.com/illustrator/using/collect-assets-export-for-screens.html}
    {Collect assets and export in batches} in the Illustrator documentation.
*/
