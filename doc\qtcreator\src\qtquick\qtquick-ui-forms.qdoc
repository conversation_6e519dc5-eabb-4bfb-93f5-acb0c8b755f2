// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-quick-ui-forms.html
    \if defined(qtdesignstudio)
    \previouspage studio-advanced.html
    \nextpage creator-telemetry.html
    \else
    \previouspage quick-converting-ui-projects.html
    \nextpage creator-qml-modules-with-plugins.html
    \endif

    \title UI Files

    \if defined(qtdesignstudio)
    You can use \QDS wizards to create UI files that have the filename
    extension \e .ui.qml. The UI files can be edited in the \l {2D} view.
    If you use the \l {Code} view to add code that is not supported
    by the \uicontrol {2D} view, \QDS displays error messages.
    \else
    If you switch between \QC and \QDS or cooperate with designers on
    a project, you might encounter UI files (.ui.qml). They are intended to
    be edited in \QDS only.
    \endif


    The following features are not supported in .ui.qml files:

    \list
        \li JavaScript blocks
        \li Other bindings than pure expressions
        \li Signal handlers
        \li States in other components than the root component
        \li Root components that are not derived from \l QQuickItem or
            \l [QML]{Item}
        \li Referencing the parent of the root component
    \endlist

    The following components are not supported:

    \list
        \li Behavior
        \li Binding
        \li Canvas
        \li Shader Effect
        \li Timer
        \li Transform
    \endlist

    \section1 Supported Methods

    \QC supports most JavaScript functions that are supported by the QML
    engine, as well as a subset of Qt QML methods.

    This section lists the functions that you can use in \e .ui.qml files.

    \section2 JavaScript Functions

    As a rule of thumb, \e {pure functions} are supported. They only depend on
    and modify states of parameters that are within their scope, and therefore
    always return the same results when given the same parameters. This makes
    it possible to convert and reformat property bindings without breaking the
    \e .ui.qml files.

    The following JavaScript functions are supported:

    \list
        \li \c charAt()
        \li \c charCodeAt()
        \li \c concat()
        \li \c endsWith()
        \li \c includes()
        \li \c indexOf()
        \li \c isFinite()
        \li \c isNaN()
        \li \c lastIndexOf()
        \li \c substring()
        \li \c toExponential()
        \li \c toFixed()
        \li \c toLocaleLowerCase()
        \li \c toLocaleString
        \li \c toLocaleUpperCase()
        \li \c toLowerCase()
        \li \c toPrecision()
        \li \c toString()
        \li \c toUpperCase()
        \li \c valueOf()
    \endlist

    In addition, all functions of the \c Math and \c Date objects are supported.

    For more information, see
    \l{https://doc.qt.io/qt/qtqml-javascript-functionlist.html}
    {List of JavaScript Objects and Functions}.

    \section2 Qt QML Methods

    \QC supports color methods, helper methods for creating objects of
    specific data types, and translation methods.

    The following color methods are supported:

    \list
        \li \l{Qt::darker()}{Qt.darker()}
        \li \l{Qt::hsla()}{Qt.hsla()}
        \li \l{Qt::hsva()}{Qt.hsva()}
        \li \l{Qt::lighter()}{Qt.lighter()}
        \li \l{Qt::rgba()}{Qt.rgba()}
        \li \l{Qt::tint()}{Qt.tint()}
    \endlist

    The following helper methods are supported:

    \list
        \li \l{Qt::formatDate()}{Qt.formatDate()}
        \li \l{Qt::formatDateTime()}{Qt.formatDateTime()}
        \li \l{Qt::formatTime()}{Qt.formatTime()}
        \li \l{Qt::matrix4x4()}{Qt.matrix4x4()}
        \li \l{Qt::point()}{Qt.point()}
        \li \l{Qt::quaternion()}{Qt.quaternion()}
        \li \l{Qt::rect()}{Qt.rect()}
        \li \l{Qt::size()}{Qt.size()}
        \li \l{Qt::vector2d()}{Qt.vector2d()}
        \li \l{Qt::vector3d()}{Qt.vector3d()}
        \li \l{Qt::vector4d()}{Qt.vector4d()}
    \endlist

    The following translation methods are supported:

    \list
        \li \l{Qt::}{qsTr()}
        \li \l{Qt::}{qsTranslate()}
        \li \l{Qt::}{qsTranslateNoOp()}
        \li \l{Qt::}{qsTrId()}
        \li \l{Qt::}{qsTrIdNoOp()}
        \li \l{Qt::}{qsTrNoOp()}
    \endlist

    \note Do not mix translation methods in a UI file.

    For more information about using the methods, see
    \l{https://doc.qt.io/qt/qml-qtqml-qt.html}{Qt QML Methods}.

    \if defined(qtdesignstudio)
    \section1 Using UI Files

    You can edit the UI files in the \l {2D} and
    \uicontrol {Text Editor} views. Components that are
    supposed to be used in code have to be exported as properties:

    \code
    Item {
        width: 640
        height: 480

        property alias button: button

        Button {
            anchors.centerIn: parent
            id: button
            text: qsTr("Press Me")
        }
    }
    \endcode

    The property alias exports the button to the code that uses the form.
    You can use the \inlineimage icons/alias.png
    (\uicontrol Export) button in \l Navigator to export a component
    as a property:

    \image qmldesigner-export-item.png

    In the UI file where the component is used, you can use the \c button
    property alias to implement signal handlers, for example. In the
    following code snippet, the UI file is called \e MainForm.ui.qml:

    \code
    MainForm {
        anchors.fill: parent
        button.onClicked: messageDialog.show(qsTr("Button pressed"))
    }
    \endcode

    You can also assign properties or define behavior or transitions.

    To move from the \l {2D} or \l Navigator view directly to the
    implementation of a component in the .qml file, right-click the
    component and select \uicontrol {Go to Implementation} in the
    context menu.
    \endif
*/
