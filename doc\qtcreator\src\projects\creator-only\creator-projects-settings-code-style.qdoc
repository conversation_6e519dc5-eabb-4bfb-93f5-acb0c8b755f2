// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-editor-settings.html
    \page creator-code-style-settings.html
    \nextpage creator-build-dependencies.html

    \title Specifying Code Style Settings

    \QC uses the \l{Editing MIME Types}{MIME type} of the file to
    determine which mode and editor to use for opening the file.
    \QC opens C++ files in \uicontrol Edit mode in the C++ code editor and
    QML files in the Qt Quick editor.

    You can configure the code style according to your needs. You can specify
    code style either globally for all projects or separately for each
    project. You can specify several sets of code style settings and easily
    switch between them. In addition, you can import and export code style
    settings.

    \image qtcreator-projects-code-style.png "Code Style settings in Projects mode"

    Alternatively, you can enable the Clang Format plugin to enforce
    the code style specified in a \c {.clang-format} file. It uses the
    \l{https://clang.llvm.org/docs/LibFormat.html}{LibFormat} library for
    automatic code formatting and indentation. For more information, see
    \l {Automatic Formatting and Indentation}.

    \image qtcreator-code-style-clang-format.png "Clang Format Code Style settings in Projects mode"

    To specify global code style settings sets for C++ files, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol C++.

    \image qtcreator-code-style-settings-edit-cpp.png "Edit Code Style Settings dialog"

    To specify global code style settings sets for QML files, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Qt Quick}.

    \image qtcreator-code-style-settings-edit-qtquick.png "Edit Code Style Settings view"

    Only \uicontrol General settings are available for QML files.

    To configure the editor behavior for the current project:

    \list 1

        \li Select \uicontrol Projects > \uicontrol {Project Settings} >
            \uicontrol {Code Style}.

        \li In the \uicontrol Language field, select \uicontrol C++,
            \uicontrol {Qt Quick}, or \uicontrol Nim.

        \li In the \uicontrol {Current settings} field, select the settings to modify
            and click \uicontrol Copy.

        \li Give a name to the settings and click \uicontrol OK.

        \li Click \uicontrol Edit to specify code style settings for the project.

    \endlist

    For more information about the settings, see \l{Indenting Text or Code}.

*/
