// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page plugin-meta-data.html
    \title Plugin Meta Data

    The meta data file of a plugin is a JSON file that contains all
    information that is necessary for loading the plugin's library,
    determining whether plugins are to be loaded and in which order (depending
    on e.g. dependencies). In addition, it contains textual descriptions of
    who created the plugin, what it is for, and where to find more information about it.
    The file must be located in one of the include search paths when compiling the plugin,
    and must have the \c .json extension. The JSON file is compiled into the plugin as meta data,
    which then is read by \QC when loading plugins.

    \section2 Main Keys

    The main keys that are used to identify your your plugin and define default loading behavior,
    consist of the mandatory keys \c Name and \c Version, and the optional keys \c CompatVersion,
    \c Experimental, \c DisabledByDefault, \c Required and \c Platform.
    \table
        \header
            \li Key
            \li Value Type
            \li Meaning
        \row
            \li Name
            \li String
            \li This is used as an identifier for the plugin and can e.g.
                be referenced in other plugin's dependencies.
        \row
            \li Version
            \li String
            \li Version string in the form \c{x.y.z_n}, used for identifying
                the plugin. Also see \l{A Note on Plugin Versions}.
        \row
            \li CompatVersion
            \li String
            \li Optional. If not given, it is implicitly
                set to the same value as \c Version. The compatibility version
                states which version of this plugin the current version is
                binary backward compatible with and is used to resolve dependencies
                on this plugin. I.e. a \c Version of \c{2.1.1} and a
                \c CompatVersion of \c{2.0.0} means that this version \c{2.1.1} of the plugin
                is binary backward compatible with all versions of the plugin down to \c{2.0.0}
                (inclusive).
        \row
            \li Experimental
            \li Boolean
            \li Optional. Defaults to \c false.
                Experimental plugins are not loaded by default but must be explicitly
                enabled by the user. This attribute should be enabled for new plugins which have the
                potential to negatively affect the user experience.
        \row
            \li DisabledByDefault
            \li Boolean
            \li Optional. Defaults to \c false.
                If set, the respective plugin is not loaded by default but must be explicitly
                enabled by the user. This should be done for plugins which are not expected
                to be used by so many people as to justify the additional resource consumption.
        \row
            \li Required
            \li Boolean
            \li Optional. Defaults to \c false.
                Is used as a hint for the \uicontrol{About Plugins...} dialog, that the user may not
                manually disable this plugin. Only used for the Core plugin.
        \row
            \li Platform
            \li String
            \li Optional. A regular expression that matches the names of the platforms the plugin
                works on. Omitting the tag implies that the plugin is loaded on all platforms.
    \endtable

    \section2 Plugin-describing Keys

    These are solely used for more detailed (user centric) description of the plugin. All of these
    are optional.
    \table
        \header
            \li Key
            \li Value Type
            \li Meaning
        \row
            \li Category
            \li String
            \li Defaults to \c Utilities. Is used to put related plugins
                under the same tree node in the plugin overview \uicontrol{About Plugins...}.
        \row
            \li Vendor
            \li String
            \li String that describes the plugin creator/vendor,
                like \c{MyCompany}.
        \row
            \li Copyright
            \li String
            \li A short copyright notice, like \c{(C) 2016 MyCompany}.
        \row
            \li License
            \li String or array of strings
            \li Possibly multi-line license information about the plugin.
                Should still be kept relatively short, since the UI is not
                designed for long texts.
        \row
            \li Description
            \li String or array of strings
            \li Possibly multi-line description of what the plugin is supposed
                to provide.
                Should still be kept relatively short, since the UI is not
                designed for long texts.
        \row
            \li Url
            \li String
            \li Link to further information about the plugin, like
                \c{http://www.mycompany-online.com/products/greatplugin}.
    \endtable

    \section2 Dependencies

    A plugin can have dependencies on other plugins. These are
    specified in the plugin meta data, to ensure that
    these other plugins are loaded before this plugin.

    Dependencies are declared with the key \c Dependency, which contains an array of JSON objects
    with required keys \c Name and \c Version, and optional key \c Type.

    The following formulas illustrate how the dependency information is matched.
    In the formulas the name of the required plugin (as defined in the \c Name of the dependency
    object) is denoted as \c DependencyName
    and the required version of the plugin is denoted as \c DependencyVersion.
    A plugin with given \c Name, \c Version and \c CompatVersion
    as defined in the plugin meta data matches
    the dependency if
    \list
        \li its \c Name matches \c DependencyName, and
        \li \c {CompatVersion <= DependencyVersion <= Version}.
    \endlist
    For example a dependency
    \code
        {
            "Name" : "SomeOtherPlugin",
            "Version" : "2.3.0_2"
        }
    \endcode
    would be matched by a plugin with
    \code
        {
            "Name" : "SomeOtherPlugin",
            "Version" : "3.1.0",
            "CompatVersion" : "2.2.0",
            ...
        }
    \endcode
    since the name matches, and the version \c{2.3.0_2} given in the dependency tag
    lies in the range of \c{2.2.0} and \c{3.1.0}.

    \table
        \header
            \li Key
            \li Value Type
            \li Meaning
        \row
            \li Dependencies
            \li Array of dependency objects
            \li Describes the dependencies on other plugins.
    \endtable

    A dependency object is a JSON object with the following keys:
    \table
        \header
            \li Key
            \li Value Type
            \li Meaning
        \row
            \li Name
            \li String
            \li The name of the plugin, on which this plugin relies.
        \row
            \li Version
            \li String
            \li The version to which the plugin must be compatible to
                fill the dependency, in the form \c{x.y.z_n}.
                Can be empty if the version does not matter.
        \row
            \li Type
            \li String
            \li Optional. Value \c Required, \c Optional, or \c Test. Defines if the dependency is
                a hard requirement, optional, or required for running the plugin's tests.
                Defaults to \c{Required}.
    \endtable

    \section3 Optional Dependencies

    A plugin can specify that a dependency on another plugin is optional, by adding
    \c {"Type" : "Optional"} to the dependency object:

    \list
        \li If the dependency can be resolved, the plugin and
            its dependency are loaded and initialized as for \c Required dependencies.
        \li If the dependency cannot be resolved, the plugin is loaded and initialized
            as if the dependency was not declared at all.
    \endlist

    The plugin is not informed about the existence of optional dependencies in any way. Since the
    dependency might be loaded or not, the plugin may also not link against the dependency.
    A common way to access objects from optional dependencies is to get the object from the
    \l{The Plugin Manager, the Object Pool, and Registered Objects}{global object pool}
    via ExtensionSystem::PluginManager::getObjectByName() and use QMetaObject functions to call
    functions on it.

    \section3 Test Dependencies

    When the user runs the application with the \c{-test} command line argument, only
    the specified plugins and their dependencies are loaded. This is done in order to
    speed up the execution of tests by avoiding the loading of unneeded plugins.

    A plugin can specify additional dependencies that are required for running its
    tests, but not for its normal execution, by declaring dependencies with
    \c {"Type" : "Test"}. Test dependencies are force loaded, and do not affect load order.

    This type of dependency is not transitive.

    \section2 Command Line Arguments

    Plugins can register command line arguments that the user can give
    when starting the application. These command line arguments are shown
    with a one-line description when the user runs the application with
    the \c{-help} command line argument, and the plugin manager does its command
    line parsing and sanity checks based on that information.
    If the plugin manager finds matching command line arguments for a plugin,
    it passes them on to the plugin's
    \l{ExtensionSystem::IPlugin::initialize()}{initialize()} function.

    Command line arguments are defined through the key \c Arguments, which contains an array
    of argument objects. Each individual argument object has the required key \c Name, and
    optional keys \c Parameter and \c Description.

    \table
        \header
            \li Key
            \li Value Type
            \li Meaning
        \row
            \li Arguments
            \li Array of argument objects
            \li Describes the command line arguments that the plugin wants to handle.
    \endtable

    An argument object is a JSON object with the following keys:
    \table
        \header
            \li Key
            \li Value Type
            \li Meaning
        \row
            \li Name
            \li String
            \li The command line argument itself, including the \c - prefix, e.g.
                \c{-my-parameter}.
        \row
            \li Parameter
            \li String
            \li Optional. If this is given, the command line argument expects an
                additional parameter, e.g. \c{-my-parameter somevalue}. The
                value of this attribute is used as a very short description of the
                parameter for the user.
        \row
            \li Description
            \li String
            \li Optional. A (one-line) description of the argument for the command line argument help.
    \endtable

    \section2 Example \c Test.json

    \code
    {
        "Name" : "Test",
        "Version" : "1.0.1",
        "CompatVersion" : "1.0.0",
        "Vendor" : "My Company",
        "Copyright" : "(C) 2016 MyCompany",
        "License" : [
            "This is a default license bla",
            "blubbblubb",
            "end of terms"
        ],
        "Category" : "My Company Additions",
        "Description" : [
            "This plugin is just a test.",
            "It demonstrates the great use of the plugin meta data."
        ],
        "Url" : "http://www.mycompany-online.com/products/greatplugin",
        "Arguments" : [
            {
                "Name" : "-variant",
                "Parameter" : "fancy|boring",
                "Description" : "Brings up the fancy or boring user interface"
            }
        ],
        "Dependencies" : [
            { "Name" : "SomeOtherPlugin", "Version" : "2.3.0_2" },
            { "Name" : "EvenOther", "Version" : "1.0.0" }
        ]
    }
    \endcode

    \section2 A Note on Plugin Versions

    Plugin versions are in the form \c{x.y.z_n} where, \c x, \c y, \c z and \c n are
    non-negative integer numbers. You don't have to specify the version
    in this full form - any left-out part will implicitly be set to zero.
    So, \c{2.10_2} is equal to \c{2.10.0_2}, and \c 1 is the same as \c{1.0.0_0}.

*/
