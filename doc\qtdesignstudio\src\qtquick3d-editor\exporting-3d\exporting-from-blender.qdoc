// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page exporting-from-blender.html
    \previouspage exporting-3d-assets.html
    \nextpage exporting-from-maya.html

    \title Exporting from Blender

    You can export meshes, lights, cameras, transformations (scale, rotation,
    or location), UV layouts, pivot points, object hierarchy, and material
    slots from Blender to \QDS.

    When you import 3D graphics to \QDS, the scenegraph is converted into
    Qt Quick 3D types.

    For best results, export 3D graphics to the GL Transmission Format (qlTF2),
    as instructed in the
    \l{https://docs.blender.org/manual/en/2.81/addons/import_export/io_scene_gltf2.html}
    {qlTF2} section of the Blender documentation.
*/
