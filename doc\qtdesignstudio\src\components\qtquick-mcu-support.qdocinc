// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [mcu qtquick components]

    \section1 Creating UIs for MCUs

    \l{Qt for MCUs} enables you to use subsets of components to create UIs for
    devices that are powered by microcontroller units (MCU). The subset of
    supported components depends on the Qt for MCUs version that you use for
    development. In this manual, we indicate which components are supported at
    the time of writing.

    To develop for MCUs, \l{Creating a Project}{create an MCU project}. Only
    the components available on MCUs are displayed in \l Components. Only a
    subset of properties is supported for the supported components. The
    properties that are not available on MCUs are marked in the \l Properties
    view with strikethrough text.

    \image qmldesigner-mcu-support.png "Components and Text properties supported for MCUs"

    For more information about the supported components and their properties,
    see \l{Qt for MCUs - All QML Types}.

//! [mcu qtquick components]
*/
