// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-configuring.html
    \page creator-build-example-application.html
    \nextpage creator-tutorials.html

    \title Building and Running an Example

    You can test that your \QSDK installation is successful by opening an existing
    example application project.

    To run an example application on an Android or iOS device, you must set up
    the development environment for Android or iOS. For more information, see
    \l{Connecting Android Devices} and \l{Connecting iOS Devices}.

    To run an example application on a Boot2Qt device, you must set up
    Boot2Qt on the development host and create connections
    between the host and devices. For more information, see
    \l{https://doc.qt.io/Boot2Qt/b2qt-installation-guides.html}
    {Boot2Qt: Installation Guides}

    If you have \l{Qt Design Studio Manual}{\QDS} installed, you can open
    \QDS examples from \QC in \QDS.

    \list 1

        \li In the \uicontrol Welcome mode, select \uicontrol Examples (1).

            \image qtcreator-gs-build-example-open.png "Selecting an example"

            If you cannot see any examples, check that the list of
            \l{Adding Qt Versions}{Qt versions} (2) is not empty. If
            you select a Qt for Android or iOS, you can only see the
            examples that run on Android or iOS.

        \li Select an example in the list of examples.

            You can also use tags (3) to filter examples. For instance, enter
            the \uicontrol Boot2Qt tag (commercial only) in the search field
            (4) to list examples that you can run on Boot2Qt devices.

        \li To check that you can compile and link the application code for a
            device, click the \uicontrol {Kit Selector} and select a
            \l{glossary-buildandrun-kit}{kit} for the
            device.

            \image qtcreator-gs-build-example-kit-selector.png "Selecting a kit to build with"

            If you installed \QC as part of a Qt installation, it should have
            automatically detected the installed kit. If you cannot see any kits,
            see \l{Adding Kits}.

        \li Click \inlineimage icons/run_small.png
            (\uicontrol Run) to build and run the application.

        \li To see the compilation progress, press \key{Alt+4} to open
            \l {Compile Output}.

            If build errors occur, check that you have a Qt version, a
            \l{Adding Compilers}{compiler}, and the necessary kits installed. If
            you are building for an \l{Connecting Android Devices}{Android device}
            or \l{Connecting iOS Devices}{iOS device}, check that you set up the
            development environment correctly.

            The \uicontrol Build progress bar on the toolbar turns green when
            you build the project successfully. The application opens on the
            device.

    \endlist

*/
