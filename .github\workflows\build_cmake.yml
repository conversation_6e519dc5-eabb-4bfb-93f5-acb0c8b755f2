name: CMake Build Matrix

on:
  push:
    paths-ignore:
      - 'dist/**'
      - 'doc/**'

env:
  QT_VERSION: 6.4.2
  MACOS_DEPLOYMENT_TARGET: 10.14
  CLANG_VERSION: 15.0.0
  ELFUTILS_VERSION: 0.175
  CMAKE_VERSION: 3.21.1
  NINJA_VERSION: 1.10.2
  BUILD_TYPE: Release
  CCACHE_VERSION: 4.6
  QT_MIRRORS: download.qt.io;mirrors.ocf.berkeley.edu/qt;ftp.fau.de/qtproject;mirror.bit.edu.cn/qtproject

jobs:
  build:
    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
        - {
            name: "Windows Latest MSVC", artifact: "Windows-MSVC",
            os: windows-latest,
            cc: "cl", cxx: "cl",
            environment_script: "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Auxiliary/Build/vcvars64.bat",
            is_msvc: true
          }
        - {
            name: "Windows Latest MinGW", artifact: "Windows-MinGW",
            os: windows-latest,
            toolchain: "https://github.com/cristianadam/mingw-builds/releases/download/v11.2.0-rev1/x86_64-11.2.0-release-posix-seh-rt_v9-rev1.7z",
            toolchain_path: "mingw64/bin",
            cc: "gcc", cxx: "g++",
            is_msvc: false
          }
        - {
            name: "Ubuntu Latest GCC", artifact: "Linux",
            os: ubuntu-latest,
            cc: "gcc", cxx: "g++"
          }
        - {
            name: "macOS Latest Clang", artifact: "macOS",
            os: macos-latest,
            cc: "clang", cxx: "clang++"
          }

    steps:
    - uses: actions/checkout@v3
    - name: Checkout submodules
      run: |
        git submodule set-url -- perfparser https://code.qt.io/qt-creator/perfparser.git
        git submodule update --init --recursive

    - name: Download Ninja and CMake
      shell: cmake -P {0}
      run: |
        set(cmake_version "$ENV{CMAKE_VERSION}")
        set(ninja_version "$ENV{NINJA_VERSION}")

        if ("${{ runner.os }}" STREQUAL "Windows")
          set(ninja_suffix "win.zip")
          set(cmake_suffix "windows-x86_64.zip")
          set(cmake_dir "cmake-${cmake_version}-windows-x86_64/bin")
        elseif ("${{ runner.os }}" STREQUAL "Linux")
          set(ninja_suffix "linux.zip")
          set(cmake_suffix "linux-x86_64.tar.gz")
          set(cmake_dir "cmake-${cmake_version}-linux-x86_64/bin")
        elseif ("${{ runner.os }}" STREQUAL "macOS")
          set(ninja_suffix "mac.zip")
          set(cmake_suffix "macos-universal.tar.gz")
          set(cmake_dir "cmake-${cmake_version}-macos-universal/CMake.app/Contents/bin")
        endif()

        set(ninja_url "https://github.com/ninja-build/ninja/releases/download/v${ninja_version}/ninja-${ninja_suffix}")
        foreach(retry RANGE 10)
          file(DOWNLOAD "${ninja_url}" ./ninja.zip SHOW_PROGRESS)
          file(SIZE ./ninja.zip fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()

        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./ninja.zip)

        set(cmake_url "https://github.com/Kitware/CMake/releases/download/v${cmake_version}/cmake-${cmake_version}-${cmake_suffix}")
        foreach(retry RANGE 10)
          file(DOWNLOAD "${cmake_url}" ./cmake.zip SHOW_PROGRESS)
          file(SIZE ./cmake.zip fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./cmake.zip)

        # Add to PATH environment variable
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}/${cmake_dir}" cmake_dir)
        set(path_separator ":")
        if ("${{ runner.os }}" STREQUAL "Windows")
          set(path_separator ";")
        endif()
        file(APPEND "$ENV{GITHUB_PATH}" "$ENV{GITHUB_WORKSPACE}${path_separator}${cmake_dir}")

        if (NOT "${{ runner.os }}" STREQUAL "Windows")
          execute_process(
            COMMAND chmod +x ninja
            COMMAND chmod +x ${cmake_dir}/cmake
          )
        endif()

    - name: Install system libs
      shell: cmake -P {0}
      run: |
        if ("${{ runner.os }}" STREQUAL "Linux")
          execute_process(
            COMMAND sudo apt update
          )
          execute_process(
            COMMAND
              sudo apt install chrpath
                               libgl1-mesa-dev libvulkan-dev libxcb-xinput-dev libxcb-xinerama0-dev libxkbcommon-dev libxkbcommon-x11-dev
                               libxcb-image0 libxcb-keysyms1 libxcb-render-util0 libxcb-xkb1 libxcb-randr0 libxcb-icccm4
                               xvfb
            RESULT_VARIABLE result
          )
          if (NOT result EQUAL 0)
            message(FATAL_ERROR "Failed to install dependencies")
          endif()
        endif()

        if ("${{ runner.os }}" STREQUAL "Windows")
          file(MAKE_DIRECTORY build/build/bin)
          foreach(retry RANGE 10)
            file(DOWNLOAD "https://download.qt.io/development_releases/prebuilt/llvmpipe/windows/opengl32sw-64.7z" ./opengl32sw-64.7z SHOW_PROGRESS)
            file(SIZE ./opengl32sw-64.7z fileSize)
            if (fileSize GREATER 0)
              break()
            endif()
          endforeach()
          execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ../../../opengl32sw-64.7z WORKING_DIRECTORY build/build/bin)
        endif()

        if (NOT "x${{ matrix.config.toolchain }}" STREQUAL "x")
          foreach(retry RANGE 10)
            file(DOWNLOAD "${{ matrix.config.toolchain }}" ./toolchain.7z SHOW_PROGRESS)
            file(SIZE ./toolchain.7z fileSize)
            if (fileSize GREATER 0)
              break()
            endif()
          endforeach()
          execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./toolchain.7z)
        endif()

        # For tests update the docker image
        find_program(docker_executable docker)
        if (docker_executable)
          execute_process(COMMAND ${docker_executable} pull alpine:latest COMMAND_ECHO STDOUT)
        endif()

    - name: Download Qt
      id: qt
      shell: cmake -P {0}
      run: |
        set(qt_version "$ENV{QT_VERSION}")

        string(REPLACE "." "" qt_version_dotless "${qt_version}")
        if ("${{ runner.os }}" STREQUAL "Windows")
          set(url_os "windows_x86")
          if ("x${{ matrix.config.environment_script }}" STREQUAL "x")
            set(qt_package_arch_suffix "win64_mingw")
            set(qt_dir_prefix "${qt_version}/mingw_64")
            set(qt_package_suffix "-Windows-Windows_10_21H2-Mingw-Windows-Windows_10_21H2-X86_64")
          elseif ("${{ matrix.config.environment_script }}" MATCHES "vcvars64.bat")
            set(qt_package_arch_suffix "win64_msvc2019_64")
            set(qt_dir_prefix "${qt_version}/msvc2019_64")
            set(qt_package_suffix "-Windows-Windows_10_21H2-MSVC2019-Windows-Windows_10_21H2-X86_64")
          endif()
        elseif ("${{ runner.os }}" STREQUAL "Linux")
          set(url_os "linux_x64")
          set(qt_package_arch_suffix "gcc_64")
          set(qt_dir_prefix "${qt_version}/gcc_64")
          set(qt_package_suffix "-Linux-RHEL_8_4-GCC-Linux-RHEL_8_4-X86_64")
        elseif ("${{ runner.os }}" STREQUAL "macOS")
          set(url_os "mac_x64")
          set(qt_package_arch_suffix "clang_64")
          set(qt_dir_prefix "${qt_version}/macos")
          set(qt_package_suffix "-MacOS-MacOS_12-Clang-MacOS-MacOS_12-X86_64-ARM64")
        endif()

        set(qt_base_url "https://\${qt_mirror}/online/qtsdkrepository/${url_os}/desktop/qt6_${qt_version_dotless}")
        foreach(qt_mirror $ENV{QT_MIRRORS})
          cmake_language(EVAL CODE "
            message(\"Downloading: ${qt_base_url}/Updates.xml\")
            file(DOWNLOAD \"${qt_base_url}/Updates.xml\" ./Updates.xml)
          ")
          file(SIZE ./Updates.xml fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()

        file(READ ./Updates.xml updates_xml)
        string(REGEX MATCH "<Name>qt.qt6.*<Version>([0-9+-.]+)</Version>" updates_xml_output "${updates_xml}")
        set(qt_package_version ${CMAKE_MATCH_1})

        file(MAKE_DIRECTORY qt6)

        # Save the path for other steps
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}/qt6/${qt_dir_prefix}" qt_dir)
        file(APPEND "$ENV{GITHUB_OUTPUT}" "qt_dir=${qt_dir}")

        function(downloadAndExtract url archive)
          foreach(qt_mirror $ENV{QT_MIRRORS})
            cmake_language(EVAL CODE "
              message(\"Downloading ${url}\")
              file(DOWNLOAD \"${url}\" ./${archive})
            ")
            file(SIZE ./${archive} fileSize)
            if (fileSize GREATER 0)
              break()
            endif()
          endforeach()
          execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ../${archive} WORKING_DIRECTORY qt6)
        endfunction()

        foreach(package qtbase qtdeclarative qttools qtsvg qttranslations)
          downloadAndExtract(
            "${qt_base_url}/qt.qt6.${qt_version_dotless}.${qt_package_arch_suffix}/${qt_package_version}${package}${qt_package_suffix}.7z"
            ${package}.7z
          )
        endforeach()

        foreach(package qtimageformats qtserialport)
          downloadAndExtract(
            "${qt_base_url}/qt.qt6.${qt_version_dotless}.addons.${package}.${qt_package_arch_suffix}/${qt_package_version}${package}${qt_package_suffix}.7z"
            ${package}.7z
          )
        endforeach()

        foreach(package qtquicktimeline qtquick3d qt5compat qtshadertools)
          downloadAndExtract(
            "${qt_base_url}/qt.qt6.${qt_version_dotless}.${package}.${qt_package_arch_suffix}/${qt_package_version}${package}${qt_package_suffix}.7z"
            ${package}.7z
          )
        endforeach()

        # uic depends on libicu56.so
        if ("${{ runner.os }}" STREQUAL "Linux")
          downloadAndExtract(
            "${qt_base_url}/qt.qt6.${qt_version_dotless}.${qt_package_arch_suffix}/${qt_package_version}icu-linux-Rhel7.2-x64.7z"
            icu.7z
          )
        endif()

        file(READ "qt6/${qt_dir_prefix}/mkspecs/qconfig.pri" qtconfig)
        string(REPLACE "Enterprise" "OpenSource" qtconfig "${qtconfig}")
        string(REPLACE "licheck.exe" "" qtconfig "${qtconfig}")
        string(REPLACE "licheck64" "" qtconfig "${qtconfig}")
        string(REPLACE "licheck_mac" "" qtconfig "${qtconfig}")
        file(WRITE "qt6/${qt_dir_prefix}/mkspecs/qconfig.pri" "${qtconfig}")

        if ("${{ runner.os }}" STREQUAL "Windows")
          # deploy "system" runtimes into Qt, so they get deployed as well
          if ("x${{ matrix.config.environment_script }}" STREQUAL "x")
            # deploy MinGW
            foreach(file libwinpthread-1.dll libstdc++-6.dll libgcc_s_seh-1.dll)
              file(INSTALL "$ENV{GITHUB_WORKSPACE}/${{ matrix.config.toolchain_path }}/${file}"
                   DESTINATION "qt6/${qt_dir_prefix}/bin"
                   USE_SOURCE_PERMISSIONS)
            endforeach()
          else()
            # deploy MSVC
            foreach(file vcruntime140.dll concrt140.dll msvcp140_1.dll msvcp140_2.dll
                         msvcp140_codecvt_ids.dll vcruntime140_1.dll msvcp140.dll)
              file(INSTALL "C:/Windows/System32/${file}"
                   DESTINATION "qt6/${qt_dir_prefix}/bin")
            endforeach()
          endif()
        endif()

    - name: Download OpenSSL
      shell: cmake -P {0}
      run: |
        if ("${{ runner.os }}" STREQUAL "Windows")
          set(url_os "windows_x86")
          set(openssl_localdir "Tools/OpenSSL/Win_x64/bin")
          set(openssl_dest_dir "${{ steps.qt.outputs.qt_dir }}/bin")
          set(shared_suffix ".dll")
        elseif ("${{ runner.os }}" STREQUAL "Linux")
          set(url_os "linux_x64")
          set(openssl_localdir "Tools/OpenSSL/binary/lib")
          set(openssl_dest_dir "${{ steps.qt.outputs.qt_dir }}/lib/Qt/lib")
          set(shared_suffix ".so*")
        elseif ("${{ runner.os }}" STREQUAL "macOS")
          # Not needed on macOS
          return()
        endif()

        set(openssl_base_url "https://\${qt_mirror}/online/qtsdkrepository/${url_os}/desktop/tools_openssl_x64")
        foreach(qt_mirror $ENV{QT_MIRRORS})
          cmake_language(EVAL CODE "
            message(\"Downloading ${openssl_base_url}/Updates.xml}\")
            file(DOWNLOAD \"${openssl_base_url}/Updates.xml\" ./Updates.xml)
          ")
          file(SIZE ./Updates.xml fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()

        file(READ ./Updates.xml updates_xml)
        string(REGEX MATCH
          "<Name>(qt.tools.openssl.*)</Name>.*<Version>([0-9+-.]+)</Version>.*<DownloadableArchives>(.*)</DownloadableArchives>" updates_xml_output "${updates_xml}")

        set(openssl_directory ${CMAKE_MATCH_1})
        set(openssl_version ${CMAKE_MATCH_2})
        set(openssl_archive ${CMAKE_MATCH_3})

        set(url "${openssl_base_url}/${openssl_directory}/${openssl_version}${openssl_archive}")

        file(MAKE_DIRECTORY openssl)
        file(MAKE_DIRECTORY ${openssl_dest_dir})

        foreach(qt_mirror $ENV{QT_MIRRORS})
          cmake_language(EVAL CODE "
            message(\"Downloading ${url}\")
            file(DOWNLOAD \"${url}\" ./openssl.7z)
          ")
          file(SIZE ./openssl.7z fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ../openssl.7z WORKING_DIRECTORY openssl)

        file(GLOB openssl_shared_objects "openssl/${openssl_localdir}/*${shared_suffix}")
        execute_process(
          COMMAND ${CMAKE_COMMAND} -E copy ${openssl_shared_objects} ${openssl_dest_dir})

    - name: Download libclang
      id: libclang
      shell: cmake -P {0}
      run: |
        set(clang_version "$ENV{CLANG_VERSION}")

        if ("${{ runner.os }}" STREQUAL "Windows")
          if ("x${{ matrix.config.environment_script }}" STREQUAL "x")
            set(libclang "libclang-release_${clang_version}-based-windows-mingw_64.7z")
          elseif ("${{ matrix.config.environment_script }}" MATCHES "vcvars64.bat")
            set(libclang "libclang-release_${clang_version}-based-windows-vs2019_64.7z")
          elseif ("${{ matrix.config.environment_script }}" MATCHES "vcvars32.bat")
            set(libclang "libclang-release_${clang_version}-based-windows-vs2019_32.7z")
          endif()
        elseif ("${{ runner.os }}" STREQUAL "Linux")
          set(libclang "libclang-release_${clang_version}-based-linux-Ubuntu20.04-gcc9.3-x86_64.7z")
        elseif ("${{ runner.os }}" STREQUAL "macOS")
          set(libclang "libclang-release_${clang_version}-based-macos-universal.7z")
        endif()

        set(libclang_url "https://\${qt_mirror}/development_releases/prebuilt/libclang/${libclang}")
        foreach(qt_mirror $ENV{QT_MIRRORS})
          cmake_language(EVAL CODE "
            message(\"Downloading ${libclang_url}\")
            file(DOWNLOAD \"${libclang_url}\" ./libclang.7z)
          ")
          file(SIZE ./libclang.7z fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()

        # Comes with libclang inside the archive
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./libclang.7z)

        # Save the path for other steps
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}/libclang" libclang_dir)
        file(APPEND "$ENV{GITHUB_OUTPUT}" "libclang_dir=${libclang_dir}")

    - name: Download elfutils
      if: runner.os == 'Windows' && matrix.config.is_msvc || runner.os == 'Linux'
      id: elfutils
      shell: cmake -P {0}
      run: |
        set(elfutils_version "$ENV{ELFUTILS_VERSION}")

        if ("${{ runner.os }}" STREQUAL "Windows")
          if ("${{ matrix.config.environment_script }}" MATCHES "vcvars64.bat")
            set(elfutils "elfutils-release_${elfutils_version}qt-windows-x86_64.7z")
          elseif ("${{ matrix.config.environment_script }}" MATCHES "vcvars32.bat")
            set(elfutils "elfutils-release_${elfutils_version}qt-windows-i686.7z")
          endif()
        elseif ("${{ runner.os }}" STREQUAL "Linux")
          set(elfutils "elfutils-release_${elfutils_version}qt-linux-x86_64.7z")
        endif()

        set(elfutils_url "https://\${qt_mirror}/development_releases/prebuilt/elfutils/${elfutils}")

        foreach(qt_mirror $ENV{QT_MIRRORS})
          cmake_language(EVAL CODE "
            message(\"Downloading ${elfutils_url}\")
            file(DOWNLOAD \"${elfutils_url}\" ./elfutils.7z)
          ")
          file(SIZE ./elfutils.7z fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()
        file(MAKE_DIRECTORY elfutils)
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ../elfutils.7z WORKING_DIRECTORY elfutils)

        # Save the path for other steps
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}/elfutils" elfutils_dir)
        file(APPEND "$ENV{GITHUB_OUTPUT}" "elfutils_dir=${elfutils_dir}")

    - name: Download ccache
      shell: cmake -P {0}
      run: |
        set(ccache_url "https://github.com/cristianadam/ccache/releases/download/v$ENV{CCACHE_VERSION}/${{ runner.os }}.tar.xz")
        foreach(retry RANGE 10)
          file(DOWNLOAD "${ccache_url}" ./ccache.tar.xz SHOW_PROGRESS)
          file(SIZE ./ccache.tar.xz fileSize)
          if (fileSize GREATER 0)
            break()
          endif()
        endforeach()
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./ccache.tar.xz)

    - name: Prepare ccache archive name
      id: ccache
      shell: cmake -P {0}
      run: |
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}" github_workspace)
        include(${github_workspace}/cmake/QtCreatorIDEBranding.cmake)
        string(REPLACE "." ";" IDE_VERSION_LIST ${IDE_VERSION_DISPLAY})
        list(GET IDE_VERSION_LIST 0 IDE_VERSION_MAJOR)
        file(APPEND "$ENV{GITHUB_OUTPUT}" "archive_name=ccache-${{ matrix.config.os }}-${{ matrix.config.cc }}-qtc${IDE_VERSION_MAJOR}")

    - name: Download ccache archive
      shell: cmake -P {0}
      run: |
        file(WRITE $ENV{GITHUB_WORKSPACE}/netrc.txt
          "default login runneradmin password ${{ secrets.GITHUB_TOKEN }}")

        foreach(page_id RANGE 1 10)
          file(
            DOWNLOAD "https://api.github.com/repos/${{ github.repository }}/actions/artifacts?per_page=100&page=${page_id}"
            HTTPHEADER "Accept: application/vnd.github.v3+json"
            NETRC_FILE "$ENV{GITHUB_WORKSPACE}/netrc.txt"
            NETRC REQUIRED
            SHOW_PROGRESS
            artifacts.json)
          file(READ artifacts.json artifacts_json)

          string(JSON artifacts_length LENGTH "${artifacts_json}" "artifacts")
          math(EXPR artifacts_length "${artifacts_length} - 1")
          if(${artifacts_length} GREATER_EQUAL 0)
            foreach(idx RANGE 0 ${artifacts_length})
              string(JSON artifact_js GET "${artifacts_json}" "artifacts" ${idx})
              string(JSON name GET "${artifact_js}" "name")
              if ("${name}" STREQUAL "${{ steps.ccache.outputs.archive_name }}")
                string(JSON download_url GET "${artifact_js}" "archive_download_url")

                foreach(retry RANGE 10)
                  file(DOWNLOAD "${download_url}"
                    "${{ steps.ccache.outputs.archive_name }}.zip"
                    NETRC_FILE "$ENV{GITHUB_WORKSPACE}/netrc.txt"
                    NETRC REQUIRED
                    SHOW_PROGRESS)
                  file(SIZE "${{ steps.ccache.outputs.archive_name }}.zip" fileSize)
                  if (fileSize GREATER 0)
                    break()
                  endif()
                endforeach()

                execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf "${{ steps.ccache.outputs.archive_name }}.zip")
                file(MAKE_DIRECTORY .ccache)
                execute_process(COMMAND ${CMAKE_COMMAND} -E tar xf "../${{ steps.ccache.outputs.archive_name }}.tar" WORKING_DIRECTORY .ccache)

                return()
              endif()
            endforeach()
          endif()
        endforeach()

    - name: Build
      shell: cmake -P {0}
      run: |
        set(ENV{CC} ${{ matrix.config.cc }})
        set(ENV{CXX} ${{ matrix.config.cxx }})
        set(ENV{MACOSX_DEPLOYMENT_TARGET} "${{ env.MACOS_DEPLOYMENT_TARGET }}")

        if ("${{ runner.os }}" STREQUAL "Windows" AND NOT "x${{ matrix.config.environment_script }}" STREQUAL "x")
          execute_process(
            COMMAND "${{ matrix.config.environment_script }}" && set
            OUTPUT_FILE environment_script_output.txt
          )
          file(STRINGS environment_script_output.txt output_lines)
          foreach(line IN LISTS output_lines)
            if (line MATCHES "^([a-zA-Z0-9_-]+)=(.*)$")
              set(ENV{${CMAKE_MATCH_1}} "${CMAKE_MATCH_2}")
            endif()
          endforeach()
        endif()

        set(ENV{NINJA_STATUS} "[%f/%t %o/sec] ")

        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}" ccache_basedir)
        set(ENV{CCACHE_BASEDIR} "${ccache_basedir}")
        set(ENV{CCACHE_DIR} "${ccache_basedir}/.ccache")
        set(ENV{CCACHE_SLOPPINESS} "pch_defines,time_macros")
        set(ENV{CCACHE_COMPRESS} "true")
        set(ENV{CCACHE_COMPRESSLEVEL} "9")
        set(ENV{CCACHE_MAXSIZE} "400M")
        if ("${{ matrix.config.cxx }}" STREQUAL "cl")
          set(ENV{CCACHE_MAXSIZE} "600M")
        endif()

        execute_process(
          COMMAND git log --format=%B -n 1 ${{ github.event.after }}
          OUTPUT_VARIABLE git_commit_msg
        )
        if ("${git_commit_msg}" MATCHES "ccache:[ ]*clea[r|n]")
          execute_process(COMMAND ccache --clear COMMAND_ECHO STDOUT)
        endif()

        execute_process(COMMAND ccache -p)
        execute_process(COMMAND ccache -z)

        if ("x${{ matrix.config.environment_script }}" STREQUAL "x")
          set(CDB_OPTION "--no-cdb")
        else()
          set(CDB_OPTION)
        endif()

        if (NOT "${{ steps.elfutils.outputs.elfutils_dir }}" STREQUAL "")
          set(ELFUTILS_OPTION "--elfutils-path;${{ steps.elfutils.outputs.elfutils_dir }}")
        else()
          set(ELFUTILS_OPTION)
        endif()

        find_package(Python3 REQUIRED COMPONENTS Interpreter)
        string(REPLACE "x86" "x64" Python3_EXECUTABLE "${Python3_EXECUTABLE}")

        set(WITH_TESTS "--with-tests")
        set(NO_DMG "--no-dmg")
        if (${{github.ref}} MATCHES "tags/v")
          unset(WITH_TESTS)
          unset(NO_DMG)
        endif()

        if (NOT "x${{ matrix.config.toolchain_path }}" STREQUAL "x")
          set(path_separator ":")
          if ("${{ runner.os }}" STREQUAL "Windows")
            set(path_separator ";")
          endif()
          set(ENV{PATH} "$ENV{GITHUB_WORKSPACE}/${{ matrix.config.toolchain_path }}${path_separator}$ENV{PATH}")
        endif()

        execute_process(
          COMMAND python
            -u
            scripts/build.py
            --build-type Release
            --src .
            --build build
            --qt-path "${{ steps.qt.outputs.qt_dir }}"
            --llvm-path "${{ steps.libclang.outputs.libclang_dir }}"
            --python3 "${Python3_EXECUTABLE}"
            ${WITH_TESTS}
            ${CDB_OPTION}
            ${ELFUTILS_OPTION}
            ${NO_DMG}
            --add-config=-DCMAKE_C_COMPILER_LAUNCHER=ccache
            --add-config=-DCMAKE_CXX_COMPILER_LAUNCHER=ccache
            --add-config=-DIDE_REVISION_URL=https://github.com/$ENV{GITHUB_REPOSITORY}/commits/$ENV{GITHUB_SHA}
            --zip-infix=-${{ matrix.config.artifact }}-${{ github.run_id }}
            --no-qbs
            --with-cpack
          RESULT_VARIABLE result
          COMMAND_ECHO STDOUT
          OUTPUT_VARIABLE output
          ERROR_VARIABLE output
          ECHO_OUTPUT_VARIABLE ECHO_ERROR_VARIABLE
        )
        if (NOT result EQUAL 0)
          string(REGEX MATCH "FAILED:.*$" error_message "${output}")
          string(REPLACE "\n" "%0A" error_message "${error_message}")
          message("::error::${error_message}")
          message(FATAL_ERROR "Build failed")
        endif()

    - name: ccache statistics
      shell: cmake -P {0}
      run: |
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}" ccache_basedir)
        set(ENV{CCACHE_BASEDIR} "${ccache_basedir}")
        set(ENV{CCACHE_DIR} "${ccache_basedir}/.ccache")
        set(ENV{CCACHE_SLOPPINESS} "pch_defines,time_macros")
        set(ENV{CCACHE_COMPRESS} "true")
        set(ENV{CCACHE_COMPRESSLEVEL} "9")
        set(ENV{CCACHE_MAXSIZE} "400M")
        if ("${{ matrix.config.cxx }}" STREQUAL "cl")
          set(ENV{CCACHE_MAXSIZE} "600M")
        endif()
        execute_process(COMMAND ccache -s)

    - name: Run tests
      shell: cmake -P {0}
      run: |
        include(ProcessorCount)
        ProcessorCount(N)

        set(ENV{CTEST_OUTPUT_ON_FAILURE} "ON")

        if ("${{ runner.os }}" STREQUAL "Linux")
          set(ENV{DISPLAY} ":1")
          set(ENV{LIBGL_ALWAYS_SOFTWARE} "1")
          set(ctest_launcher xvfb-run --auto-servernum --server-num=1)
        elseif ("${{ runner.os }}" STREQUAL "Windows")
          set(ENV{QT_OPENGL} "software")
          set(ENV{QT_ASSUME_STDERR_HAS_CONSOLE} "1")
        endif()

        execute_process(
          COMMAND ${ctest_launcher} ctest -j ${N} --timeout 300 --label-exclude exclude_from_precheck --exclude-regex tst_perfdata
          WORKING_DIRECTORY build/build
          RESULT_VARIABLE result
          OUTPUT_VARIABLE output
          ERROR_VARIABLE output
          ECHO_OUTPUT_VARIABLE ECHO_ERROR_VARIABLE
          TIMEOUT 600
        )
        if (NOT result EQUAL 0)
          string(REGEX MATCH "[0-9]+% tests.*[0-9.]+ sec.*$" test_results "${output}")
          string(REPLACE "\n" "%0A" test_results "${test_results}")
          # Do not fail on ctest failure
          message("::warning::${test_results}")
        endif()

    - name: Upload
      uses: actions/upload-artifact@v3
      with:
        path: build/qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.7z

    - name: Upload Devel
      uses: actions/upload-artifact@v3
      with:
        path: build/qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}_dev.7z
        name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}_dev.7z

    - name: Upload wininterrupt
      if: runner.os == 'Windows'
      uses: actions/upload-artifact@v3
      with:
        path: build/wininterrupt-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        name: wininterrupt-${{ matrix.config.artifact }}-${{ github.run_id }}.7z

    - name: Upload qtcreatorcdbext
      if: runner.os == 'Windows' && matrix.config.is_msvc
      uses: actions/upload-artifact@v3
      with:
        path: build/qtcreatorcdbext-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        name: qtcreatorcdbext-${{ matrix.config.artifact }}-${{ github.run_id }}.7z

    - name: Upload Debian package
      if: runner.os == 'Linux'
      uses: actions/upload-artifact@v3
      with:
        path: build/build/qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.deb
        name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.deb

    - name: Upload disk image
      if: runner.os == 'macOS' && contains(github.ref, 'tags/v')
      uses: actions/upload-artifact@v3
      with:
        path: build/qt-creator-${{ matrix.config.artifact }}-${{ github.run_id }}.dmg
        name: qt-creator-${{ matrix.config.artifact }}-${{ github.run_id }}.dmg

    - name: Create ccache archive
      working-directory: .ccache
      run: cmake -E tar cf ../${{ steps.ccache.outputs.archive_name }}.tar .

    - name: Upload ccache archive
      uses: actions/upload-artifact@v3
      with:
        path: ./${{ steps.ccache.outputs.archive_name }}.tar
        name: ${{ steps.ccache.outputs.archive_name }}

  release:
    if: contains(github.ref, 'tags/v')
    runs-on: ubuntu-latest
    needs: build

    steps:
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false

    - name: Store Release url
      run: |
        echo "${{ steps.create_release.outputs.upload_url }}" > ./upload_url

    - uses: actions/upload-artifact@v3
      with:
        path: ./upload_url
        name: upload_url

  publish:
    if: contains(github.ref, 'tags/v')
    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
        - {
            name: "Windows Latest MSVC", artifact: "Windows-MSVC",
            os: ubuntu-latest
          }
        - {
            name: "Windows Latest MinGW", artifact: "Windows-MinGW",
            os: ubuntu-latest
          }
        - {
            name: "Ubuntu Latest GCC", artifact: "Linux",
            os: ubuntu-latest
          }
        - {
            name: "macOS Latest Clang", artifact: "macOS",
            os: ubuntu-latest
          }
    needs: release

    steps:
    - name: Download artifact
      uses: actions/download-artifact@v3
      with:
        name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        path: ./

    - name: Download Devel artifact
      uses: actions/download-artifact@v3
      with:
        name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}_dev.7z
        path: ./

    - name: Download wininterrupt artifact
      if: contains(matrix.config.artifact, 'Windows')
      uses: actions/download-artifact@v3
      with:
        name: wininterrupt-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        path: ./

    - name: Download qtcreatorcdbext artifact
      if: matrix.config.artifact == 'Windows-MSVC'
      uses: actions/download-artifact@v3
      with:
        name: qtcreatorcdbext-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        path: ./

    - name: Download Debian package artifact
      if: matrix.config.artifact == 'Linux'
      uses: actions/download-artifact@v3
      with:
        name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.deb
        path: ./

    - name: Download disk image artifact
      if: matrix.config.artifact == 'macOS'
      uses: actions/download-artifact@v3
      with:
        name: qt-creator-${{ matrix.config.artifact }}-${{ github.run_id }}.dmg
        path: ./

    - name: Download URL
      uses: actions/download-artifact@v3
      with:
        name: upload_url
        path: ./
    - id: set_upload_url
      run: |
        upload_url=`cat ./upload_url`
        echo upload_url=$upload_url >> $GITHUB_OUTPUT

    - name: Upload to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.set_upload_url.outputs.upload_url }}
        asset_path: ./qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        asset_name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        asset_content_type: application/x-gtar

    - name: Upload Devel to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.set_upload_url.outputs.upload_url }}
        asset_path: ./qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}_dev.7z
        asset_name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}_dev.7z
        asset_content_type: application/x-gtar

    - name: Upload wininterrupt to Release
      if: contains(matrix.config.artifact, 'Windows')
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.set_upload_url.outputs.upload_url }}
        asset_path: ./wininterrupt-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        asset_name: wininterrupt-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        asset_content_type: application/x-gtar

    - name: Upload qtcreatorcdbext to Release
      if: matrix.config.artifact == 'Windows-MSVC'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.set_upload_url.outputs.upload_url }}
        asset_path: ./qtcreatorcdbext-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        asset_name: qtcreatorcdbext-${{ matrix.config.artifact }}-${{ github.run_id }}.7z
        asset_content_type: application/x-gtar

    - name: Upload Debian package to Release
      if: matrix.config.artifact == 'Linux'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.set_upload_url.outputs.upload_url }}
        asset_path: ./qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.deb
        asset_name: qtcreator-${{ matrix.config.artifact }}-${{ github.run_id }}.deb
        asset_content_type: application/x-gtar

    - name: Upload disk image to Release
      if: matrix.config.artifact == 'macOS'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.set_upload_url.outputs.upload_url }}
        asset_path: ./qt-creator-${{ matrix.config.artifact }}-${{ github.run_id }}.dmg
        asset_name: qt-creator-${{ matrix.config.artifact }}-${{ github.run_id }}.dmg
        asset_content_type: application/x-gtar
