// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage qtcreator-ui-text.html
    \page coding-style.html
    \nextpage qtcreator-api.html

    \title Qt Creator Coding Rules

    \note This document is work in progress.

    The coding rules aim to guide Qt Creator developers, to help them write
    understandable and maintainable code, and to minimize confusion and surprises.

    As usual, rules are not set in stone. If you have a good reason to break one,
    do so. But first make sure that at least some other developers agree with you.

    To contribute to the main Qt Creator source, you should comply to the
    following rules:

    \list
        \li The most important rule is: KISS (keep it short and simple). Always
            choose the simpler implementation option over the more complicated one.
            This makes maintenance a lot easier.
        \li Write good C++ code. That is, readable, well commented when necessary,
            and object-oriented.
        \li Take advantage of Qt. Do not re-invent the wheel. Think about which parts
            of your code are generic enough that they might be incorporated into
            Qt instead of Qt Creator.
        \li Adapt the code to the existing structures in Qt Creator.
            If you have improvement ideas, discuss them with other developers
            before writing the code.
        \li Follow the guidelines in \l{Code Constructs}, \l{Formatting}, and
            \l{Patterns and Practices}.
        \li Document interfaces. Right now we use qdoc, but changing to doxygen
            is being considered.
    \endlist


    \section1 Submitting Code

    To submit code to Qt Creator, you must understand the tools and mechanics as well as
    the philosophy behind Qt development. For more information about how to set up
    the development environment for working on Qt Creator and how to submit code
    and documentation for inclusion, see
    \l{https://wiki.qt.io/Qt_Contribution_Guidelines}{Qt Contribution Guidelines}.

    \section1 Binary and Source Compatibility

    The following list describes how the releases are numbered and defines
    \e {binary compatibility} and \e {source code compatibility} between
    releases:

    \list
        \li \QC 3.0.0 is a \e {major release}, \QC 3.1.0 is a \e {minor
            release}, and \QC 3.1.3 is a \e {patch release}.
        \li \e {Backward binary compatibility} means that code linked to an
            earlier version of the library still works.
        \li \e {Forward binary compatibility} means that code linked to a
            newer version of the library works with an older library.
        \li \e {Source code compatibility} means that code compiles without
            modification.
        \endlist

    We do not currently guarantee binary nor source code
    compatibility between major releases and minor releases.

    However, we try to preserve backward binary compatibility and
    backward source code compatibility for patch releases within the same minor release:
    \list
        \li Soft API freeze: Starting shortly after the beta release of a minor release,
            we start keeping backward source code compatibility within that minor release,
            including its patch releases. This means that from that point, code that uses \QC API will
            compile against the API of all coming versions of this minor release, including its
            patch releases. There still might be occasional exceptions to this rule, which should
            be properly communicated.
        \li Hard API freeze: Starting with the release candidate of a minor release,
            we keep backward source code compatibility within that minor release, including its
            patch releases. We also start keeping backward binary compatibility, with the exception
            that this will not be reflected in the plugins' compatVersion setting. So, \QC plugins
            written against the release candidate will not actually load in the final release or
            later versions, even though the binary compatibility of the libraries would
            theoretically allow for it. See the section about plugin specs below.
        \li Hard ABI freeze: Starting with the final release of a minor release,
            we keep backward source code and binary compatibility for this release and all its
            patch releases.
    \endlist

    For preserving backward compatibility:
    \list
        \li Do not add or remove any public API (for example global functions,
            public/protected/private member functions).
        \li Do not reimplement functions (not even inlines,
            nor protected or private functions).
        \li Check
            \l {https://wiki.qt.io/Binary_Compatibility_Workarounds}{Binary Compatibility Workarounds}
            for ways to preserve binary compatibility.
    \endlist

    For more information on binary compatibility, see
    \l{http://techbase.kde.org/Policies/Binary_Compatibility_Issues_With_C++}{Binary Compatibility Issues With C++}.

    From the viewpoint of \l{Plugin Meta Data}{plugin metadata} this means that:
    \list
        \li \QC plugins in patch releases will have the minor release as \c {compatVersion}. For
            example the plugins from version 3.1.2 will have \c {compatVersion="3.1.0"}.
        \li Pre-releases of the minor release, including the release candidate,
            will still have themselves as the \c {compatVersion}, meaning that plugins compiled
            against the final release will not load in the pre-releases.
        \li \QC plugin developers can decide if their plugin requires a certain patch release
            (or later) of other \QC plugins, or if they work with all patch releases of this minor
            version, by setting the corresponding \c {version} when declaring the dependency on
            the other plugin. The default for \QC plugins provided by the Qt Project is to require
            the latest patch release.
    \endlist

    For example, the \c {Find} plugin in \QC 3.1 beta (internal version number 3.0.82) will have
    \code
        <plugin name="Find" version="3.0.82" compatVersion="3.0.82">
          <dependencyList>
            <dependency name="Core" version="3.0.82"/>
            ....
    \endcode
    The \c {Find} plugin in \QC 3.1.0 final will have
    \code
        <plugin name="Find" version="3.1.0" compatVersion="3.1.0">
          <dependencyList>
            <dependency name="Core" version="3.1.0"/>
            ....
    \endcode
    The \c {Find} plugin in \QC 3.1.1 patch release will have version 3.1.1, will be
    backward binary compatible with \c {Find} plugin version 3.1.0 (\c {compatVersion="3.1.0"}),
    and will require a \c {Core} plugin that is binary backward compatible with \c {Core}
    plugin version 3.1.1:
    \code
        <plugin name="Find" version="3.1.1" compatVersion="3.1.0">
          <dependencyList>
            <dependency name="Core" version="3.1.1"/>
            ....
    \endcode

    \section1 Code Constructs

    Follow the guidelines for code constructs to make the code faster and
    clearer. In addition, the guidelines allow you to take advantage of the strong
    type checking in C++.

    \list
        \li Prefer preincrement to postincrement whenever possible.
            Preincrement is potentially faster than postincrement. Just
            think about the obvious implementations of pre/post-increment. This
            rule applies to decrement too:

            \code
            ++T;
            --U;

            -NOT-

            T++;
            U--;
            \endcode

        \li Try to minimize evaluation of the same code over and over. This is
            aimed especially at loops:

            \code

            Container::iterator end = large.end();
            for (Container::iterator it = large.begin(); it != end; ++it) {
                    ...;
            }

            -NOT-

            for (Container::iterator it = large.begin();
                 it != large.end(); ++it) {
                    ...;
            }
            \endcode

        \endlist

    \section1 Formatting

    \section2 Capitalizing Identifiers

    Use \l{http://en.wikipedia.org/wiki/CamelCase}{camel case} in identifiers.

    Capitalize the first word in an identifier as follows:

    \list
        \li Class names begin with a capital letter.
        \li Function names begin with a lower case letter.
        \li Variable names begin with a lower case letter.
        \li Enum names and values begin with a capital letter. Unscoped Enum
            values contain some part of the name of the enum type.
    \endlist

    \section2 Whitespace

    \list
        \li Use four spaces for indentation, no tabs.
        \li Use blank lines to group statements together where suited.
        \li Always use only one blank line.
    \endlist

    \section3 Pointers and References

    For pointers or references, always use a single space before an asterisk (*)
    or an ampersand (&), but never after.
    Avoid C-style casts when possible:

    \code
    char *blockOfMemory = (char *)malloc(data.size());
    char *blockOfMemory = reinterpret_cast<char *>(malloc(data.size()));

    -NOT-

    char* blockOfMemory = (char* ) malloc(data.size());
    \endcode

    Of course, in this particulare case, using \c new might be an even better
    option.

    \section3  Operator Names and Parentheses

    Do not use spaces between operator names and parentheses. The equation
    marks (==) are a part of the operator name, and therefore, spaces make the
    declaration look like an expression:

    \code
    operator==(type)

    -NOT-

    operator == (type)
    \endcode

    \section3 Function Names and Parentheses

    Do not use spaces between function names and parentheses:

    \code
    void mangle()

    -NOT-

    void mangle ()
    \endcode

    \section3 Keywords

    Always use a single space after a keyword, and before a curly brace:

    \code
    if (foo) {
    }

    -NOT-

    if(foo){
    }
    \endcode

    \section3 Comments

    In general, put one space after "//". To align text in multiline
    comments, you can insert multiple spaces.

    \section2 Braces

    As a base rule, place the left curly brace on the same line as the
    start of the statement:

    \code
    if (codec) {
    }

    -NOT-

    if (codec)
    {
    }
    \endcode

    Exception: Function implementations and class declarations always have
    the left brace in the beginning of a line:

    \code
    static void foo(int g)
    {
        qDebug("foo: %i", g);
    }

    class Moo
    {
    };
    \endcode

    Use curly braces when the body of a conditional statement contains more
    than one line, and also if a single line statement is somewhat complex.
    Otherwise, omit them:

    \code
    if (address.isEmpty())
        return false;

    for (int i = 0; i < 10; ++i)
        qDebug("%i", i);

    -NOT-

    if (address.isEmpty()) {
        return false;
    }

    for (int i = 0; i < 10; ++i) {
        qDebug("%i", i);
    }
    \endcode

    Exception 1: Use braces also if the parent statement covers several
    lines or if it wraps:

    \code
    if (address.isEmpty()
            || !isValid()
            || !codec) {
        return false;
    }
    \endcode

    \note This could be re-written as:

    \code
    if (address.isEmpty())
        return false;

    if (!isValid())
        return false;

    if (!codec)
        return false;
    \endcode

    Exception 2: Use braces also in if-then-else blocks where either the
    if-code or the else-code covers several lines:

    \code
    if (address.isEmpty()) {
        --it;
    } else {
        qDebug("%s", qPrintable(address));
        ++it;
    }

    -NOT-

    if (address.isEmpty())
        --it;
    else {
        qDebug("%s", qPrintable(address));
        ++it;
    }
    \endcode

    \code
    if (a) {
        if (b)
            ...
        else
            ...
    }

    -NOT-

    if (a)
        if (b)
            ...
        else
            ...
    \endcode

    Use curly braces when the body of a conditional statement is empty:

    \code
    while (a) {}

    -NOT-

    while (a);
    \endcode

    \section2 Parentheses

    Use parentheses to group expressions:

    \code
    if ((a && b) || c)

    -NOT-

    if (a && b || c)
    \endcode

    \code
    (a + b) & c

    -NOT-

    a + b & c
    \endcode

    \section2 Line Breaks

    \list
        \li Keep lines shorter than 100 characters.
        \li Insert line breaks if necessary.
        \li Commas go at the end of a broken line.
        \li Operators start at the beginning of the new line.
            \code
            if (longExpression
                || otherLongExpression
                || otherOtherLongExpression) {
            }

            -NOT-

            if (longExpression ||
                otherLongExpression ||
                otherOtherLongExpression) {
            }
            \endcode
    \endlist

    \section2 Declarations

    \list
        \li Use this order for the access sections of your class: public,
            protected, private. The public section is interesting for every
            user of the class. The private section is only of interest for the
            implementors of the class (you).

        \li Avoid declaring global objects in the declaration file of the class.
            If the same variable is used for all objects, use a static member.

        \li Use \c{class} instead of \c{struct}. Some compilers mangle that
            difference into the symbol names and spit out warnings if a struct
            declaration is followed by a class definition. To avoid ongoing
            changes from one to the other we declare \c{class} the preferred way.

    \endlist

    \section3 Declaring Variables

    \list
        \li Avoid global variables of class type to rule out initialization order problems.
            Consider using \c Q_GLOBAL_STATIC if they cannot be avoided.
        \li Declare global string literals as
            \code
            const char aString[] = "Hello";
            \endcode
        \li Avoid short names (such as, a, rbarr, nughdeget) whenever possible.
            Use single-character variable names only for counters and
            temporaries, where the purpose of the variable is obvious.
        \li Declare each variable on a separate line:
            \code
            QString a = "Joe";
            QString b = "Foo";

            -NOT-

            QString a = "Joe", b = "Foo";
            \endcode

            \note \c{QString a = "Joe"} formally calls a copy constructor on a
            temporary that is constructed from a string literal. Therefore, it is
            potentially more expensive than direct construction by
            \c {QString a("Joe")}. However, the compiler is allowed to elide the
            copy (even if this has side effects), and modern compilers typically do
            so. Given these equal costs, Qt Creator code favours the '=' idiom as
            it is in
            line with the traditional C-style initialization, it cannot be
            mistaken as function declaration, and it reduces the level of nested
            parantheses in more initializations.

        \li Avoid abbreviations:

            \code
            int height;
            int width;
            char *nameOfThis;
            char *nameOfThat;

            -NOT-

            int a, b;
            char *c, *d;
            \endcode

        \li Wait with declaring a variable until it is needed. This is especially
            important when initialization is done at the same time.
    \endlist

    \section2 Namespaces

    \list
        \li Put the left curly brace on the same line as the \c namespace keyword.
        \li Do not indent declarations or definitions inside.
        \li Optional, but recommended if the namespaces spans more than a few lines:
            Add a comment after the right curly brace repeating the namespace.

        \code
        namespace MyPlugin {

        void someFunction() { ... }

            }  // namespace MyPlugin
            \endcode

        \li As an exception, if there is only a single class declaration inside
            the namespace, all can go on a single line:

            \code
            namespace MyPlugin { class MyClass; }
            \endcode

        \li Do not use using-directives in header files.

        \li Do not rely on using-directives when defining classes and
        functions, instead define it in a properly named declarative region.

        \li Do not rely on using-directives when accessing global functions.

        \li In other cases, you are encouraged to use using-directives,
        as they help you avoid cluttering the code. Prefer putting all
        using-directives near the top of the file, after all includes.

        \code
        [in foo.cpp]
        ...
        #include "foos.h"
        ...
        #include <utils/filename.h>
        ...
        using namespace Utils;

        namespace Foo::Internal {

        void SomeThing::bar()
        {
            FilePath f;              // or Utils::FilePath f
            ...
        }
        ...
        } // Foo::Internal           // or // namespace Foo::Internal

        -NOT-

        [in foo.h]
        ...
        using namespace Utils;       // Wrong: no using-directives in headers

        class SomeThing
        {
            ...
        };

        -NOT-

        [in foo.cpp]
        ...
        using namespace Utils;

        #include "bar.h"             // Wrong: #include after using-directive

        -NOT-

        [in foo.cpp]
        ...
        using namepace Foo;

        void SomeThing::bar()        // Wrong if Something is in namespace Foo
        {
            ...
        }
        \endcode

    \endlist

    \section1 Patterns and Practices

    \target coding-rules-namespacing
    \section2 Namespacing

    Read \l{https://wiki.qt.io/Qt_In_Namespace}{Qt In Namespace}
    and keep in mind that all of Qt Creator is \e{namespace aware} code.

    The namespacing policy within Qt Creator is as follows:
    \list
        \li Classes/Symbols of a library or plugin that are exported for use of
            other libraries or plugins are in a namespace specific to that
            library/plugin, e.g. \c{MyPlugin}.
        \li Classes/Symbols of a library or plugin that are not exported are in
            an additional \c{Internal} namespace, e.g. \c{MyPlugin::Internal}.
    \endlist

    \section2 Passing File Names

    Qt Creator API expects file names in portable format, that is, with slashes (/)
    instead of backslashes (\\) even on Windows. To pass a file name from the user
    to the API, convert it with QDir::fromNativeSeparators first. To present a file
    name to the user, convert it back to native format with
    QDir::toNativeSeparators. Consider using Utils::FilePath::fromUserInput(QString)
    and Utils::FilePath::toUserOutput() for these tasks.

    Use Utils::FilePath when comparing file names, because that takes case sensitivity into account.
    Also make sure that you compare clean paths (QDir::cleanPath()).


    \section2 Classes to Use and Classes Not to Use

    A significant portion of Qt Creator code handles data on devices that are not
    the same as the development machine. These may differ in aspects like path
    separator, line endings, process launching details and so on.

    However, some basic Qt classes assume that a Qt application is only
    concerned with machines that are similar to the development machine.

    These classes are therefore not appropriate to use in the part of Qt Creator that
    is concerned with non-local code. Instead, Qt Creator's Utils library provides
    substitutes, leading to the following rules:

    \list
        \li Use Utils::FilePath for any QString that semantically is a file or directory,
            see also \l{Passing File Names}.
        \li Prefer using Utils::FilePath over any use of QDir and QFileInfo.
        \li Prefer using Utils::QtcProcess over QProcess.
        \li If Utils::FilePath or Utils::QtcProcess functionality is not sufficient
            for your purpose, prefer enhancing them over falling back to QString
            or QProcess.
        \li Avoid platform #ifdefs unless they are absolutely needed for locally
            executed code, and even then prefer Utils::HostInfo over #ifdefs.
    \endlist


    \section2 Plugin Extension Points

    A plugin extension point is an interface that is provided by one plugin
    to be implemented by others. The plugin then retrieves all
    implementations of the interface and uses them. That is, they \e extend the
    functionality of the plugin. Typically, the
    implementations of the interface are put into the global object pool
    during plugin initialization, and the plugin retrieves them from the
    object pool at the end of plugin initialization.

    For example, the Find plugin provides the FindFilter interface for
    other plugins to implement. With the FindFilter interface, additional search
    scopes can be added, that appear in the \uicontrol {Advanced Search} dialog. The
    Find plugin retrieves all FindFilter implementations from the global
    object pool and presents them in the dialog. The plugin forwards the
    actual search request to the correct FindFilter implementation, which
    then performs the search.

    \section2 Using the Global Object Pool

    You can add objects to the global object pool via
    \l{ExtensionSystem::PluginManager::addObject()}, and retrieve objects
    of a specific type again via
    \l{ExtensionSystem::PluginManager::getObject()}.  This should mostly
    be used for implementations of \l{Plugin Extension Points}.

    \note Do not put a singleton into the pool, and do not retrieve
    it from there. Use the singleton pattern instead.

    \section2 C++ Features

    \list
        \li Prefer \c {#pragma once} over header guards.

        \li Do not use exceptions, unless you know what you do.

        \li Do not use RTTI (Run-Time Type Information; that is, the typeinfo
            struct, the dynamic_cast or the typeid operators, including throwing
            exceptions), unless you know what you do.

        \li Do not use virtual inheritance, unless you know what you do.

        \li Use templates wisely, not just because you can.

            Hint: Use the compile autotest to see whether a C++ feature is supported
            by all compilers in the test farm.

        \li All code is ASCII only (7-bit characters only, run \c {man ascii} if unsure)
            \list
                \li Rationale: We have too many locales inhouse and an unhealthy
                    mix of UTF-8 and Latin1 systems. Usually, characters > 127 can
                    be broken without you even knowing by clicking Save in your
                    favourite editor.
                \li For strings: Use \\nnn (where nnn is the octal representation
                    of whatever locale you want your string in) or \\xnn (where nn
                    is hexadecimal).
                    For example: QString s = QString::fromUtf8("\\213\\005");
              \li For umlauts in documentation, or other non-ASCII characters,
                  either use the qdoc \c {\unicode} command or use the relevant macro.
                  For example: \c{\uuml} for \uuml.
           \endlist

        \li Use static keywords instead of anonymous namespaces whenever possible.
            A name localized to the compilation unit with static is
            guaranteed to have internal linkage. For names declared in anonymous
            namespaces, the C++ standard unfortunately mandates external linkage
            (ISO/IEC 14882, 7.1.1/6, or see various discussions about this on the gcc
            mailing lists).
    \endlist

    \section3 Null Pointers

    Use nullptr for null pointer constants.

    \code
    void *p = nullptr;

    -NOT-

    void *p = NULL;

    -NOT-

    void *p = '\0';

    -NOT-

    void *p = 42 - 7 * 6;
    \endcode

    \note As an exception, imported third party code as well as code
    interfacing the native APIs (src/support/os_*) can use NULL or 0.

    \section2 C++11 and C++14 Features

    Code should compile with Microsoft Visual Studio 2013, g++ 4.7, and Clang 3.1.

    \section3 Lambdas

    When using lambdas, note the following:

    \list
        \li You do not have to explicitly specify the return type. If you are not using one
            of the previously mentioned compilers, do note that this is a C++14 feature and you
            might need to enable C++14 support in your compiler.
            \code
            []() {
                Foo *foo = activeFoo();
                return foo ? foo->displayName() : QString();
            });
            \endcode

        \li If you use static functions from the class that the lambda is located in, you have to
            explicitly capture \c this. Otherwise it does not compile with g++ 4.7 and earlier.
            \code
            void Foo::something()
            {
                ...
                [this]() { Foo::someStaticFunction(); }
                ...
            }

            -NOT-

            void Foo::something()
            {
                ...
                []() { Foo::someStaticFunction(); }
                ...
            }
            \endcode
    \endlist

    Format the lambda according to the following rules:

    \list
        \li Place the capture-list, parameter list, return type, and opening brace on the first line,
            the body indented on the following lines, and the closing brace on a new line.
            \code
            []() -> bool {
                something();
                return isSomethingElse();
            }

            -NOT-

            []() -> bool { something();
            somethingElse(); }
            \endcode

        \li Place a closing parenthesis and semicolon of an enclosing function call on the same line
            as the closing brace of the lambda.
            \code
            foo([]() {
                something();
            });
            \endcode

        \li If you are using a lambda in an 'if' statement, start the lambda on a new line, to
            avoid confusion between the opening brace for the lambda and the opening brace for the
            'if' statement.
            \code
            if (anyOf(fooList,
                    [](Foo foo) {
                        return foo.isGreat();
                    }) {
                return;
            }

            -NOT-

            if (anyOf(fooList, [](Foo foo) {
                        return foo.isGreat();
                    }) {
                return;
            }
            \endcode

        \li Optionally, place the lambda completely on one line if it fits.
            \code
            foo([] { return true; });

            if (foo([] { return true; })) {
                ...
            }
            \endcode

    \endlist

    \section3 \c auto Keyword

    Optionally, you can use the \c auto keyword in the following cases. If in doubt,
    for example if using \c auto could make the code less readable, do not use \c auto.
    Keep in mind that code is read much more often than written.

    \list
        \li When it avoids repetition of a type in the same statement.
            \code
            auto something = new MyCustomType;
            auto keyEvent = static_cast<QKeyEvent *>(event);
            auto myList = QStringList({ "FooThing",  "BarThing" });
            \endcode

        \li When assigning iterator types.
            \code
            auto it = myList.const_iterator();
            \endcode

    \endlist

    \section3 Scoped enums

    You can use scoped enums in places where the implicit conversion to int of unscoped enums is
    undesired or the additional scope is useful.

    \section3 Delegating constructors

    Use delegating constructors if multiple constructors use essentially the same code.

    \section3 Initializer list

    Use initializer lists to initialize containers, for example:

    \code
    const QVector<int> values = {1, 2, 3, 4, 5};
    \endcode

    \section3 Initialization with Curly Brackets

    If you use initialization with curly brackets, follow the same rules as with
    round brackets. For example:

    \code
    class Values // the following code is quite useful for test fixtures
    {
        float floatValue = 4; // prefer that for simple types
        QVector<int> values = {1, 2, 3, 4, integerValue}; // prefer that syntax for initializer lists
        SomeValues someValues{"One", 2, 3.4}; // not an initializer_list
        SomeValues &someValuesReference = someValues;
        ComplexType complexType{values, otherValues} // constructor call
    }

    object.setEntry({"SectionA", value, doubleValue}); // calls a constructor
    object.setEntry({}); // calls default constructor
    \endcode

    But be careful not to overuse it, to not obfuscate your code.

    \section3 Non-Static Data Member Initialization

    Use non-static data member initialization for trivial initializations, except in public
    exported classes.

    \section3 Defaulted and Deleted Functions

    Consider using \c{=default} and \c{=delete} to control the special functions.

    \section3 Override

    It is recommended to use the \c{override} keyword when overriding virtual functions. Do not
    use virtual on overridden functions.

    Make sure that a class uses \c{override} consistently, either for all overridden functions or
    for none.

    \section3 Nullptr

    All compilers support \c{nullptr}, but there is no consensus on using it. If in doubt, ask
    the maintainer of the module whether they prefer using \c{nullptr}.

    \section3 Range-Based for-Loop

    You may use range-based for-loops, but beware of the spurious detachment problem.
    If the for-loop only reads the container and it is not obvious whether the
    container is const or unshared, use \c{std::cref()} to ensure that the container
    is not unnecessarily detached.

    \section2 Using QObject

    \list
        \li Remember to add the Q_OBJECT macro to QObject subclasses that rely
            on the meta object system. Meta object system related features are
            the definition of signals and slots, the use of \c{qobject_cast<>},
            and others. See also \l{Casting}.
        \li Prefer Qt5-style \c{connect()} calls over Qt4-style.
        \li When using Qt4-style \c{connect()} calls, normalize the arguments
            for signals and slots inside connect statements to safely make
            signal and slot lookup a few cycles faster. You can use
            $QTDIR/util/normalize to normalize existing code. For more
            information, see QMetaObject::normalizedSignature.
    \endlist

    \section2 File Headers

    If you create a new file, the top of the file should include a
    header comment equal to the one found in other source files of Qt Creator.

    \section2 Including Headers

    \list
        \li Use the following format to include Qt headers:
            \c{#include <QWhatEver>}. Do not include the module as it might have changed between
            Qt4 and Qt5.
        \li Arrange includes in an order that goes from specific to generic to
            ensure that the headers are self-contained. For example:
        \list
            \li \c{#include "myclass.h"}
            \li \c{#include "otherclassinplugin.h"}
            \li \c{#include <otherplugin/someclass.h>}
            \li \c{#include <QtClass>}
            \li \c{#include <stdthing>}
            \li \c{#include <system.h>}
        \endlist
        \li Enclose headers from other plugins in angle brackets (<>) rather than
            quotation marks ("") to make it easier to spot external dependencies in
            the sources.
        \li Add empty lines between long blocks of \e peer headers and try to
            arrange the headers in alphabetic order within a block.
    \endlist

    \section2 Casting

    \list
        \li Avoid C casts, prefer C++ casts (\c static_cast, \c const_cast,
            \c reinterpret_cast) Both \c reinterpret_cast and
            C-style casts are dangerous, but at least \c reinterpret_cast
            will not remove the const modifier.
        \li Do not use \c dynamic_cast, use \c {qobject_cast} for QObjects, or
            refactor your design, for example by introducing a \c {type()}
            function (see QListWidgetItem), unless you know what you do.
    \endlist

    \section2 Compiler and Platform-specific Issues

    \list
        \li Be extremely careful when using the question mark operator.
            If the returned types are not identical, some compilers generate
            code that crashes at runtime (you will not even get a compiler warning):
            \code
            QString s;
            // crash at runtime - QString vs. const char *
            return condition ? s : "nothing";
            \endcode

        \li Be extremely careful about alignment.

            Whenever a pointer is cast such that the required alignment of
            the target is increased, the resulting code might crash at runtime
            on some architectures. For example, if a \c {const char *} is cast to a
            \c {const int *}, it will crash on machines where integers have to be
            aligned at two-byte or four-byte boundaries.

            Use a union to force the compiler to align variables correctly.
            In the example below, you can be sure that all instances of
            AlignHelper are aligned at integer-boundaries:
            \code
            union AlignHelper
            {
                char c;
                int i;
            };
            \endcode

        \li Stick to integral types, arrays of integral types, and structs thereof
            for static declarations in headers.
            For example, \c {static float i[SIZE_CONSTANT];} will not be optimized
            and copied in every plugin in most cases, it would be good to avoid it.

        \li Anything that has a constructor or needs to run code to be
            initialized cannot be used as global object in library code,
            since it is undefined when that constructor or code will be run
            (on first usage, on library load, before \c {main()} or not at all).

            Even if the execution time of the initializer is defined for
            shared libraries, you will get into trouble when moving that code
            in a plugin or if the library is compiled statically:

            \code
            // global scope

            -NOT-

            // Default constructor needs to be run to initialize x:
            static const QString x;

            -NOT-

            // Constructor that takes a const char * has to be run:
            static const QString y = "Hello";

            -NOT-

            QString z;

            -NOT-

            // Call time of foo() undefined, might not be called at all:
            static const int i = foo();
            \endcode

            Things you can do:

            \code
            // global scope
            // No constructor must be run, x set at compile time:
            static const char x[] = "someText";

            // y will be set at compile time:
            static int y = 7;

            // Will be initialized statically, no code being run.
            static MyStruct s = {1, 2, 3};

            // Pointers to objects are OK, no code needed to be run to
            // initialize ptr:
            static QString *ptr = 0;

            // Use Q_GLOBAL_STATIC to create static global objects instead:

            Q_GLOBAL_STATIC(QString, s)

            void foo()
            {
                s()->append("moo");
            }
            \endcode

            \note Static objects in function scope are no problem. The constructor
            will be run the first time the function is entered. The code is not
            reentrant, though.

        \li A \c char is signed or unsigned dependent on the architecture. Use signed
            \c char or \c uchar if you explicitly want a signed or unsigned char.
            The following code will break on PowerPC, for example:

            \code
            // Condition is always true on platforms where the
            // default is unsigned:
            if (c >= 0) {
                ...
            }
            \endcode

        \li Avoid 64-bit enum values. The AAPCS (Procedure Call Standard
            for the ARM Architecture) embedded ABI hard codes
            all enum values to a 32-bit integer.

        \li Do not mix const and non-const iterators. This will silently crash
            on broken compilers.
            \code
            for (Container::const_iterator it = c.constBegin(); it != c.constEnd(); ++it)

            -NOT-

            for (Container::const_iterator it = c.begin(); it != c.end(); ++it)
            \endcode

        \li Do not inline virtual destructors in exported classes. This leads to duplicated vtables
            in dependent plugins and this can also break RTTI. See
            \l {https://bugreports.qt.io/browse/QTBUG-45582}{QTBUG-45582}.
    \endlist

    \section2 Esthetics

    \list
        \li Prefer unscoped enums to define const over static const int or defines.
            Enumeration values will be replaced by the compiler at compile time,
            resulting in faster code. Defines are not namespace safe.
        \li Prefer verbose argument names in headers.
            Qt Creator will show the argument names in their completion box.
            It will look better in the documentation.
    \endlist

    \section2 Inheriting from Template or Tool Classes

    Inheriting from template or tool classes has the following potential
    pitfalls:

    \list
        \li The destructors are not virtual, which can lead to memory leaks.
        \li The symbols are not exported (and mostly inline), which can lead to
            symbol clashes.
    \endlist

    For example, library A has class \c {Q_EXPORT X: public QList<QVariant> {};}
    and library B has class \c {Q_EXPORT Y: public QList<QVariant> {};}.
    Suddenly, QList symbols are exported from two libraries which results in a
    clash.


    \section2 Inheritance Versus Aggregation

    \list
        \li Use inheritance if there is a clear \e{is-a} relation.
        \li Use aggregation for re-use of orthogonal building blocks.
        \li Prefer aggregation over inheritance if there is a choice.
    \endlist


    \section2 Conventions for Public Header Files

    Our public header files have to survive the strict settings of
    some of our users. All installed headers have to follow these rules:

    \list

        \li No C style casts (\c{-Wold-style-cast}). Use \c static_cast, \c const_cast
            or \c reinterpret_cast, for basic types, use the constructor form:
            \c {int(a)} instead of \c {(int)a}. For more information, see \l{Casting}.

        \li No float comparisons (\c{-Wfloat-equal}). Use \c qFuzzyCompare to compare
            values with a delta. Use \c qIsNull to check whether a float is
            binary 0, instead of comparing it to 0.0, or, preferred, move
            such code into an implementation file.

       \li Do not hide virtual functions in subclasses (\{-Woverloaded-virtual}).
           If the baseclass A has a virtual \c {int val()} and subclass B an
           overload with the same name, \c {int val(int x)}, the A \c val function
           is hidden. Use the \c using keyword to make it visible again, and
           add the following silly workaround for broken compilers:
           \code
               class B: public A
               {
               #ifdef Q_NO_USING_KEYWORD
               inline int val() { return A::val(); }
               #else
               using A::val;
               #endif
               };
           \endcode

        \li Do not shadow variables (\c{-Wshadow}).

        \li Avoid things like \c {this->x = x;} if possible.

        \li Do not give variables the same name as functions declared in
            your class.

        \li To improve code readability, always check whether a preprocessor
            variable is defined before probing its value (\c {-Wundef}).

            \code
            #if defined(Foo) && Foo == 0

              -NOT-

            #if Foo == 0

            -NOT-

            #if Foo - 0 == 0
            \endcode

        \li When checking for a preprocessor define using the \c {defined}
            operator, always include the variable name in parentheses.

            \code
            #if defined(Foo)

              -NOT-

              #if defined Foo
           \endcode

    \endlist

    \section1 Class Member Names

    We use the "m_" prefix convention, except for public struct members
    (typically in *Private classes and the very rare cases of really
    public structures). The \c{d} and \c{q} pointers are exempt from
    the "m_" rule.

    The \c{d} pointers ("Pimpls") are named "d", not "m_d". The type of the
    \c{d} pointer in \c{class Foo} is \c{FooPrivate *}, where \c{FooPrivate}
    is declared in the same namespace as \c{Foo}, or if \c{Foo} is
    exported, in the corresponding \{Internal} namespace.

    If needed (for example when the private object needs to emit signals of
    the proper class), \c{FooPrivate} can be a friend of \c{Foo}.

    If the private class needs a backreference to the real class,
    the pointer is named \c{q}, and its type is \c{Foo *}. (Same convention
    as in Qt: "q" looks like an inverted "d".)

    Do not use smart pointers to guard the \c{d} pointer as it imposes
    a compile and link time overhead and creates fatter object
    code with more symbols, leading, for instance to slowed down
    debugger startup:

        \code

            ############### bar.h

            #include <QScopedPointer>
            //#include <memory>

            struct BarPrivate;

            struct Bar
            {
                Bar();
                ~Bar();
                int value() const;

                QScopedPointer<BarPrivate> d;
                //std::unique_ptr<BarPrivate> d;
            };

            ############### bar.cpp

            #include "bar.h"

            struct BarPrivate { BarPrivate() : i(23) {} int i; };

            Bar::Bar() : d(new BarPrivate) {}

            Bar::~Bar() {}

            int Bar::value() const { return d->i; }

            ############### baruser.cpp

            #include "bar.h"

            int barUser() { Bar b; return b.value(); }

            ############### baz.h

            struct BazPrivate;

            struct Baz
            {
                Baz();
                ~Baz();
                int value() const;

                BazPrivate *d;
            };

            ############### baz.cpp

            #include "baz.h"

            struct BazPrivate { BazPrivate() : i(23) {} int i; };

            Baz::Baz() : d(new BazPrivate) {}

            Baz::~Baz() { delete d; }

            int Baz::value() const { return d->i; }

            ############### bazuser.cpp

            #include "baz.h"

            int bazUser() { Baz b; return b.value(); }

            ############### main.cpp

            int barUser();
            int bazUser();

            int main() { return barUser() + bazUser(); }

        \endcode

        Results:

        \code

            Object file size:

             14428 bar.o
              4744 baz.o

              8508 baruser.o
              2952 bazuser.o

            Symbols in bar.o:

                00000000 W _ZN3Foo10BarPrivateC1Ev
                00000036 T _ZN3Foo3BarC1Ev
                00000000 T _ZN3Foo3BarC2Ev
                00000080 T _ZN3Foo3BarD1Ev
                0000006c T _ZN3Foo3BarD2Ev
                00000000 W _ZN14QScopedPointerIN3Foo10BarPrivateENS_21QScopedPointerDeleterIS2_EEEC1EPS2_
                00000000 W _ZN14QScopedPointerIN3Foo10BarPrivateENS_21QScopedPointerDeleterIS2_EEED1Ev
                00000000 W _ZN21QScopedPointerDeleterIN3Foo10BarPrivateEE7cleanupEPS2_
                00000000 W _ZN7qt_noopEv
                         U _ZN9qt_assertEPKcS1_i
                00000094 T _ZNK3Foo3Bar5valueEv
                00000000 W _ZNK14QScopedPointerIN3Foo10BarPrivateENS_21QScopedPointerDeleterIS2_EEEptEv
                         U _ZdlPv
                         U _Znwj
                         U __gxx_personality_v0

            Symbols in baz.o:

                00000000 W _ZN3Foo10BazPrivateC1Ev
                0000002c T _ZN3Foo3BazC1Ev
                00000000 T _ZN3Foo3BazC2Ev
                0000006e T _ZN3Foo3BazD1Ev
                00000058 T _ZN3Foo3BazD2Ev
                00000084 T _ZNK3Foo3Baz5valueEv
                         U _ZdlPv
                         U _Znwj
                         U __gxx_personality_v0

          \endcode



    \section1 Documentation

    The documentation is generated from source and header files. You document
    for the other developers, not for yourself. In the header files, document
    interfaces. That is, what the function does, not the implementation.

    In the .cpp files, you can document the implementation if the
    implementation is not obvious.

*/
