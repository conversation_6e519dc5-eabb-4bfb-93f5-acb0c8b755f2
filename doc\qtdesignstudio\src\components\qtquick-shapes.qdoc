// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-shapes.html
    \previouspage quick-preset-components.html
    \nextpage quick-text.html

    \title Shapes

    \if defined(qtdesignstudio)
    \QDS is a UI design tool rather than a generic drawing tool, and therefore,
    the focus is on providing ready-made UI controls that you can modify
    according to your needs. The values of some properties of the controls are
    specified by using styling, and therefore you cannot change them.

    However, you can use some of the components in \l Components to draw
    basic shapes, such as rectangles. In addition, \QDS comes with a set of
    more powerful and flexible graphical primitives, that allow creating more
    complex shapes, such as borders, triangles, arcs, and pies in
    the \uicontrol {2D} view.

    \image studio-shapes.png "Shapes in the 2D view"

    \else
    You can use the Rectangle component to draw basic shapes in
    the \l {2D} view.
    \image qml-shapes.png "Shapes in the 2D view"
    \endif

    Most visual components in \uicontrol Components are based on the \l [QtQuick]
    {Item} component. Even though it has no visual appearance itself (similarly
    to a mouse area, for example), it defines all the properties that are
    common across visual components, such as position, size, and visibility. For
    more information, see \l{Basic Component Properties}.

    In addition, each component has a set of properties that specify its visual
    appearance and behavior. You can modify the property values to set fill
    and border colors, stroke width, and other characteristics of the shapes.

    The following sections describe the available shapes and their properties
    in more detail. You can modify the values of the properties in the
    \uicontrol Properties view.

    \target basic-rectangle
    \section1 Rectangle

    The basic \l [QtQuick] {Rectangle} component is used for drawing shapes
    with four sides and corners, as well as a solid border.

    \if defined(qtdesignstudio)
    \image qml-shapes-rectangle.png "A rectangle and its properties"
    \endif

    Rectangles can be filled either with a solid fill color or a linear
    gradient that you set in the \uicontrol {Fill color} field. You can
    also use a \l{Picking Colors}{color picker} to select colors and a
    \l{Picking Gradients}{gradient picker} to select gradients from a
    predefined set of \l{https://webgradients.com/}{WebGradients}.

    An optional solid border can be added to a rectangle with its own color
    and thickness by setting the values of the \uicontrol {Border color} and
    \uicontrol {Border width} fields. To create a border without a fill color,
    select the \inlineimage icons/transparent-icon.png
    button that sets the color to \e transparent.

    By setting the value of the \uicontrol Radius field, you can create shapes
    with rounded corners. With the right combination of the values of the
    rectangle width, height, and radius properties, you can create oval and
    circular shapes. For example, to draw a full circle, first draw a rectangle
    with all four sides equal, and then set its radius to half the side length.

    When drawing curved edges, consider enabling the \uicontrol Antialiasing
    check box in the \uicontrol Advanced section to improve the appearance of
    your shape.

    \if defined(qtdesignstudio)
    \target studio-rectangle
    \section2 Studio Rectangle

    If you want to modify each corner of the rectangle independently
    or use a dashed border, create an instance of the \uicontrol Rectangle
    component available in \uicontrol Components >
    \uicontrol {Qt Quick Studio Components} instead of the basic
    \uicontrol Rectangle component available in \uicontrol {Default Components}
    > \uicontrol Basic.

    By setting the values of properties in the \uicontrol {Corner Radiuses}
    section, you can draw each corner independently. By using radius values
    in combination with the values in the \uicontrol {Corner Bevel} section,
    you can create shapes with cut corners.

    \image studio-shapes-rectangle.png "A studio rectangle with cut corners"

    In addition to linear gradients, you can specify conical and radial
    gradients in the \uicontrol {Fill color} field.

    In the \uicontrol {Stroke Details} section, you can specify the border
    mode, line style, and dash pattern for dashed and dotted lines.
    For more information, see \l{Strokes}.

    \section1 Border

    The Border component is used to create borders out of four segments:
    left, top, right, and bottom. By selecting the check boxes in the
    \uicontrol {Draw Edges} section, you can determine whether each
    of the segments is visible. This enables you to draw solid or dashed
    lines with specified dash patterns and joint and cap styles.

    \image studio-shapes-border.png "Border properties"

    Set the color of the border in the \uicontrol {Stroke color} field and its
    thickness in the \uicontrol {Stroke width} field. Then specify additional
    properties for borders in the \uicontrol {Stroke Details} section.

    \section2 Strokes

    In the \uicontrol {Stroke Details} section, you can specify the border mode,
    line style, and dash pattern for dashed and dotted lines.

    \image studio-shapes-stroke-properties.png "Stroke properties"

    In the \uicontrol {Border mode} field, you can specify whether the border
    is drawn along the inside or outside edge of the component, or on top of the
    edge.

    If you select a dashed or dotted pattern in the \uicontrol {Stroke style}
    field, you can specify the dash pattern as the dashes and the gaps between
    them in the \uicontrol {Dash pattern} field. The dash pattern is specified
    in units of the stroke width. That is, a dash with the length 5 and width
    10 is 50 pixels long.

    The value of the \uicontrol {Dash offset} field specifies the starting point
    of the dash pattern for a line. It is measured in terms of the units used to
    specify the dash pattern. For example, a pattern where each stroke is four
    units long, followed by a gap of two units, will begin with the stroke when
    drawn as a line. However, if the dash offset is set to 4.0, any line drawn
    will begin with the gap. Values of the offset up to 4.0 will cause part of
    the stroke to be drawn first, and values of the offset between 4.0 and 6.0
    will cause the line to begin with part of the gap.

    In the \uicontrol {Join style} field, select \uicontrol {Miter Join} to
    extend the outer edges of the lines to meet at an angle and to fill the
    area between them. Select \uicontrol {Bevel Join} to fill the triangular
    notch between the two lines. Select \uicontrol {Round Join} to fill a
    circular arc between the two lines.

    The value of the \uicontrol {Cap style} property specifies whether line
    ends are square or rounded.

    \section1 Triangle

    The Triangle component can be used to draw triangles with different dimensions
    and shapes. The component is enclosed in an invisible rectangle that determines
    its size. The dimensions of the triangle can be changed to make it elongated
    or squatter with space around it by setting the top, bottom, left, and right
    margins in the \uicontrol Margin section. The margins are set between the
    triangle and the edges of the parent rectangle.

    \image studio-shapes-triangle.png "Triangle properties"

    The fill and stroke color property values are set in the same way as for a
    rectangle. The border property values are described in \l{Strokes}.

    \section1 Arc

    An arc is specified by setting values in degrees in the
    \uicontrol {Arc start} and \uicontrol {Arc end} fields.
    The arc can be just a line or a filled outline. The
    properties of the line or outline are described in \l{Strokes}.

    \image studio-shapes-arc.png "Arc properties"

    The area between the arc's start and end points or the area inside the
    outline are painted using either a solid fill color or a gradient.

    \image studio-shapes-arc-outline-properties.png "Arc Outline Properties"

    To create an arc with an outline, select the \uicontrol {Full outline}
    check box. The \uicontrol {Outline width} field sets the width of
    the arc outline, including the stroke.

    The \uicontrol {Round outline}, \uicontrol {Round start}, and
    \uicontrol {Round end} properties specify whether the end points of the
    arc outline have rounded caps. For an arc that does not have an outline,
    the \uicontrol {Cap style} field in the \uicontrol {Stroke Details}
    section specifies whether the line ends are square or rounded.

    \section1 Pie

    The Pie component is used to create a pie slice, a pie that is missing slices,
    or just the pie rind (similar to an arc), depending on the values of the
    \uicontrol {Pie start} and \uicontrol {Pie end} fields and the
    \uicontrol Hide check box.

    \image studio-shapes-pie.png "Pie properties"

    The filling of the pie is painted using either a solid fill color or a
    gradient. The fill and stroke color property values are set in the same
    way as for a rectangle. The border property values are described in
    \l{Strokes}.

    \section1 Flipable

    A Flipable component can be visibly \e flipped between its front and back
    sides, like a card. The front and back sides are specified by using any
    two components inside the Flipable component. The component with the
    higher z-order is the front side. The \uicontrol Front and \uicontrol Back
    fields in the \uicontrol Opacity section are used to hide and show either
    the front or back side of the item at a time.

    \image studio-flipable.png

    The \uicontrol {Flip angle} property is used to animate the angle
    of the component to produce the flipping effect. The value of the
    \uicontrol {Rotational axis} property determine whether the component
    is rotated around the x-axis or the y-axis.

    \section1 Summary of Shapes

    The following table lists the components that you can use to draw shapes.
    The \e Location column indicates the location of the component in
    \uicontrol Components. The \e MCU column indicates which
    components are supported on MCUs.

    \table
    \header
        \li Icon
        \li Name
        \li Location
        \li MCU
        \li Purpose
    \row
        \li \inlineimage icons/item-arc-16px.png
        \li \l Arc
        \li Qt Quick Studio Components
        \li
        \li An arc that begins and ends at given positions.
    \row
        \li \inlineimage icons/custom-border-16px.png
        \li \l Border
        \li Qt Quick Studio Components
        \li
        \li A line with four segments that you can show and shape individually.
    \row
        \li \inlineimage icons/item-pie-16px.png
        \li \l Pie
        \li Qt Quick Studio Components
        \li
        \li A pie slice or a pie with a slice missing from it.
    \row
        \li \inlineimage icons/item-flippable-16px.png
        \li \l Flipable
        \li Qt Quick Studio Components
        \li
        \li A component that can be visibly \e flipped between its front and
         back sides, like a card.
    \row
        \li \inlineimage rect-icon16.png
        \li \l Rectangle
        \li Default Components - Basic
        \li \inlineimage ok.png
        \li A rectangle that is painted with a solid fill color or linear
            gradient and an optional border. You can use the radius property
            to draw circles.
    \row
        \li \inlineimage icons/custom-rectangle-16px.png
        \li \l{Studio Rectangle}{Rectangle}
        \li Qt Quick Studio Components
        \li
        \li An extended rectangle that is painted with a solid fill color or
            linear, conical, or radial gradients, and corners that you can
            shape independently of each other.
    \row
        \li \inlineimage icons/item-triangle-16px.png
        \li \l Triangle
        \li Qt Quick Studio Components
        \li
        \li A triangle with different dimensions and shapes that is
            enclosed in an invisible rectangle.
    \endtable
    \endif
*/
