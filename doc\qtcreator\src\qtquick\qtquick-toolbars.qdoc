// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-indenting-code.html
    \page qt-quick-toolbars.html
    \if defined(qtdesignstudio)
    \nextpage creator-diff-editor.html
    \else
    \nextpage creator-editor-codepasting.html
    \endif

    \title Using Qt Quick Toolbars

    When you select a QML type in the code and a toolbar is available,
    a light bulb icon appears: \inlineimage icons/refactormarker.png
    . Select the icon to open the toolbar.

    To open toolbars immediately when you select a QML type, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Qt Quick} >
    \uicontrol {QML/JS Editing} > \uicontrol {Always show Qt Quick Toolbar}.

    \image qtcreator-qml-js-editing.png "QML/JS Editing preferences"

    Drag the toolbar to pin it to another location. Select
    \inlineimage icons/pin.png
    to unpin the toolbar and move it to its default location. To pin toolbars
    by default, select \uicontrol {Pin Qt Quick Toolbar}.

    \section1 Previewing Images

    The Qt Quick Toolbar for images allows you to edit the properties of
    \l {Border Image} and
    \if defined(qtdesignstudio)
    \l {Images}{Image} items.
    \else
    \l Image items.
    \endif

    You can scale and tile the images, replace them with other images,
    preview them, and change the image margins.

    \image qml-toolbar-image.png "Qt Quick Toolbar for images"

    To preview an image, double-click it on the toolbar. In the preview
    dialog, you can zoom the image. Drag the image margins to change them.

    \image qml-toolbar-image-preview.png "Image preview dialog"

    \section1 Formatting Text

    The Qt Quick Toolbar for text allows you to edit the properties of
    \l{Text} items.
    You can change the font family and size as well as text formatting, style,
    alignment, and color.

    If a property is assigned an expression instead of a value, you
    cannot use the toolbar to edit it. The button for editing the property
    is disabled.

    \image qml-toolbar-text.png "Qt Quick Toolbar for text"

    By default, font size is specified as pixels. To use points, instead,
    change \uicontrol px to \uicontrol pt in the size field.

    \section1 Previewing Animation

    The Qt Quick Toolbar for animation allows you to edit the properties of
    PropertyAnimation items and the items that inherit it. You can
    change the easing curve type and duration. For some curves, you can also
    specify amplitude, period, and overshoot values.

    \image qml-toolbar-animation.png "Qt Quick Toolbar for animation"

    Select the play button to preview your changes.

    \section1 Editing Rectangles

    The Qt Quick Toolbar for rectangles allows you to edit the properties of
    \if defined(qtdesignstudio)
    \l {basic-rectangle}{Rectangle}
    \else
    Rectangle
    \endif
    items. You can change the fill and border colors and add gradients.

    \image qml-toolbar-rectangle.png "Qt Quick Toolbar for rectangles"

    To add gradient stop points, click above the gradient bar. To remove
    stop points, drag them upwards.

*/
