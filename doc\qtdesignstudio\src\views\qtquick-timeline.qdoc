// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage quick-animation-overview.html
    \page studio-timeline.html
    \nextpage qtquick-editing-easing-curves.html

    \title Creating Timeline Animations

    You can create timelines and keyframe-based animations for linear
    interpolation through intermediate values at specified keyframes
    instead of immediately changing to the target value.

    You can also bind the timeline to a property value of a component such as
    a slider and control the animation this way.

    \section1 Creating an Animation

    To create an animation, whether it's a keyframe animation or an animation
    bound to a property value, you first need to create a timeline.

    \section2 Creating a Timeline

    To create a timeline to animate a UI component:

    \list 1
        \li In the \l Timeline view, select the \inlineimage icons/plus.png
            (\uicontrol {Add Timeline}) button to specify settings
            for the timeline and running the animation
            in the \uicontrol {Timeline Settings} dialog.
        \li On the \uicontrol {Timeline Settings} tab:
            \list
                \li In the \uicontrol {Timeline ID} field, enter an id that
                describes the timeline.
                \li In the \uicontrol {Start frame} field, set the first frame
                of the timeline. Negative values are allowed.
                \li In the \uicontrol {End frame} field, set the last frame
                of the timeline.
                \image timeline-settings-dialog.png
            \endlist
        \li On the \uicontrol {Animation Settings} tab:
            \list
                \li In the \uicontrol {Animation ID} field, enter an ID for the
                animation.
                \li Optional. Select the \uicontrol {Running in Base State}
                check box to run the animation when the base state is applied.
                Clear the check box to run the animation when some other state
                is applied. For more information, see
                \l{Binding Animations to States}.
                \li In the \uicontrol {Start frame} field, set the first frame
                of the animation.
                \li In the \uicontrol {End frame} field, set the last frame of
                the animation.
                \li In the \uicontrol {Duration} field, set the length of the
                animation in milliseconds.
                \li Optional. Select the \uicontrol Continuous check box to
                loop the animation indefinitely.
                \li Optional. In the \uicontrol Loops field, set the number of
                times to run the animation. The default number of
                loops is one, which means that you must restart the animation
                to see it again.
                \li Optional. Select the \uicontrol {Ping pong} check box to
                play the animation backwards back to the beginning when it
                reaches the end.
                \li Optional. In the \uicontrol Finished field, select the state
                to transition to when the animation finishes.
            \endlist
        \li Select \uicontrol Close to close the dialog and save the settings.
    \endlist

    Now, with the settings set for the timeline and the animation, you
    set the keyframes for the properties to animate.

    \section3 Creating Additional Timelines

    You can create more than one timeline. The purpose of several timelines is
    to use different timelines in different states.

    To create a timeline for a second state:

     \list 1
       \li In \uicontrol {Timeline}, open the \uicontrol {Timeline Settings}
       dialog.
       \li Next to the \uicontrol {Timeline Settings} tab, select
       \inlineimage icons/plus.png
       . This creates another timeline.
       \li In the table below the \uicontrol {Animation Settings} tab, set the
       Timeline for the state where you want to use it.
       \image timeline-settings-dialog-second.png
    \endlist
    To set the keyframe values for the timeline you created, first select the
    state in \uicontrol {States} and the timeline is available in
    \uicontrol{Timelines}.

    \image timeline-states.png

    \section2 Setting Keyframe Values

    When you create a timeline, \QDS creates one animation for the timeline.
    You can create as many animations for a timeline as you want. For example,
    you can create animations to run just a small section of the timeline or to
    run the timeline backwards.

    To animate components in the \l Timeline view, you set keyframe values for
    the property to animate. \QDS automatically adds keyframes between two
    keyframes and sets their values evenly to create, for example, movement or
    transformation.

    To set keyframe values for a component property:

    \list 1
        \li In the \l Navigator view, select the component to animate.
        \li In the \l Properties view, select \inlineimage icons/action-icon.png
            (\uicontrol Actions) > \uicontrol {Insert Keyframe} for the property
            that you want to animate.
            \image timeline-insert-keyframe.png
        \li In the \l Timeline view, select the
            \uicontrol {Per Property Recording} button
            to start recording property changes.
            \image timeline-per-property-recording.png
        \li Ensure that the playhead is in frame 0 and enter the value of the
            property in the field next to the property name on the timeline.
            Press \key Enter to save the value.
        \li Move the playhead to another frame on the timeline and specify
            the value at that frame. For more information, see
            \l{Navigating in Timeline}.
        \li When you have specified as many values as you need, select
            \uicontrol {Per Property Recording} again to stop recording.
    \endlist

    \section2 Binding a Timeline to a Property

    When you bind a timeline to a component property, the animation's
    current frame is controlled by the value of the property.

    In this example, you bind the timeline to a slider component.

    With a timeline created and keyframe values set:

    \list 1
        \li From \uicontrol {Components}, drag a slider to
        the \uicontrol {2D} or \uicontrol {Navigator} view.
        \li In \uicontrol {Navigator}, select \e slider and in
        \uicontrol {Properties}, set:
            \list
              \li \uicontrol To to 1000.
              \note The \uicontrol From and \uicontrol To values of the slider
              should match the \uicontrol {Start Frame} and
              \uicontrol {End Frame} values of the timeline if you want to
              control the complete animation with the slider.
            \endlist
        \li In the \uicontrol {Timeline Settings} dialog, select
        \inlineimage icons/minus.png
        next to the \uicontrol {Animation Settings} tab to delete the
        animation. If you have several animations, delete all.
        \li In \uicontrol {Expression binding}, enter \c {slider.value}.
        \image timeline-settings-property-binding.png
    \endlist

    \section2 Binding Animations to States

    You can bind animations to states, this means that the animation will run
    when you enter the state.

    To bind an animation to a state:
    \list 1
      \li In the table at the bottom of the \uicontrol {Timeline Settings}
      dialog lists:
        \list
          \li Double-click the value in the \uicontrol Timeline field and select
          the timeline with the animation you want to bind to the state.
          \li Double-click the value in the \uicontrol Animation field and
          select the animation you want to bind to the state.
          \image timeline-bind-animation-state.png
        \endlist
    \endlist
    To bind a state to a certain keyframe in an animation without running the
    animation, set the keyframe in the \uicontrol{Fixed Frame} field.

    \section1 Managing Keyframes

    \image studio-timeline-with-tracks.png "Timeline view"

    \section2 Editing Keyframes

    To remove all the changes you recorded for a property, right-click the
    property name on the timeline and select \uicontrol {Remove Property}.

    To add keyframes to the keyframe track of a component at the current
    position of the playhead, right-click the component name on the timeline and
    select \uicontrol {Add Keyframes at Current Frame}.

    Keyframes are marked on the timeline by using \l{keyframe_marker}{markers}
    of different colors and shapes, depending on whether they are active or
    inactive or whether you have applied \l{Editing Easing Curves}
    {easing curves} to them.

    \section2 Editing Keyframe Values

    To fine-tune the value of a keyframe, double-click a keyframe marker or
    right-click it and select \uicontrol {Edit Keyframe} in the context menu.

    The \uicontrol {Edit Keyframe} dialog displays the name of the property
    you are animating and its current value at the frame specified in the
    \uicontrol Frame field. You can change both the keyframe and its value.

    \image studio-edit-keyframe.png "Edit Keyframe dialog"

    \section2 Copying Keyframes

    You can copy the keyframes from the keyframe track for a component and
    paste them to the keyframe track of another component.

    To copy all keyframes from one track to another one:
    \list 1
      \li Right-click the component ID and select
      \uicontrol {Copy All Keyframes} in the context menu.
      \li Right-click the other component ID, and select
      \uicontrol {Paste Keyframes} in the context menu.
    \endlist

    \section2 Deleting Keyframes

    To delete a keyframe, right-click it and select \uicontrol {Delete Keyframe}
    in the context menu.

    To delete all keyframes from the selected component, right-click the
    component name in \uicontrol {Timeline} and select
    \uicontrol {Delete All Keyframes} in the context menu.

    \section1 Viewing the Animation

    To preview your animation, do one of the following in the
    \uicontrol{Timeline} view:
    \list
      \li Drag the playhead along the timeline.
      \li Select \inlineimage icons/start_playback.png
      button or press \key Space.
    \endlist

    To preview the whole UI, select the
    \inlineimage icons/live_preview.png
    (\uicontrol {Show Live Preview}) button on the canvas toolbar
    or press \key {Alt+P}.

    \section1 Animating Rotation

    To animate components that rotate around a central point, you can use the
    \l {basic-item}{Item} component as a parent for the rotating component. Then
    create a timeline for the Item, and set the rotation property for the start
    and end keyframes.

    \if defined(qtdesignstudio)
    \section1 Animating Shapes

    You can use the \uicontrol {Qt Quick Studio Components} to animate the
    following shapes:

    \list
        \li \l Arc
        \li \l Border
        \li \l Pie
        \li \l Rectangle
        \li \l Triangle
    \endlist
    \endif
*/
