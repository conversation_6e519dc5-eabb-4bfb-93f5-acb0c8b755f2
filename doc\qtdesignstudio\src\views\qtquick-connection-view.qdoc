// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-connection-view.html
    \previouspage qtquick-properties-view.html
    \nextpage qtquick-states-view.html

    \title Connections

    The \uicontrol {Connections} view is a collection of views that enable you
    to create connections between components and the application, to bind
    component properties together, and to add custom properties for components.

    \image qmldesigner-connections.png "The Connections view"

    The \l{glossary-component}{components} of the application UI and the
    application logic need to communicate with each other. For example, a
    button needs to know that the user has clicked it. The button may then
    change color to indicate its state or perform an action. Further, the
    application needs to know whether the user clicked the button because
    it may need to relay this clicking event to other applications. For
    this purpose, a button typically contains a \l{Mouse Area} component,
    which can receive different pointer events and emit \e signals in
    response to them.

    Another way to connect components is to bind their \l{Properties}
    {properties} together. A component's property can be assigned a static
    value which stays constant until it is explicitly changed. To make the
    UI more dynamic, you can use \e {property bindings}. This means that
    you can specify relationships between component properties so that when
    the value of a property changes, the values of any properties that are
    bound to it are updated accordingly.

    Each preset component has a set of \l{Specifying Component Properties}
    {preset properties} that you can specify values for. You can add
    custom properties that would not otherwise exist for a particular
    \l{Component Types}{component type}.

    \if defined(qtcreator)
    In addition, application developers can use the \uicontrol Backends view
    to access QObject objects implemented in C++ from QML files.
    \endif

    \section1 Summary of the Connections View Tabs

    \table
    \header
        \li Tab
        \li Purpose
        \li Read More
    \row
        \li \uicontrol Connections
        \li Create connections between components and the application logic
            by accessing signals outside of the components that emit them.
        \li \l{Connecting Components to Signals}
    \row
        \li \uicontrol Bindings
        \li Dynamically change the behavior of a component by creating a
            binding between the properties of two components.
        \li \l{Adding Bindings Between Properties}
        \row
        \li \uicontrol Properties
        \li Add custom properties that would not otherwise exist for a
            particular preset component or your own custom component.
        \li \l{Specifying Custom Properties}
    \if defined(qtcreator)
    \row
        \li \uicontrol Backends
        \li Access QObject objects implemented in C++ from QML files.
        \li \l{Managing C++ Backend Objects}
    \endif
    \endtable
*/
