// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-build-settings-incredibuild.html
    \page creator-build-settings-conan.html
    \nextpage creator-run-settings.html

    \title Conan Build Configuration

    You can specify build steps for <PERSON>.

    For more information about configuring <PERSON>, see \l{Setting Up Conan}.

    \section1 Conan Build Steps

    To configure a project to be built using the Conan package manager, select
    \uicontrol {Add Build Step} > \uicontrol {Run Conan Install}.

    \image qtcreator-build-steps-conan-install.png "Conan Install build step"

    In the \uicontrol {Conan file} field, enter the location of the
    \e conanfile.txt file for the project.

    The \uicontrol {Conan install} field displays the effective
    build command. You can add arguments for the command in the
    \uicontrol {Additional arguments} field.

    Select \uicontrol {Build missing} to build packages from source if binary
    packages are not found.
*/
