// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-managing.html
    \page creator-project-creating.html
    \nextpage creator-file-creating.html

    \title Creating Projects

    Creating a project enables you to:

    \list

        \li Group files together

        \li Add custom build steps

        \li Include forms and resource files

        \li Specify settings for running applications

    \endlist

    When you set up a new project in \QC, a wizard guides you step-by-step
    through the process. The wizard templates prompt you to enter the settings
    that you need for that particular type of project and create
    the necessary files for you. You can add your own custom wizards to
    standardize the way of adding subprojects and classes to a project.

    Most \QC project wizards enable you to choose the build system to use for
    building the project: qmake, CMake, or Qbs. If you do not get to choose,
    the project uses qmake as the build system.

    You can use wizards also to create plain C or C++ projects that use
    qmake, Qbs, or CMake, but do not use the Qt library.

    In addition, you can import projects as \e {generic projects} that do not
    use qmake, Qbs, or CMake. This enables you to use \QC as a code editor and
    to fully control the steps and commands used to build the project.

    You can install tools for \l{glossary-device}{devices} as part of Qt distributions.
    The installers create \l{glossary-buildandrun-kit}{kits} and specify build
    and run settings for the installed device types. However, you might need to
    install and configure some additional software on the devices to be able to
    connect to them from the development PC.

    \include creator-projects-build-systems.qdocinc build systems

    \section1 Using Project Wizards

    In the first step, you select a template for the project. You can filter
    templates (1) to view only those that apply to a particular target platform.

    \image qtcreator-new-project.png

    Next, you select a location for the project and specify settings for it.

    When you have completed the steps, \QC automatically generates the project
    with required headers, source files, user interface descriptions and project
    files, as defined by the wizard.

    For example, if you choose to create a Qt Quick application, \QC generates a
    QML file that you can modify in the \uicontrol Edit mode.

    \section1 Selecting Project Type

    The following table lists the wizard templates for creating projects.

    \table
        \header
            \li Category
            \li Wizard Template
            \li Purpose
        \row
            \li Application (Qt for MCU)
            \li MCU Support Application
            \li Creates an application that uses a subset of Qt QML and
                Qt Quick Controls types (as supported by Qt for MCUs) that
                you can deploy, run, and debug on MCU boards. For more
                information, see \l {Connecting MCUs}.
        \row
            \li {1,3} Application (Qt)
            \li Qt Widgets Application
            \li Uses \QD forms to design a Qt widget based user interface for
                the desktop and C++ to implement the application logic.
        \row
            \li Qt Console Application
            \li Uses a single main.cpp file.
        \row
            \li Qt Quick Application
            \li Creates a Qt Quick 2 application project that can contain both
                QML and C++ code. You can build the application and deploy it
                to desktop, embedded, and mobile target platforms.
        \row
            \li {1,4} Application (Qt for Python)
            \li Empty Application
            \li Creates a \l{https://doc.qt.io/qtforpython/index.html}
                {Qt for Python} application that contains only the main
                code for a QApplication.
        \row
            \li Empty Window
            \li Creates a Qt for Python application that contains an empty
                window.
        \row
            \li Window UI
            \li Creates a Qt for Python application that contains an empty
                window with a widget-based UI. Preferred approach that requires
                you to generate a Python file from the .ui file, to import
                it directly into your application.
        \row
            \li Window UI - Dynamic load
            \li Creates a Qt for Python application that contains an empty
                window with a widget-based UI.
        \row
            \li Qt Quick Application - Empty
            \li Creates a Python project that contains an empty Qt Quick
                Application.
        \row
            \li {1,3} Library
            \li C++ Library
            \li A shared or static C++ library based on qmake.
        \row
            \li Qt Quick 2 Extension Plugin
            \li Creates a C++ plugin that makes it possible to offer extensions
                that the QQmlEngine class can load dynamically into Qt Quick 2
                applications.
        \row
            \li \QC Plugin
            \li Creates a \QC plugin.
        \row
            \li {1,6} Other Project
            \li Qt Custom Designer Widget
            \li Creates a custom \QD widget or widget collection.
        \row
            \li Qt Quick UI Prototype
            \li Creates a \l{Creating Qt Quick UI Projects}{Qt Quick UI project}
                with a single QML file that contains the main view. You can
                preview Qt Quick 2 UI projects in the
                \l{Validating with Target Hardware}{QML Scene preview tool}.
                You do not need to build them because they do not contain any
                C++ code.

                Use this template only if you are prototyping. You cannot create
                a full application by using this template.

                You cannot deploy Qt Quick UI projects to embedded or mobile
                target platforms. For those platforms, create a Qt Quick
                application instead.
        \row
            \li Auto Test Project
            \li Creates a project with boilerplate code for a Qt or Google
                test. For more information, see \l {Creating Tests}.
        \row
            \li Subdirs Project
            \li Creates a subproject that enables you to structure your qmake
                projects as a tree hierarchy.
        \row
            \li Empty qmake Project
            \li Creates an empty qmake project that uses qmake as the build
                system but does not use any default classes.
        \row
            \li Code Snippet
            \li Creates a qmake project from a code snippet. When working on
                bug reports that contain a code snippet, you can place the code
                snippet into a project to compile and check it.
        \row
            \li {1,4} Non-Qt Project
            \li Plain C Application
            \li Creates a plain C application that uses qmake, Qbs, or CMake
                but does not use the Qt library.
        \row
            \li Plain C++ Application
            \li Creates a plain C++ application that uses qmake, Qbs, or CMake
                but does not use the Qt library.
        \row
            \li Nim Application (experimental)
            \li Creates a Nim application that uses Nimble, but does not use the
                Qt library. For more information, see \l {Setting Up Nimble}.
        \row
            \li Nimble Application (experimental)
            \li Creates a Nimble application that uses Nimble, but does not use
                the Qt library. For more information, see
                \l {Setting Up Nimble}.
        \row
            \li {1,3} Import Project
            \li Project from version control
            \li Imports a project from a supported version control system, such
                as Bazaar, CVS, Git, Mercurial, or Subversion. For more
                information about how \QC integrates version control systems,
                see \l{Using Version Control Systems}.
        \row
            \li Import as qmake or CMake Project (Limited Functionality)
            \li Imports an existing project that does not use any of the
                supported build systems: qmake, Qbs, CMake, or Autotools. The
                template creates a project file, which enables you to use
                \QC as a code editor and as a launcher for debugging and
                analysis tools. However, if you want to build the project,
                you might need to edit the generated project file.
        \row
            \li Import Existing Project
            \li Imports an existing project that does not use any of the
                supported build systems: qmake, Qbs, CMake, or Autotools.
                This enables you to use \QC as a code editor.
        \row
            \li Squish
            \li Squish Test Suite
            \li Creates a new \l {Using Squish}{Squish test suite}.

    \endtable

    To create a new project, select \uicontrol File > \uicontrol{New Project} and
    select the type of your project. The contents of the wizard dialogs depend
    on the project type and the \l{glossary-buildandrun-kit}{kits} that you select in the
    \uicontrol {Kit Selection} dialog. Follow the instructions of the wizard.

    For examples of creating different types of projects, see
    \l{Tutorials}.

    For more information about creating Qt Quick projects, see
    \l {Creating Qt Quick Projects}.

    \include creator-python-project.qdocinc python project wizards

    \section1 Specifying Project Contents

    A project can contain files that should be:

    \list
        \li Compiled or otherwise handled by the build
        \li Installed
        \li Not installed, but included in a source package created with
            \c {make dist}
        \li Not installed, nor be part of a source package, but still be known
            to \QC
    \endlist

    \QC displays all files that you declare to be part of the project by the
    project files in the \l Projects view. It sorts the files into categories
    by file type (.cpp, .h, .qrc, and so on). To display additional files, edit
    the project file. Alternatively, you can see all the files in a project
    directory in the \l {File System} view.

    Declaring files as a part of the project also makes them visible to the
    \l{Searching with the Locator}{locator} and \l{Advanced Search}
    {project-wide search}.

    \section2 CMake Projects

    When using CMake, you can specify additional files for a project by either
    adding them as sources or installing them.

    In the CMakeLists.txt file, define the files as values of the
    \l{CMake: target_sources command}{target_sources} command using
    the \c PRIVATE property, for example.

    You can prevent CMake from handling some files, such as a .cpp
    file that should not be compiled. Use the \l{CMake: set_property command}
    {set_property} command and the \l{CMake: HEADER_FILE_ONLY}
    {HEADER_FILE_ONLY} property to specify such files. For example:

    \badcode
    set_property(SOURCE "${files}" PROPERTY HEADER_FILE_ONLY ON)
    \endcode

    Alternatively, to install the files, use the \l {CMake: install command}
    {install} command with the \c FILES or \c DIRECTORY property.

    \section2 qmake Projects

    Use the following variables in the .pro file:

    \list
        \li \c SOURCES and \c HEADERS for files to compile
        \li \c INSTALLS for files to install
        \li \c DISTFILES for files to include in a source package
        \li \c OTHER_FILES for files to manage with \QC without
            installing them or including them in source packages
    \endlist

    For example, the following value includes text files in the source package:

    \badcode

    DISTFILES += *.txt

    \endcode

    \section1 Adding Subprojects to Projects

    In addition to Qt libraries, you can link your application to other
    libraries, such as system libraries or your own libraries. Further, your
    own libraries might link to other libraries. To be able to compile your
    project, you must add the libraries to your project. This also enables
    code completion and syntax highlighting for the libraries.
    The procedure of adding a library to a project depends on the build
    system that you use.

    \section2 CMake Projects

    You can add CMakeLists.txt files to any project by using the
    \l{https://cmake.org/cmake/help/latest/command/add_subdirectory.html}
    {add_subdirectory} command. The files can define complete projects that
    you include into the top-level project or any other CMake commands.

    \section2 qmake Projects

    When you create a new project and select qmake as the build system,
    you can add it to another project as a subproject in the
    \uicontrol{Project Management} dialog. However, the root project
    must specify that qmake uses the \c subdirs \l{TEMPLATE}{template} to
    build the project.

    To create a root project, select \uicontrol File >
    \uicontrol {New Project} > \uicontrol {Other Project} >
    \uicontrol {Subdirs Project} > \uicontrol Choose.

    On the \uicontrol Summary page, select \uicontrol {Finish & Add Subproject} to create
    the root project and to add another project, such as a C++ library.

    The wizard creates a project file (.pro) that defines a \c subdirs template
    and the subproject that you add as a value of the \l{Variables#subdirs}
    {SUBDIRS variable}. It also adds all the necessary files for the subproject.

    To create more subprojects, right-click the project name in the
    \uicontrol Projects view to open the context menu, and select
    \uicontrol {New Subproject}. Follow the steps in the
     \uicontrol {New Subproject} wizard to create a subproject.

    \image qtcreator-new-qt-quick-project.png

    To add an existing project as a subproject, select
    \uicontrol {Add Existing Projects} in the context menu.
    In the file browser dialog, locate your subproject.

    To remove subprojects, right-click the project name in the \uicontrol Projects
    view, and select \uicontrol {Remove Subproject} in the context menu.

    To specify dependencies, use the \uicontrol{Add Library} wizard. For more
    information, see \l{Adding Libraries to Projects}.

    \section1 Binding Keyboard Shortcuts to Wizards

    If you use a wizard regularly, you can bind a custom keyboard shortcut to
    it. Triggering this keyboard shortcut directly opens the wizard, so you do
    not need to navigate to \uicontrol File > \uicontrol {New File} or
    \uicontrol {New Project}.

    Set keyboard shortcuts for wizards in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Environment > \uicontrol Keyboard >
    \uicontrol Wizard. All wizard actions start with \uicontrol Impl there.

    \section1 Related Topics

    \list
        \li  \l{Creating Files}
        \li  \l{Opening Projects}
        \li  \l{Adding Libraries to Projects}
        \li  \l{Adding New Custom Wizards}
        \li  \l{Build Systems}
    \endlist

*/
