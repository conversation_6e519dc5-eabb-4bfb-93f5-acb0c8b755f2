Qt Creator version 2.3 contains bug fixes and new features.

The most important changes are listed in this document. For a complete
list of changes, see the Git log for the Qt Creator sources that
you can check out from the public Git repository. For example:

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --cherry-pick --pretty=oneline v2.2.1...origin/2.3

General
   * Redesigned the Welcome page to allow searching through examples with
     descriptions
   * Fixed Output panes so that they are minimized to their previous size
   * Added support for building with the Clang compiler

Editing
   * Fixed lock up when regular expression search matched empty line
   * Completions are now computed in a separate thread (non-blocking editor)

Managing Projects

Debugging
   * Color memory display highlighting the variables in stack layout
   * Correct source location for breakpoints in CDB using code model
     (QTCREATORBUG-2317)
   * Added support for multiple breakpoints from one location
     (covering constructors, destructors, template functions, and so on)
   * Added a custom dumper for QXmlAttributes, Eigen::Matrix
   * Added the option to set data breakpoints on either an address or expression
   * Added a framework to modify strings, vectors, and so on, during debugging
   * Added an option to stop on qWarning
   * Renamed "Locals and Watchers" as "Locals and Expressions"
   * Renamed "Watchpoint" as "Data Breakpoints"
   * Made original values of automatically dereferenced pointers accessible
   * Improved the "Add Breakpoint" dialog
   * Improved the displaying of enums (included numeric value)
   * Improved the speed of the QVariant dumper
   * Improved the handling of shadowed local variables
   * Fixed object expansion in tooltip
   * Fixed std::deque dumper (QTCREATORBUG-4936)
   * Fixed the handling of watched expressions with unusual characters
   * Fixed "Run To Line" on Mac (QTCREATORBUG-4619)
   * Fixed output parsing for data breakpoints on Mac (QTCREATORBUG-4797)
   * Fixed auto-continue on SIGTRAP (QTCREATORBUG-4968)
   * Fixed the dumper for gcc 4.4's std::map

Debugging QML/JS

Analyzing Code
   * Redesigned the framework code
   * Added support for QML profiling
   * Added support for the Valgrind Callgrind tool on Linux and Mac

C++ Support
   * Made C++ coding style configurable (QTCREATORBUG-2670, QTCREATORBUG-4310,
     QTCREATORBUG-2763, QTCREATORBUG-3623, QTCREATORBUG-567)
   * Various indentation fixes (QTCREATORBUG-4993)

QML/JS Support
   * Updated QML type descriptions for the current state of Qt 5
   * Made tab settings configurable separately and per-project
   * Added 'Find usages' functionality for QML types
   * Added type resolution for alias properties (QTCREATORBUG-2306)
   * Added tooltips to imports
   * Added documentation on using QML modules with plugins
   * Added support for properties and methods with a revision, allowing
     the QtQuick 1.0 and 1.1 imports to behave as intended
   * Added 'Reset code model' action to the Tools->QML/JS menu (QTCREATORBUG-4813)
   * Use qmlplugindump from Qt 4.8 or newer instead of building own qmldump
   * Improved error message for missing prototypes (QTCREATORBUG-4952)
   * Fixed completion of slots (QTCREATORBUG-3614, QTCREATORBUG-3459)
   * Fixed completion inside grouped property bindings (QTCREATORBUG-3541)
   * Fixed scanning and indentation of regular expression literals (QTCREATORBUG-4566)
   * Fixed indentation of object literals

Qt Quick Designer
  * Added tool buttons to the navigator for reordering and reparenting of items
  * Added support for import as (QTCREATORBUG-4087)
  * Added editing and navigation for inline components
  * Added support for models and delegates in the current file (QTCREATORBUG-4528)
  * Added support for repeaters (QTCREATORBUG-4852)
  * Added context menu for resetting postion and size
  * Added context menu for improved selection (QTCREATORBUG-4611)
  * Added translation support for text items
  * Added context menu for z-order (QTCREATORBUG-2522)
  * Fixed z-order in the form editor (QTCREATORBUG-5226)
  * Improved usability of the form editor (QTCREATORBUG-4820 QTCREATORBUG-4819)
  * Do not enforce clipping of items in the form editor anymore
  * Improved property editor for mouse area (QTCREATORBUG-4927)
  * Use the qmlpuppet from current Qt if availabe (QTCREATORBUG-4824)
  * Added project for qmlpuppet in share of Qt Creator
  * Fixed copy and paste bug (QTCREATORBUG-4581)
  * Fixed scoping of root item properties (QTCREATORBUG-4574)
  * Improved property editor for ListView, GridView and PathView

Help

Platform Specific

Mac
   * "Open Terminal Here" opens Terminal.app instead of xterm

Linux (GNOME and KDE)

Windows

Symbian Target

Remote Linux Support
   * Created new plugin "RemoteLinux" comprising support for Maemo/MeeGo and
     generic remote Linux hosts
   * Generic remote Linux support entails:
        * The ability to create a deploy configuration and associated steps the
          same way as for Maemo
        * A device configuration wizard for a "Generic Linux" device type
        * The possibility to add device types and associated wizards and actions
          for the device configuration dialog via third-party plugins

Qt Designer

FakeVim:
   * Made FakeVim functionality available for shortcuts in normal text editor
   * Added snippet support
   * Fixed behaviour of Ctrl keys on Mac
   * Fixed searching for expressions containing '|' (QTCREATORBUG-4752)

Version control plugins
   * Git: Manage remote repositories
   * Git: Improved branch dialog
   * Bazaar: lots of small improvements

Additional credits go to:
   Hugues Delorme for his work on the Bazaar plugin as well as version control base.
