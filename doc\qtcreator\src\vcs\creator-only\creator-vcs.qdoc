// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-wizards.html
    \page creator-version-control.html
    \nextpage creator-vcs-bazaar.html

    \title Using Version Control Systems

    Version control systems supported by \QC are:
    \table
        \header
            \li  Version Control System
            \li  Address
            \li  Notes
        \row
            \li \l{Using Bazaar}{Bazaar}
            \li \l{http://bazaar.canonical.com/}
            \li
        \row
            \li \l{Using ClearCase}{ClearCase}
            \li \l{http://www-01.ibm.com/software/awdtools/clearcase/}
            \li Disabled by default. To enable the plugin, select
                \uicontrol Help > \uicontrol {About Plugins} >
                \uicontrol {Version Control} > \uicontrol ClearCase.
                Then select \uicontrol {Restart Now} to restart \QC
                and load the plugin.
        \row
            \li \l{Using CVS}{CVS}
            \li \l{http://www.nongnu.org/cvs/}
            \li
        \row
            \li \l{https://doc.qt.io/qtcreator/creator-vcs-fossil.html}{Fossil}
            \li \l{https://fossil-scm.org/index.html/doc/trunk/www/index.wiki}
            \li To use Fossil, you need to build the Fossil plugin from
                \l{https://code.qt.io/cgit/qt-creator/plugin-fossil-scm.git/}
                {sources}, and install Fossil as described in
                \l{https://doc.qt.io/qtcreator/creator-vcs-fossil.html}
                {Qt Creator Fossil Plugin Manual}.
        \row
            \li \l{Using Git}{Git}
            \li \l{http://git-scm.com/}
            \li Git version 1.9.0, or later

                Gerrit version 2.6, or later
        \row
            \li \l{Using GitLab}{GitLab}
            \li \l{http://gitlab.com/}
            \li Experimental
        \row
            \li \l{Using Mercurial}{Mercurial}
            \li \l{http://mercurial.selenic.com/}
            \li
        \row
            \li \l{Using Perforce}{Perforce}
            \li \l{http://www.perforce.com}
            \li Server version 2006.1 and later

                Disabled by default. To enable the plugin, select
                \uicontrol Help > \uicontrol {About Plugins} >
                \uicontrol {Version Control} > \uicontrol Perforce.
                Then select \uicontrol {Restart Now} to restart \QC
                and load the plugin.
        \row
            \li \l{Using Subversion}{Subversion}
            \li \l{http://subversion.apache.org/}
            \li Subversion version 1.7.0 and later
    \endtable

    \include creator-vcs-options.qdocinc vcs options

    \section1 Creating VCS Repositories for New Projects

    \QC allows you to create repositories for version control systems that
    support local repository creation, such as Git, Mercurial, or Bazaar.
    When creating a new project by selecting \uicontrol File > \uicontrol{New File or
    Project}, you can choose a version control system on the final wizard page.

    You can also select \uicontrol Tools and then select \uicontrol {Create Repository}
    in the submenu for the version control system.

    To import a project that is under version control, choose \uicontrol File >
    \uicontrol {New Project} > \uicontrol {Project from Version Control}
    and select the version control system that you use. Follow the instructions
    of the wizard to import the project.

    \section1 Using Common Functions

    The \uicontrol{Tools} menu contains a submenu for each supported version
    control system. This section describes using the functions that are
    available for all the supported version control systems. For more
    information about the additional functions and options available for a
    particular version control system, see:

    \list
        \li \l{Using Bazaar}
        \li \l{Using ClearCase}
        \li \l{Using CVS}
        \li \l{https://doc.qt.io/qtcreator/creator-vcs-fossil.html}
            {Qt Creator Fossil Plugin Manual}
        \li \l{Using Git}
        \li \l{Using GitLab}
        \li \l{Using Mercurial}
        \li \l{Using Perforce}
        \li \l{Using Subversion}
    \endlist

    \uicontrol{Version Control} displays the commands that are
    executed, a timestamp, and the relevant output. Select \uicontrol View >
    \uicontrol Output > \uicontrol {Version Control} to open the view.

    \image qtcreator-vcs-pane.png

    \section2 Adding Files

    When you create a new file or a new project, the wizard displays a page
    asking whether the files should be added to a version control system.
    This happens when the parent directory or the project is already
    under version control and the system supports the concept of adding files,
    for example, Perforce and Subversion. Alternatively, you can
    add files later by using the version control tool menus.

    \section2 Viewing Diff Output

    All version control systems provide menu options to \e{diff} the current
    file or project: to compare it with the latest version stored in the
    repository and to display the differences. In \QC, a diff is displayed in a
    read-only editor. If the file is accessible, you can double-click on a
    selected diff chunk and \QC opens an editor displaying the file, scrolled to
    the line in question.

    \image qtcreator-vcs-diff.png

    With Git, Mercurial, and Subversion, the diff is displayed side-by-side in
    a \l{Comparing Files}{diff editor} by default. To use the inline diff view
    instead, select the \uicontrol {Switch to Text Diff Editor} (1) option from
    the toolbar. In the inline
    diff view, you can use context menu commands to apply, revert, stage, and
    unstage chunks or selected lines, as well as send chunks to a code pasting
    service.

    \section2 Viewing Versioning History and Change Details

    Display the versioning history of a file by selecting \uicontrol{Log}
    or \uicontrol{Filelog}. Typically, the log output contains the date, the commit
    message, and a change or revision identifier.

    \section2 Annotating Files

    Annotation views are obtained by selecting \uicontrol{Annotate} or \uicontrol{Blame}.
    Selecting \uicontrol{Annotate} or \uicontrol{Blame} displays the lines of the file
    prepended by the change identifier they originate from. Clicking on the
    change identifier shows a detailed description of the change.

    To show the annotation of a previous version, right-click on the
    version identifier at the beginning of a line and choose one of the
    revisions shown at the bottom of the context menu. This allows you to
    navigate through the history of the file and obtain previous versions of
    it. It also works for Git and Mercurial using SHA-1.

    The same context menu is available when right-clicking on a version
    identifier in the file log view of a single file.

    \section2 Committing Changes

    Once you have finished making changes, submit them to the version control
    system by choosing \uicontrol{Commit} or \uicontrol{Submit}. \QC displays a
    commit page containing a text editor where you can enter your commit
    message and a checkable list of modified files to include.

    \section2 Reverting Changes

    All supported version control systems support reverting your project to
    known states. This functionality is generally called \e reverting.

    The changes discarded depend on the version control system.

    A version control system can replace the \uicontrol Revert menu option with other
    options.

    \section2 Viewing Status

    You can select \uicontrol{Status} to view the status of the project or
    repository.

    \section2 Updating the Working Tree

    You can select \uicontrol Update to update your working tree with the latest
    changes from the branch. Some version control systems allow you to choose
    between updating the current project and updating all projects.

    \section2 Deleting Files

    You can select \uicontrol Delete to delete obsolete files from the repository.
*/
