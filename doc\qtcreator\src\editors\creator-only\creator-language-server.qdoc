// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-editor-fakevim.html
    \page creator-language-servers.html
    \nextpage creator-mime-types.html

    \title Using Language Servers

    For several programming languages, a \e {language server} is available
    that provides information about the code to IDEs as long as they support
    communication via the \l{Language Server Protocol}
    {language server protocol (LSP)}. This enables the
    IDE to provide the following services:

    \list
        \li \l{Completing Code}{Code completion}
        \li Sending document formatting requests to the language server to
            automatically format documents using the settings specified in
            \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor}
            > \uicontrol Behavior
        \li Highlighting the symbol under cursor
        \li \l{Viewing Function Tooltips}{Viewing function tooltips}
        \li \l{Semantic Highlighting}{Semantic highlighting}, as defined in
            \l{https://github.com/microsoft/vscode-languageserver-node/pull/367}
            {Proposal of the semantic highlighting protocol extension}
        \li Navigating in the code by using the \l{Searching with the Locator}
            {locator} or \l{Moving to Symbol Definition or Declaration}
            {moving to the symbol definition}
        \li Inspecting code by viewing the document outline in the
            \l{Viewing Defined Types and Symbols}{Outline} view or
            in the \uicontrol Symbols list on the \l{Using the Editor Toolbar}
            {editor toolbar}
        \li \l{Finding Symbols}{Finding references to symbols}
        \li \l{Renaming Symbols}{Renaming the symbol under cursor}
        \li Code actions
        \li Displaying diagnostics from the language server as tooltips. You can
            also select a code range to display diagnostics for.
    \endlist

    By providing a client for the language server protocol, \QC can support
    the above features for several other programming languages besides C++.
    However, the client does not support language servers that require special
    handling.

    \section1 Adding MIME Types for Language Servers

    \QC uses the \l{https://www.iana.org/assignments/media-types/media-types.xhtml}
    {MIME type} of the file to determine which language server to request
    information from when you open a file for editing. Add new MIME types or
    file patterns to match language servers. If you do not set at least one MIME
    type or file pattern, no files will be sent to the language server. This is
    done to avoid unnecessary traffic and inaccurate information, as files are
    only sent to the language server if they are known to be handled by it. For
    more information about how \QC uses MIME types, see \l {Editing MIME Types}.

    \section1 Specifying Settings for Language Clients

    \QC supports adding a Java language server for
    \l{Connecting Android Devices}{Android development}. A Python language
    server is added by default and you can edit its preferences.
    For other languages, you can add generic stdIO language servers.

    To add language servers, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Language Client} > \uicontrol Add (or \uicontrol {Qt Creator} >
    \uicontrol Preferences > \uicontrol {Language Client} > \uicontrol Add
    on \macos).

    To enable a language server, select the check box next to the language
    server name and set server preferences.

    To remove language servers from the list, select \uicontrol Delete.

    \section2 Generic StdIO Language Server

    To add a generic language server:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol {Language Client} > \uicontrol Add >
            \uicontrol {Generic StdIO Language Server}
            to add a generic language server.
            \image qtcreator-language-server-generic-stdio.png
        \li In the \uicontrol Name field, enter a name for the language server.
            Select the \inlineimage icons/replace.png
            (\uicontrol {Variables}) button to use a variable for the server
            name. For more information, see \l{Using Qt Creator Variables}.
        \li In the \uicontrol {Language} field, select
            \uicontrol {Set MIME Types} to select the MIME types of the files to
            send to the language server. In the field below, you can enter file
            patterns to extend the MIME types, separated by semicolons.
        \li In the \uicontrol {Startup behavior} field, select whether the
            language server is started when \QC starts or when a project or file
            with a matching MIME type is opened. \l{Viewing Output}
            {General Messages} displays information about the connection to the
            language server.
        \li In the \uicontrol {Initialization options} field, you can add
            language server specific JSON attributes to pass to an \c initialize
            request.
        \li In the \uicontrol Executable field, enter the path to the language
            server executable.
        \li In the \uicontrol Arguments field, enter any required command line
            arguments. Select \uicontrol Variables to use variables as
            arguments.
    \endlist

    \section2 Java Language Server

    To add a Java language server:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol {Language Client} > \uicontrol Add >
            \uicontrol {Java Language Server} to add a Java language server.
            \image qtcreator-language-client-options-java.png "Java language server preferences"
        \li In the \uicontrol Name field, enter a name for the language server.
            Select the \inlineimage icons/replace.png
            (\uicontrol {Variables}) button to use a variable for the server
            name. For more information, see \l{Using Qt Creator Variables}.
        \li In the \uicontrol Java field, enter the path to the Java executable.
        \li In the \uicontrol {Java Language Server} field, enter the path to
            the Java language server \c .jar file.
    \endlist

    \section2 Python Language Server

    To set preferences for Python language servers:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol Python > \uicontrol {Language Server Configuration} to
            select the Python language server plugins to use.
            \image qtcreator-python-plugins.png "Python Language Server Configuration"
        \li Select \uicontrol Advanced to configure the plugins.
            \image qtcreator-python-advanced.png "Python language server plugin configuration"
    \endlist

    For a complete list of configuration options, see
    \l{https://github.com/python-lsp/python-lsp-server/blob/develop/CONFIGURATION.md}
    {Python Language Server Configuration}.

    To disable the Python language server, deselect
    \uicontrol {Use Python Language Server}.

    \section2 QML Language Server

    Qt 6.4 ships with the \c qmlls language server that provides completion and
    warnings for QML. To set it up as a \l {Generic StdIO Language Server},
    select \c {text/x-qml} and \c {application/x-qt.ui+qml} as MIME types, and
    \c {<Qt Installation>/bin/qmlls} as executable.

    If the language server is used together with the \c QmlJSEditor plugin,
    duplicate suggestions and warnings might be shown. To avoid this, disable
    the editor plugin as described in \l {Enabling and Disabling Plugins}.

    \section1 Supported Locator Filters

    The locator enables you to browse not only files, but any items defined by
    \e {locator filters}. The language client plugin supports the following
    locator filters:

    \list
            \li Locating symbols in the current project (\c {:})
            \li Locating symbols in the current document (\c {.})
            \li Locating class (\c {c}), enum, and function (\c {m})
                definitions in your project
    \endlist

    \section1 Inspecting Language Clients

    \QC sends messages (\e Requests) to the language server and receives
    responses that contain the requested information if the language server is
    capable of handling the requests. To inspect the communication between \QC
    and language servers and view server capabilities, select \uicontrol Tools
    > \uicontrol {Debug \QC} > \uicontrol {Inspect Language Clients}.

    \image qtcreator-language-client-inspector-log.png "Language Client Inspector dialog"

    The dialog shows a list of running language servers. The value of the
    \uicontrol {Startup behavior} field in the language server preferences
    determines when the server is started. The information displayed depends on
    the language server.

    \uicontrol Log displays additional information about the selected log entry.
    You can see the \uicontrol {Content length} and \uicontrol {MIME type} of
    a \uicontrol {Client Message} and \uicontrol {Server Message}, as well as
    inspect the data sent between \QC and the language server.

    To remove old entries, select \uicontrol Clear.

    \section2 Capabilities

    In \uicontrol Capabilities, you can check whether a language server is
    capable of a specific task. You cannot modify the server capabilities
    in this dialog.

    You can view the \uicontrol Name, \uicontrol Value, and \uicontrol Type
    of the capability.

    \image qtcreator-language-client-inspector-capabilities.png "Language Client Inspector Capabilities tab"

    For some language servers, \uicontrol {Dynamic Capabilities} lists the
    \uicontrol Methods and \uicontrol Options available.

    \section2 Memory Usage

    For a clangd server, you can inspect the total amount of memory used by a
    particular component in \uicontrol {Memory Usage}.

    \image qtcreator-language-client-inspector-memory-usage.png "Language Client Inspector Memory Usage tab"

    \section1 Reporting Issues

    The language server client has been mostly tested with Python and Java.
    If problems arise when you try them or some other language, please select
    \uicontrol Help > \uicontrol {Report Bug} to report them in the
    \l{https://bugreports.qt.io/}{Qt Project Bug Tracker}. The reports
    should include \QC console output with the environment
    variable \c {QT_LOGGING_RULES=qtc.languageclient.*=true} set.
*/
