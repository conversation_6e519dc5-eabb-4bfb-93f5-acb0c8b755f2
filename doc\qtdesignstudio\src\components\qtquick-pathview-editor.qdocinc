// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [pathview]

    \section1 Path View

    A \uicontrol {Path View} component lays out data provided by data models
    on a \l{Path}.

    A graphical spline editor enables you to specify path view paths, which
    is a non-trivial task to do in the \uicontrol {Code} view.

    \image qmldesigner-pathview-editor.png "Path View editor"

    To start editing the path, double-click the path view in the
    \l {2D} view. The editor composes the path of PathCubic path
    objects. They are cubic Bezier curves to a given position with two control
    points. Drag and drop the control points in the \uicontrol {2D} view to
    construct the curve.

    In addition, PathLine and PathQuad path objects are supported indirectly.
    To make a curve segment linear, select \uicontrol {Make Curve Segment Straight} in
    the context menu.

    By default, the path is closed, which means that its start and end points
    are identical. To create separate start and end points for it, right-click
    an edit point to open a context menu, and deselect \uicontrol {Closed Path}.

    To add intermediary points to a curve segment, select \uicontrol {Split Segment}
    in the context menu.

    In the \uicontrol {Path View} section, you can specify other properties for
    the path view. The value of the \uicontrol {Drag margin} field specifies
    the maximum distance from the path that initiates mouse dragging.

    \image qtquick-designer-pathview-properties.png "Path View properties"

    Select the \uicontrol Interactive check box to make an item flickable.
    The value of the \uicontrol {Flick deceleration} field specifies the
    rate at which a flick will decelerate.

    In the \uicontrol Offset field, specify how far along the path the items
    are from their initial positions. This is a real number that ranges from
    0 to the value of the \uicontrol {Item count} field, which displays the
    number of items in the model.

    In the \uicontrol {Path View Highlight} section, you can specify properties
    for \l{View Highlight}{highlighting} path objects.

    \if defined(qtdesignstudio)
    \note You can also use the \l {SVG Path Item} Studio Component to specify an
    SVG path data string that draws a path.
    \endif

//! [pathview]

//! [svgpath]

    \section1 SVG Path Item

    The \uicontrol {SVG Path Item} component uses an SVG path data string to
    draw a path as a line.

    Setting path colors is described in \l {Picking Colors}.

    The stroke property values that specify the appearance of the path are
    described in \l{Strokes}.

    \image studio-svgpath-properties.png "SVG Path Item properties"

    The \uicontrol {Path data} field in the \uicontrol {Path Info} section
    contains the SVG path data string that specifies the path. For more
    information, see \l{https://www.w3.org/TR/SVG/paths.html#PathData}
    {W3C SVG Path Data}.

//! [svgpath]
*/
