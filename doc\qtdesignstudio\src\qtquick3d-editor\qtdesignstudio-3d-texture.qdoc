// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-texture.html
    \previouspage studio-3d-materials.html
    \nextpage studio-3d-materials-types.html

    \title Textures

    You can use the \uicontrol Texture component to attach textures to materials.
    You specify an image to use as the source file for the \uicontrol Texture,
    and also define how it is mapped to meshes in a 3D scene.

    \image studio-qtquick-3d-texture.png "Texture attached to a material in Design mode"

    \section1 Selecting the Mapping Method

    To specify the method of mapping to use when sampling a texture, select
    \uicontrol UV, \uicontrol Environment, or \uicontrol LightProbe in the
    \uicontrol {Texture mapping} field.

    UV mapping is the process of projecting 2D images to the surface of a
    3D model for texture mapping. The letters \e U and \e V denote the axes
    of the 2D texture because x, y, and z are already used to denote the
    axes of the 3D object in the model space. You can paint the polygons that
    make up a 3D object with color and other surface attributes from a UV
    texture map. Pixels in the image are assigned to surface mappings on the
    polygon. Usually this is done by programmatically copying a triangular
    piece of the image map and pasting it onto a triangle on the object.

    UV mapping is used by default for diffuse and opacity maps. It sticks the
    image to the mesh, so that a particular same portion of the image always
    appears on a particular vertex, unless you animate the UV properties.

    Environment mapping is used by default for specular reflection. It
    \e projects the image onto the material as though it is being reflected
    from the material. Using Environmental mapping for diffuse maps provides a
    mirror effect.

    Light probe mapping is used by default for HDRI sphere maps of
    light probes. For more information about light probes, see
    \l {Using Highlights and Reflections}.

    To use image data from a file, specify the path to the file in the
    \uicontrol Source field. To use a 2D Qt Quick QML type as the source,
    specify the type in the \uicontrol {Source item} field. The type is
    rendered as an offscreen layer. If you specify the source item, any
    image you might specify as a source is ignored.

    \note Currently, there is no way to forward input events to the Item
    used as a texture source.

    \section1 UV Scaling

    The \uicontrol {U scale} and \uicontrol {V scale} properties define how
    to scale the U and V texture coordinates when mapping to a mesh's UV
    coordinates.

    Scaling the U value when using horizontal tiling specifies how many times
    the texture is repeated from left to right, while scaling the V value when
    using vertical tiling specifies the repetition from bottom to top.

    To control how the texture is mapped when the U scaling value is greater
    than 1, set the horizontal tiling mode in the \uicontrol {U tiling} field.
    To control how the texture is mapped when the V scaling value is greater
    than 1, set the vertical tiling mode in the \uicontrol {V tiling} field.

    To specify that the texture is not tiled, but the value on the edge is used
    instead, select \uicontrol ClampToEdge. To repeat the texture and mirrored
    it over the x or y axis, select \uicontrol MirroredRepeat. To repeat the
    texture over the x or y axis, select \uicontrol Repeat.

    \section1 Setting UV Transform Properties

    To offset the U coordinate mapping from left to right, set the position of
    the component in the \uicontrol {U position} field. To offset the mapping
    from bottom to top, set it in the \uicontrol {V position} field.

    Specify the U and V pivot point in the \uicontrol {U pivot} and
    \uicontrol {V pivot} fields.

    To rotate the texture around the pivot point, specify rotation as degrees
    in the \uicontrol {UV rotation} field. A positive value indicates clockwise
    rotation.

    For more information about rotating and pivoting components in the local
    coordinate space, see \l {Managing 3D Transformations}.

    \section1 Applying Textures to Materials

    Drag-and-drop an image from \l Assets on a material to
    create a texture component. Dragging an image to a default or principled
    material opens a \uicontrol {Select Texture Property} dialog. You can select
    the  property to attach the texture to in the \uicontrol {Set texture to property}
    field. For a custom material, you must assign the texture to a map. If the
    \uicontrol Texture component is not displayed in \uicontrol {Components},
    you should add the \uicontrol {Qt Quick 3D} module to your project, as
    described in \l {Adding and Removing Modules}.

    To use Texture components to apply textures to materials, drag-and-drop a
    Texture component from \uicontrol Components >
    \uicontrol {Qt Quick 3D} > \uicontrol {Qt Quick 3D} to a material component
    in \l Navigator. The new texture should now be visible in the \l {2D}
    and \l {3D} views. \note If the colors in your texture are not visualized
    correctly, you should check the color in the \uicontrol Diffuse property of
    the material and try changing it to white (#ffffff).

    \image studio-qtquick-3d-material-texture.png "Material properties"

    To change the source file for the texture, select the \uicontrol Texture
    component in \uicontrol Navigator, go to the \l Properties view, and specify
    a new image to use in the \uicontrol Source field.

    \image studio-qtquick-3d-texture-properties.png "Texture properties"
*/
