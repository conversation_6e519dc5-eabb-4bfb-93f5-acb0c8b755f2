// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page quick-buttons.html
    \previouspage quick-components-creating.html
    \nextpage quick-scalable-image.html

    \title Creating Buttons

    To create a button component:

    \list 1

        \li Select \uicontrol File > \uicontrol {New File} >
            \if defined(qtcreator)
            \uicontrol Qt > \uicontrol {Qt Quick UI File} >
            \else
            \uicontrol {Qt Quick Files} > \uicontrol {Qt Quick UI File} >
            \endif
            \uicontrol Choose to create a \l{UI Files}{UI file} called
            Button.ui.qml (for example).

            \note Components are listed in \uicontrol Components >
            \uicontrol {My Components} only if the filename begins with a
            capital letter.

        \li Click \uicontrol {Design} to edit the file in the \l {2D} view.

        \li In \l Navigator, select \uicontrol Item and set the width
            (\uicontrol W) and height (\uicontrol H) of the button in
            \l Properties.

        \li Drag-and-drop a \uicontrol Rectangle from \uicontrol Components >
            \uicontrol {Default Components} > \uicontrol Basic to the component
            in \uicontrol Navigator. This creates a nested component where the
            Item is the parent of the Rectangle. Components are positioned
            relative to their parents.

        \li In the \uicontrol Properties view, modify the appearance of the
            rectangle:

        \list a

            \li In the \uicontrol Color field, select the button color.

            \li In the \uicontrol Radius field, set the radius of
                the rectangle to produce rounded corners for the button.

            \li Select \uicontrol {Layout}, and then select the
                \inlineimage icons/anchor-fill.png
                (\uicontrol {Fill to Parent}) button to anchor the Rectangle to
                the Item.


        \endlist

        \li Drag-and-drop a \uicontrol {Text} component to the Item in
            \uicontrol Navigator.

        \li In the \uicontrol Properties view, edit the properties of the
            \uicontrol Text component

        \list a

            \li In the \uicontrol Text field, enter \e Button.

                You can select the text color in the \uicontrol {Text color} field and the
                font, size, and style in the
                \uicontrol Font section.

            \li In the \uicontrol Alignment field, select the center buttons to align
                the text to the center of the button.

            \li Select \uicontrol Layout > \uicontrol {Fill to Parent}
                to anchor the text to the whole button area.

        \endlist

        \li Press \key {Ctrl+S} to save the button.

            \image qmldesigner-button.png "Button component"

    \endlist

    To be useful, the button component has to be created in a project.
    When you work on other files in the project to create screens
    or other components for the UI, the button component appears in
    \uicontrol Components > \uicontrol {My Components}.
    You can use it to create button instances and modify their properties
    to assign them useful IDs, change their appearance, and set the button
    text for each button instance, for example.

    To create a graphical button that scales beautifully without using
    vector graphics, use the \l {Border Image} component. For more
    information, see \l{Creating Scalable Buttons and Borders}.
    */

/*!
    \previouspage quick-buttons.html
    \page quick-scalable-image.html
    \if defined(qtdesignstudio)
    \nextpage qtquick-properties.html
    \else
    \nextpage studio-optimized-3d-scenes.html
    \endif

    \title Creating Scalable Buttons and Borders

    You can use the \l {Border Image} component to display an image, such as a
    PNG file, as a border and a background.

    Use two border images and suitable graphics to change the appearance of
    a button when it is clicked. You can use use \l{Working with States}{states}
    to determine which image is visible depending on whether the mouse
    button is pressed down. You could add more images and states to
    change the appearance of the button depending on other mouse events,
    such as hovered.

    Use a \l Text component to add button text.
    You can use states also to change the button text color and font size. For
    example, you can scale the button text up or down.

    Add a \l {Mouse Area} component that covers the whole area and
    reacts to mouse events.

   \image qmldesigner-borderimage-type.png "Button component in the 2D and States views"

    \section1 Creating the Button Component

    To create a button component, select \uicontrol File >
    \uicontrol {New File} >
    \if defined(qtcreator)
    \uicontrol Qt > \uicontrol {Qt Quick UI File} >
    \else
    \uicontrol {Qt Quick Files} > \uicontrol {Qt Quick UI File} >
    \endif
    \uicontrol Choose to create a \l{UI Files}{UI file} called Button.ui.qml
    (for example).

    \note Components are listed in \uicontrol Components >
    \uicontrol {My Components} only if the filename begins with a
    capital letter.

    \section1 Constructing the Button Component

    To construct the button component:

    \list 1
        \li Click \uicontrol {Design} to edit the UI file in the \l {2D} view.
        \li Select \uicontrol Assets > \inlineimage icons/plus.png
            to copy the image files you want to use to the project folder.
        \li In \l Navigator, select the root component and set the
            width (\uicontrol W) and height (\uicontrol H) of the button in the
            \l Properties view to match the size of the images
            you plan to use. This specifies the initial size of the button
            component.
        \li Drag-and-drop two \uicontrol {Border Image} components from
            \uicontrol Components > \uicontrol {Default Components} >
            \uicontrol Basic to the root component in \uicontrol Navigator.
        \li Drag-and-drop a \uicontrol Text component to the root component.
        \li Drag-and-drop a \uicontrol {Mouse Area} to the root component.
        \li Select a border image to edit the values of its properties:
            \list a
                \li In the \uicontrol Id field, enter an ID for the border
                    image. In this example, we use the ID \e inactiveButton.
                \li In the \uicontrol Source field, select the image file for
                    the border image. For example, inactive_button.png.
                \li In the \uicontrol {Layout} tab, select the
                    \inlineimage icons/anchor-fill.png
                    (\uicontrol {Fill to Parent}) button to always make the
                    image the same size as its parent. This makes the button
                    component scalable because the image size is bound to the
                    component size.
            \endlist
        \li Select the other border image to edit the values of its properties
            similarly:
            \list a
                \li In the \uicontrol Id field, enter \e activeButton.
                \li In the \uicontrol Source field, select the image file
                    for the button when it is clicked. For example,
                    active_button.png.
                \li In the \uicontrol {Layout} tab, select the
                    \inlineimage icons/anchor-fill.png
                    (\uicontrol {Fill to Parent}) button.
            \endlist
        \li Select the text component to specify font size and color in
            \uicontrol Properties:
            \list a
                \li In the \uicontrol Color field, use the \l{Picking Colors}
                    {color picker} to select the font color, or enter a value
                    in the field.
                \li In \uicontrol Font group, \uicontrol Size field, enter the
                    font size.
                \li In the \uicontrol {Layout} tab, select
                    \inlineimage icons/anchor-center-vertical.png
                    (\uicontrol {Vertical Center}) and
                    \inlineimage icons/anchor-center-horizontal.png
                    (\uicontrol {Horizontal Center}) buttons to inherit the
                    vertical and horizontal centering from the parent.
                    This ensures that the button label is centered when the
                    component is resized.
            \endlist
    \endlist

    \section1 Using States to Change Component Property Values

    \list 1
        \li In the \l States view, select \inlineimage icons/plus.png
            twice to create two new states.
            \image qmldesigner-borderimage-states.png "Active and inactive states"
        \li Select \uicontrol State1.
        \li Change the state name to \e active.
        \li Select \inlineimage icons/action-icon.png
            , and then select \uicontrol {Set when Condition} to determine
            when the state should be applied.
        \li In the \uicontrol {Binding Editor}, select the \c mouseArea
            component and the \c pressed signal to specify that the state is
            applied when the mouse button is pressed down.
            \image qmldesigner-borderimage-bindings.png "Active state when condition"
        \li Select the text component in \uicontrol Navigator to specify that the
            text size is scaled up when the button is pressed down.
        \li In \uicontrol Properties, select the \uicontrol Advanced section, and
            increase the value of the \uicontrol Scale property.
        \li Select \e inactiveButton in \uicontrol Navigator to hide
            it in the \e active state by changing the value of its
            \uicontrol Visibility property in \uicontrol Properties.
        \li Select \uicontrol State2.
        \li Change the state name to \e inactive.
        \li Set the when condition for the state to \c !mouseArea.pressed to
            specify that the state is applied when the mouse button is not
            pressed down.
            \image qmldesigner-borderimage-bindings1.png "Inactive state when condition"
        \li Press \key {Ctrl+S} to save the button.
        \li Select the \inlineimage icons/live_preview.png
            (\uicontrol {Show Live Preview}) button to check how the
            button behaves when you click it. You can drag the preview
            window borders to see what happens when you resize the
            component.
    \endlist

    To be useful, the button component has to be created in a project.
    When you work on other files in the project to create screens
    or other components for the UI, the button component appears in
    \uicontrol Components > \uicontrol {My Components}.
    You can drag-and-drop it to the \uicontrol {2D} or
    \uicontrol Navigator view to create button instances and modify the values
    of their properties to assign them useful IDs, change their appearance,
    and set the button text for each button instance, for example.

    For more information about positioning buttons on screens, see
    \l{Scalable Layouts}.

    \image qmldesigner-borderimage.png "Button preview as part of a screen"
*/
