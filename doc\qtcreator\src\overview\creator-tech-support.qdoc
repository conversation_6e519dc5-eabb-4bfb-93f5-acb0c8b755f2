// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page technical-support.html
    \if defined(qtdesignstudio)
    \previouspage studio-platforms.html
    \else
    \previouspage creator-glossary.html
    \endif
    \nextpage creator-acknowledgements.html

    \title Technical Support

    The following table lists Qt support sites and other useful links.



    \table
        \header
            \li What Do You Want to Do
            \li Where to Go

        \row
            \li View examples of what you can do with Qt Quick
            \li \l{Qt Quick Examples and Tutorials}

        \if defined(qtcreator)
        \row
            \li View examples of what you can do with Qt
            \li \l{List of Qt Examples}

        \row
            \li Develop Qt applications for desktop and \l{glossary-device}{devices}
            \li \l{https://www.qt.io/developers/}{Qt Developers}

        \row
            \li Participate in Qt development
            \li \l{https://wiki.qt.io/Qt_Contribution_Guidelines}
                {Qt Contribution Guidelines}
        \endif

       \row
            \li Find extensions for Qt, such as \QC plugins, development tools,
                and Qt modules
            \li \l{https://marketplace.qt.io/}{Qt Marketplace}

        \if defined(qtcreator)
        \row
            \li Find free Qt-based applications
            \li \l{https://github.com/topics/qt}{Qt Apps on GitHub}
        \else
        \row
            \li Find free QML and Qt Quick applications
            \li \l{https://github.com/topics/qml}{QML Apps on GitHub}
        \endif

        \row
            \li Develop with a commercial Qt license and support -
                Qt by The Qt Company
            \li \l{http://qt.io/licensing/}{Qt Licensing}
    \endtable

*/
