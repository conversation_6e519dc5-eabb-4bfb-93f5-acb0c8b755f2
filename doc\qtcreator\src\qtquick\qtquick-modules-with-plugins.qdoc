// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-qml-modules-with-plugins.html
    \if defined(qtdesignstudio)
    \previouspage studio-simulink.html
    \nextpage qtquick-adding-dynamics.html
    \else
    \previouspage creator-quick-ui-forms.html
    \nextpage creator-using-qt-designer.html
    \endif

    \title Using QML Modules with Plugins

    \l{Defining a QML Module}{QML modules} may use \l{Creating C++ Plugins for QML}
    {C++ plugins} to expose components defined in C++ to QML applications.

    To create a QML
    \if defined(qtdesignstudio)
    module and make it appear in the \l Components view:
    \else
    module:
    \endif

    \list 1

        \li Create custom components and place all the \c .qml files in a
           directory dedicated to your module. For example:
           \c {imports\asset_imports}.

        \li For Qt Quick UI projects (.qmlproject), specify the path to
            the directory that contains the module in the .qmlproject file
            of the application where you want to use the module
            as a value of the \c importPaths variable. For example
            \c{importPaths: [ "imports", "asset_imports" ]}.

        \li Create a \c qmldir file for your module and place it
            in the module directory. For more information, see
            \l {Module Definition qmldir Files}.

        \li Create a \c qmltypes file, as instructed in
            \l {Generating Type Description Files}.

        \li Create a directory named \c designer in your module directory.

        \li Create a \c .metainfo file for your module and place it in the
            \c designer directory.
            \if defined(qtdesignstudio)
            Meta information is needed to display the components in
            \uicontrol Components.
            \endif
            Use a metainfo file delivered with Qt, such as
            \c qtquickcontrols2.metainfo, as an example.

        \if defined(qtcreator)
        \li Import the module into the project, as instructed in
            \l {Importing QML Modules}.
        \endlist
        \else
        \li Build your module using the same Qt version and compiler as \QDS.
            For more information, see \l {Running QML Modules in Design Mode}.
        \endlist

    Your module should now appear in \uicontrol Components. Your components
    should appear in a subsection of \uicontrol Components if a valid
    \c .metainfo file is in place.
    \endif

    \section1 Generating Type Description Files

    When \l{Defining QML Types from C++}{registering QML types}, make sure that
    the QML module has a \c{plugins.qmltypes} file. Ideally, it should be located
    in the same directory as the \c qmldir file. The \c qmltypes file contains a
    description of the components exported by the module's plugins and is loaded
    by \QC when the module is imported.

    For more information, see \l{Type Description Files}.

    \section2 Dumping Plugins Automatically

    If a module with plugins lacks the \c qmltypes file, \QC tries to generate
    a temporary file itself by running the \c qmldump program in the background.
    However, this automatic dumping is a fallback mechanism with many points of
    failure and you cannot rely upon it.

    \section1 Importing QML Modules

    By default, \QC will look in the QML import path of Qt for QML modules.

    If you use qmake and your application adds additional import paths that
    \QC should use, specify them using \c{QML_IMPORT_PATH} in the \c{.pro}
    file of your application: \c {QML_IMPORT_PATH += path/to/module}.

    If you use CMake, add the following command to the CMakeLists.txt file to
    set the QML import path:

    \code
    set(QML_IMPORT_PATH ${CMAKE_SOURCE_DIR}/qml ${CMAKE_BINARY_DIR}/imports CACHE STRING "" FORCE)
    \endcode

    The import path affects all the targets built by the CMake project.

    \if defined(qtdesignstudio)
    \section1 Running QML Modules in Design Mode

    A QML emulation layer (also called QML Puppet) is used in the
    \uicontrol Design mode to render and preview images and to collect
    data. To be able to render custom components correctly from QML modules,
    the emulation layer must be built with the same Qt version and compiler
    as the QML modules.

    On Windows, select \uicontrol Help > \uicontrol {About Qt Design Studio} to
    check the Qt version and compiler that you need to use to build your plugin.
    For example: \c {Based on Qt 5.15.2 (MSVC 2019, 64 bit)}.

    On macOS, select \uicontrol {Qt Design Studio} >
    \uicontrol {About Qt Design Studio} to see something like:
    \c {Based on Qt 5.15.2 (Clang 10.0 (Apple), 64 bit)}.

    A plugin should behave differently depending on whether it is run by the
    emulation layer or an application. For example, animations should not be run
    in the \uicontrol Design mode. You can use the value of the \c QML_PUPPET_MODE
    environment variable to check whether the plugin is currently being run
    by an application or edited in the \uicontrol Design mode.

    If you want to use a different module in the \uicontrol Design mode
    than in your actual application for example to mockup C++ items,
    you can use \c{QML_DESIGNER_IMPORT_PATH}
    in the \c{.pro} file (for qmake projects), or declare and set the property
    \c qmlDesignerImportPaths in your product (for Qbs projects).
    Modules in the import paths defined in \c{QML_DESIGNER_IMPORT_PATH} will be
    used only in the \uicontrol Design mode.
    For an example, see \l {Qt Quick Controls - Contact List}.
    \endif
*/
