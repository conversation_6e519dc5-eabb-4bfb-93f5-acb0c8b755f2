// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-designer-developer-workflow.html
    \previouspage studio-implementing-applications.html
    \nextpage creator-coding.html

    \title Designer-Developer Workflow

    \note In this section, you are using advanced menu items. These are not
    visible by default. To toggle the visibility of advanced menu items, see
    \l{Customizing the Menu}.

    \QDS enables designers and developers to work together on common
    projects to develop applications. Designers use the \l{Design Views}{views}
    in the \uicontrol Design mode to modify \l{UI Files}{UI files} (\e .ui.qml),
    whereas developers use Qt Creator to work on the Qt Quick (\e .qml) and
    other files that are needed to implement the application logic and to
    prepare the application for production.

    Use the \l{Using Git}{Git} version control system to ensure that changes
    are not lost when files are passed back and forth between designers and
    developers.

    \QDS \l{Creating Projects}{projects} come with boilerplate code for a
    working Qt 6 application that you can build and run in Qt Creator using
    CMake. Therefore, you can open, build, and run the projects with Qt Creator.

    \QDS continues to use the \e .qmlproject file format, while Qt Creator uses a
    \e CMakeLists.txt file as the project file. This enables you to share
    your project as a fully working C++ application with developers.

    If you add or remove QML files in \QDS, you have to regenerate the
    \e CMakeLists.txt project configuration file by selecting \uicontrol Build
    > \uicontrol Run > \uicontrol {Generate CMakeLists.txt Files}.

    If you use Git, you can clone an example project
    \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/playground/AuroraCluster0}
    {here}.

    The following image shows the example project structure and contents in the
    \l Projects and \l {File System} views in \QDS and Qt Creator:

    \image studio-project-structure.png "\QDS project in \QDS and Qt Creator views"

    \section1 Converting Project Structure for CMake

    \QDS can generate \e CMakeLists.txt and other related files to use with
    Qt Creator and to compile into an executable application but only if the
    project has a certain folder structure. If you have a \QDS QML project that
    doesn't have the CMake configuration, follow these steps to convert its
    file structure to the correct format.

    \list 1
    \li Create a folder named \e content in the project's folder. This folder contains the
    application's main module.
    \li Move all QML files of the project's main module to the \e content folder. If your project
    has multiple modules, place the other modules in the \e imports or
    \e asset_imports folder.
    \li If your project's main module has resource folders such as \e fonts or \e {images}, move
    them to the \e content folder.
    \li Create a folder named \e src in the project's folder. This folder contains C++ code for
    compiling the project.
    \li If your project doesn't have an \e imports folder for other QML modules, create it
    now even if you do not have other modules. The CMake file generator expects it.
    \li In the project's \e .qmlproject file:
        \list
        \li Add \e "." in importPaths. For example:
        \code
            importPaths: [ "imports", "asset_imports", "." ]
        \endcode
        \li Change mainFile to \e "content/App.qml":
        \code
            mainFile: "content/App.qml"
        \endcode
        \endlist
    \li In the \e content folder, create a file named \e App.qml and add the following content:

        \qml
        import QtQuick
        import QtQuick.Window
        import YourImportModuleHere
        Window {
            width: Constants.width
            height: Constants.height
            visible: true
            title: "YourWindowTitleHere"
            <YourMainQmlClassHere> {
            }
        }
        \endqml

    \li In \e{App.qml}, modify imported modules, window dimensions, window title, and main QML
    class appropriately.

    \note This template assumes that your project has a module named \e YourImportModuleHere in
    the \a imports folder containing a singleton class named \a Constants.
    This isn't mandatory.

    \li Generate CMake files and C++ source files that are used to compile the application into
    an executable file by selecting \uicontrol Build > \uicontrol{Generate CMakeLists.txt files}.
    \endlist
*/
