// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage qtbridge-sketch-setup.html
    \page qtbridge-sketch-using.html
    \nextpage figmaqtbridge.html

    \title Using \QBSK

    \section1 Organizing Assets

    To get the best results when you use \QBSK to export designs from Sketch
    to \QDS, follow these guidelines when working with Sketch:

    \list
        \li Use pages for different purposes, such as \e sketching for trying
            out ideas, \e components for creating UI components, \e assets for
            images that you use in the components, and \e screens for building
            the UI screens using components and assets.
        \li Arrange each page into artboards. When you are happy with a design,
            move it from the sketching page to the components page and use it
            in screens. When you export your assets, you can skip all artboards
            that you don't want to be part of the final UI, to avoid cluttering
            the \QDS project. The code generated in \QDS corresponds to the
            structure of your Sketch document.
        \li \QBSK supports document \e symbols and \e {symbol overrides} for
            text symbols. Each symbol is exported as a component, and each
            symbol instance is generated as a respective \e component instance
            in the generated component file. The symbol overrides are exported
            as values of properties for the components. For example, if you use
            symbol overrides to specify the text for each button instance, it is
            exported as a value of the text property.
        \li Create components in Sketch and export them to \QDS before you
            start making instances of them. In \QDS, add functionality to the
            components, such as button states and then bring them back to Sketch
            as assets. If you use functional \QDS components in Sketch, you will
            find it easier to merge new iterations of the design to \QDS and
            continue to build the screens there.
        \li Use descriptive and unique IDs to avoid duplicate IDs after
            exporting assets and \l{Importing 2D Assets}{importing} them to
            \QDS.
        \li Store all assets in the scalable vector graphics (SVG) format
            to be able to easily rescale them for different screen sizes
            and resolutions. You can export assets into JPG, PNG, or SVG format
            and select options for optimizing them during the export.
    \endlist

    To use the fonts that you use in Sketch also in \QDS, you need to import
    them to \QDS as assets. \QDS deploys them to devices when you preview the
    UI. For more information, see \l{Using Custom Fonts}.

    For more information, see the \QBSK tutorials that are also accessible from
    the \uicontrol Tutorials tab of the Welcome mode:

    \list
        \li \l{https://www.qt.io/blog/qt-design-studio-sketch-bridge-tutorial-part-1}
            {Sketch Bridge Tutorial Part 1}
        \li \l{https://www.qt.io/blog/qt-design-studio-sketch-bridge-tutorial-part-2}
            {Sketch Bridge Tutorial Part 2}
    \endlist

    \section2 Items You Can Export

    You can export the following parts of your design using \QBSK:
    \list
        \li Layers
            \list
                \li Shape layers
                \li Vector layers
                \li Pencil layers
                \li Slice layers
                \li Text layers
            \endlist
        \li Groups
        \li Artboards
        \li Hotspots
        \li Symbols
        \li Libraries
    \endlist

    \section2 Using Artboards

    The relationships between artboards and layers are preserved when you export
    designs from Sketch and import them into \QDS.

    An artboard can only be exported as a component or skipped. A component will
    be imported as a separate file that contains all the artwork on the
    artboard, except layers that are set to be skipped or exported as child
    items. The child items can contain graphical assets or text.

    To use the contents of an artboard as a single image in the UI, you can
    merge the groups and layers when you export them. During import, the
    contents are flattened into one file. The merge is done in a way that
    enables you to change the groups and layers in Sketch and then export and
    import the artboard again. This is an easy way to create a reference image
    of how the final UI should look like, for example.

    Place different parts of the UI, such as menus and pop-ups, on separate
    artboards to be able to export them as components or children and to
    import them as component files and images that you can drag and drop to the
    the \uicontrol {2D} view in \QDS Design mode while creating a UI.

    If you want to use the assets on an artboard in \QDS as they are in Sketch,
    you can import the artboard without generating code for it.

    \section2 Using Layers and Groups

    When you use \QBSK to export your designs, you will determine how you want
    each layer or group to be exported: as \e merged or as \e child. Each
    layer or group represents a separate image in the UI when it is exported as
    a \e child. The asset of the layer or group is merged to its parent
    group or artboard if it is exported as \e merged.

    You can select the asset format and DPI to use for each image in
    \uicontrol Settings > \uicontrol {Asset Settings}.

    \section2 Using IDs

    The most common issues in using \QBSK are caused by having duplicate IDs in
    your project. Even though the importer in \QDS is capable of detecting and
    retaining IDs, you should still manually check all the IDs to make
    them unique and descriptive.

    Name the layers in exactly the same way as your IDs, to be able to find
    artwork later, especially as the export files can grow very large and
    complicated as they approach the level of a complete UI project.

    \note Even though \QDS is capable of handling the IDs during merges, we
    recommend that you do not change the IDs after the first time you export
    the assets, to avoid problems.

    \section1 Exporting Assets

    By default, assets are exported as follows:

    \list
        \li Artboards are always exported as \e components or \e skipped.
        \li Asset layers are exported as \e merged.
        \li Text layers can only be exported as \e child or \e skipped.
        \li A \e hotspot layer can only be exported as \e child or \e skipped.
            It is always exported as an instance of the \l {Mouse Area} component.
        \li A symbol instance layer can only be exported as \e child or
            \e skipped.
        \li Images are exported in JPG, PNG, or SVG format, depending on your
            selection.
    \endlist

    Make sure to skip all artboards that you don't want to be part of the final
    UI to avoid cluttering the \QDS project. You can select multiple artboards
    on a page and then select \uicontrol Skip in \QBSK to skip them.

    You can export assets using the default settings and make all the changes
    later in \QDS. If you are familiar with the \l{QML Syntax Basics}
    {QML syntax}, you can modify the settings to tailor the generated code to
    a certain degree. For example, you can specify the component or
    \l {Shapes}{Qt Quick Studio Component} to use for a component or
    layer. If you have drawn an arc that you mean to animate, you can export it
    as an \l Arc component to avoid having to replace the arc image with an Arc
    component in \QDS. Or you could export a button as a Qt Quick Controls
    \l Button component.

    \image qt-sketch-bridge.png

    \section2 Specifying Settings for Exporting Assets

    To export your design using \QBSK:

    \list 1
        \li \QBSK automatically proposes identifiers for all groups and layers
            that you can change in the \uicontrol {ID} field. Use unique and
            descriptive IDs to avoid duplicate IDs when the layer and the
            respective artwork is imported into \QDS.
        \li In the \uicontrol {Export As} field, select the export type for the
            group or layer:
            \list
                \li \uicontrol Component exports the selected symbol with
                    metadata. The exported data can be used later to import
                    the component as a separate UI file that contains all
                    the artwork in it, except layers or groups that are set
                    to be skipped or exported as child items.
                \li \uicontrol Child exports each asset of the selected group
                    or layer a separate PNG file, with references
                    to the images in the component file. You select the image
                    format in the \uicontrol {Asset Format} field.
                \li \uicontrol Merged merges the selected groups and layers into
                    the parent artboard or group as one item.
                \li \uicontrol Skipped completely skips the selected layer.
            \endlist
        \li In the \uicontrol {Component} field, specify the component or
            \l {Shapes}{Qt Quick Studio Component} to morph this
            layer into. The component that is generated during import will be
            of this type. For example, if you drew a rectangle, you can export
            it as a \l {basic-rectangle}{Rectangle} component.
            You can provide the import statement of the module where the
            component is defined in the \uicontrol {Imports} field.
            \note The implicit properties except position and size are not
            applied when the \uicontrol {Component} is defined. For example, all text
            properties will be ignored if \uicontrol {Component} is defined
            for a text layer, but explicit properties defined in the \uicontrol
            {Properties} field will be applied.
        \li Select the \uicontrol {Render Text} check box to render the text
            layer as an asset. The layer will be exported as an asset and the
            text data will not be exported. This allows the text layer to be
            merged to parent artboard or group as well.
        \li In the \uicontrol {Imports} field, enter additional import statements
            to have them added to the generated code file. For example, to use
            Qt Quick Controls 2.3, you need the import statement
            \c {QtQuick.Controls 2.3} and to use Qt Quick Studio Components 1.0,
            you need the import statement \c {QtQuick.Studio.Components 1.0}.
            You can also import a module as an alias.
        \li In the \uicontrol {Properties} field, specify properties for the
            component. You can add and modify properties in \QDS.
        \li Select the \uicontrol Alias check box to export the item generated
            from this layer as an alias in the parent component.
        \li Select the \uicontrol Clip check box to enable clipping in the
            component generated from the layer. The generated component will clip
            its own painting, as well as the painting of its children, to its
            bounding rectangle.
        \li Select the \uicontrol Visible check box to determine the visibility
            of the layer.
        \li In the \uicontrol Annotations field, specify annotation for the
            component. See \l {Annotating Designs}.
        \li Select the \uicontrol Settings tab to specify the export path and
            asset format.
        \li Select \uicontrol Export to export the document into a .qtbridge archive.
        \li When the exporting is done, select \uicontrol OK.
    \endlist

    All the assets and metadata are exported into a .qtbridge archive in the directory
    you specified. This might take a little while depending on the complexity of
    your project.

    You can now create a project in \QDS and import the .atbrige archive to it, as
    described in \l {Creating Projects} and \l{Importing Designs}.

    \section1 Specifying Export Path and Asset Format

    You can export assets into JPG, PNG, or SVG format. To specify export path
    and asset format, select \uicontrol Settings.

    \image qt-sketch-bridge-settings.png

    \QBSK exports assets to a .qtbridge archive named after your
    Sketch file. By default, the directory is  located inside the parent directory
    of the Sketch file being exported. You can change the export path in the
    \uicontrol {Export Path} field.

    You can select the default asset format and DPI to use for each
    layer. These settings are overridden by the layer export settings
    from the Sketch app. That is, if the layer is made exportable in
    the Sketch app, the respective asset format and DPI settings are
    preserved.

    To optimize the assets, you can select the check boxes in the
    \uicontrol {Export format options} field. You can remove metadata
    from PNG files, export assets as progressive JPG or compact SVG,
    and include SVG namespaces in SVG files.

    \section1 Scaling

    You can scale the generated assets and UI artefacts by setting a scaling factor between
    0.1 and 10.0 in \uicontrol Factor.
    \note The factor is independent of the asset scale settings, that is, 2x assets shall
    have a size of 5x when a scale factor of 2.5 is selected.

    \section1 Exporting Library Symbols

    \QBSK can handle symbols used from a local library. Before you use \QBSK to export a document
    that contains remote symbols, you must prepare the Sketch document of the local library with
    \QBSK for export.

    For more information about Sketch libraries, see Sketch documentation.

    You can either export the complete library with the document or unlink the symbols.
    Unlinking the symbols exports the symbols as if the symbols were part of the document as
    Group layers.
    Exporting the complete library exports all the layers of the library irrespective of their usage
    in the document.

    To export a library, select \uicontrol Export. \QBSK asks you whether you want to export the
    complete library or to unlink the symbols.

    \image qt-sketch-bridge-library.png
*/
