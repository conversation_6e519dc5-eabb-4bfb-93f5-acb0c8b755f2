// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-cpu-usage-analyzer.html
    \page creator-cppcheck.html
    \nextpage creator-ctf-visualizer.html

    \title Analyzing Code with Cppcheck

    \l{http://cppcheck.sourceforge.net/}{Cppcheck} is a static analysis tool
    that detects errors in C++ code. Static analysis is performed on the source
    code without actually executing the application.

    The experimental Cppcheck Diagnostics plugin integrates diagnostics
    that are generated by the Cppcheck tool into the C++ editor.

    Cppcheck is automatically run on open files. To select the files to
    check in the currently active project, select \uicontrol Analyze >
    \uicontrol Cppcheck.

    \section1 Enabling the Cppcheck Plugin

    To enable the Cppcheck plugin:

    \list 1
        \li Select \uicontrol Help > \uicontrol {About Plugins} >
            \uicontrol {Code Analyzer} > \uicontrol Cppcheck to enable the
            plugin.
        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.
    \endlist

    \section1 Running Cppcheck on Selected Files

    \list 1
        \li Select \uicontrol Analyze > \uicontrol Cppcheck.
            \image qtcreator-cppcheck-run-configuration.png "Cppcheck run configuration"
        \li In the \uicontrol Binary field, enter the path to the Cppcheck
            executable file.
        \li In the \uicontrol Checks group, select the checks to perform.
            \note By default, Cppcheck uses multiple threads to perform checks.
            Selecting the \uicontrol {Unused functions} option disables the
            default behavior.
        \li In the \uicontrol {Custom arguments} field, enter additional
            arguments for running Cppcheck. The arguments might be shadowed
            by automatically generated ones. To avoid possible conflicts in
            configuration, select the \uicontrol {Show raw output} check box
            to see the final arguments.
        \li In the \uicontrol {Ignored file patterns} field, enter a filter
            for ignoring files that match the pattern (wildcard). You can enter
            multiple patterns separated by commas. Even though Cppcheck is not
            run on files that match the provided patterns, they might be
            implicitly checked if other files include them.
        \li Select the \uicontrol {Inconclusive errors} check box to also
            mark possible false positives.
        \li Select the \uicontrol {Check all define combinations} check box to
            check all define combinations. Enabling this option can significantly
            slow down analysis, but might help to find more issues.
        \li Select the \uicontrol {Add include paths} check box to pass the
            current project's include paths to Cppcheck. Enabling this option
            slows down checks on big projects, but can help Cppcheck to find
            missing includes.
        \li Select the \uicontrol {Calculate additional arguments} check box to
            calculate additional arguments based on current project's settings
            (such as the language used and standard version) and pass them to
            Cppcheck.
        \li Select the files to run Cppcheck on.
        \li Select \uicontrol Analyze.
    \endlist

    \QC runs Cppcheck on the selected files and displays results via text
    marks or annotations.

    To specify the settings above for the automatically run checks,
    select \uicontrol Edit > \uicontrol Preferences > \uicontrol Analyzer
    > \uicontrol Cppcheck.
*/
