// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-project-qbs.html
    \page creator-projects-autotools.html
    \nextpage creator-project-generic.html

    \title Setting Up an Autotools Project

    The AutotoolsProjectManager is a plugin for autotools support. It is
    disabled by default. To enable the plugin, select \uicontrol Help >
    \uicontrol {About Plugins} > \uicontrol {Build Systems} >
    \uicontrol AutotoolsProjectManager. Then select \uicontrol {Restart Now}
    to restart \QC and load the plugin.

    To work with your Autotools project in \QC:

    \list 1

        \li Select \uicontrol File > \uicontrol {Open File or Project}.
        \li Select the Makefile.am file from your project. This is the only way
            you can use the autotools plugin.
        \li Select the build directory. Only in-source building is currently
            supported.
        \li Select \uicontrol Finish. \QC displays the project tree structure.
            The root node displays the project name. All project files are
            listed below it and you can open them from the list.

            \image qtcreator-autotools-buildrun.png

        \li Select \uicontrol Run to build and run the application. The
            predefined build steps (autogen.sh or autoreconf, configure, and
            make) are executed.

            The first time you run the application you must choose the
            executable.

        \li To check and edit autotools build steps, select
            \uicontrol Projects > \uicontrol {Build Settings}.

            You can add parameters to the predefined autotools build steps.

            \image qtcreator-autotools-buildsettings.png
    \endlist
*/
