// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-quick-tour.html
    \if defined(qtdesignstudio)
    \previouspage {Tutorials}
    \else
    \previouspage creator-overview.html
    \endif
    \nextpage creator-modes.html

    \title User Interface

    When you start \QC, it opens to the \uicontrol Welcome mode, where you can:

    \list
        \if defined(qtcreator)
        \li Open recent sessions
        \endif

        \li Open recent projects

        \li Create and open projects

        \li Open tutorials and example projects

        \if defined(qtcreator)
        \li Browse Qt extensions in the \l{https://marketplace.qt.io/}
            {Qt Marketplace}
        \li Download the Qt Installer
        \endif

        \li Read news from the online community and Qt blogs

        \li Create or manage a Qt Account

    \endlist

    \if defined(qtcreator)
    \image qtcreator-breakdown.png
    \else
    \image studio-welcome-mode.png
    \endif

    \if defined(qtcreator)
    Use the \l{Selecting Modes}{mode selector} (1) to change to another \QC mode.

    Use the kit selector (2) to select the \l{glossary-buildandrun-kit}{kit} for
    running (3), debugging (4), or building (5) the application. The task bar (7)
    displays output from these actions.

    Use the \l{Searching with the Locator}{locator} (6) to browse through
    projects, files, classes, functions, documentation, and file systems.

    For a quick tour of the user interface that takes you to the locations of
    these controls, select \uicontrol Help > \uicontrol {UI Tour}.

    The following sections describe some \QC controls in more detail:

    \list
        \li \l{Selecting Modes}{Mode selector}
        \li \l{Working with Sidebars}{Sidebars}
        \li \l{Browsing Project Contents}{Views}
        \li \l{Viewing Output}{Output}
    \endlist

    \else
    You can use the \l{Selecting Modes}{mode selector} to switch to the
    \l{Design Views}{Design mode} (1), where you will do
    most of your work and the \l{Using the Help Mode}{Help} (2) mode, where
    you can read the product documentation. The other modes are for more
    advanced use, and you are likely to need them less often.

    Below the mode selector, you can find shortcuts to some more advanced
    functions, such as running the application or finding problems in the
    QML code by debugging or profiling it.
    \endif

    \section1 What's New?

    For information about new features and bug fixes in each \QC release,
    select \uicontrol Help > \uicontrol {Change Log}.

    \section1 For \macos Users

    \QC uses standard names and locations for standard features, such as
    \e preferences. In this manual, the names and locations on
    Windows and Linux are usually used to keep the instructions short. Here are
    some places to check if you cannot find a function, dialog, or keyboard
    shortcut on \macos when following the instructions:

    \table
        \header
            \li For
            \li Look In
        \row
            \li \uicontrol Edit > \uicontrol Preferences
            \li \uicontrol {\QC} > \uicontrol Preferences
        \row
            \li \uicontrol Help > \uicontrol {About Plugins}
            \li \uicontrol {\QC} > \uicontrol {About Plugins}
        \row
            \li Keyboard shortcuts
            \li \uicontrol {\QC} > \uicontrol Preferences > \uicontrol Environment >
                \uicontrol Keyboard
    \endtable

    \if defined(qtdesignstudio)
    \section1 Customizing the Menu

    By default, top-level menu items \uicontrol Build, \uicontrol Debug, and
    \uicontrol Analyze are not visible. These menu items contain options for
    advanced functionality.

    To toggle the visibility of these menu items:
    \list 1
      \li Go to \uicontrol Edit > \uicontrol Preferences.
      \li On the \uicontrol Environment tab, select
      \uicontrol{Qt Design Studio Configuration}.
      \li Clear the checkbox for the items that you want to be visible.
    \endlist

    \image studio-menu-item-visibility.png

    You need to restart \QDS to apply changes made to these settings.

    \endif

    \section1 Switching UI Themes

    Themes enable you to change the appearance of the UI from dark to light,
    for example. To switch themes, select \uicontrol Edit > \uicontrol Preferences
    > \uicontrol Environment, and then select a theme in the \uicontrol Theme
    field.

    \image qtcreator-options-environment-interface.png "Interface preferences"

    \section1 Changing Languages

    \QC has several language versions. If the system language
    is one of the supported languages, \QC selects it automatically. To
    change the language, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Environment and select a language in the \uicontrol Language
    field. Select \uicontrol {Restart Now} to restart \QC and have the change
    take effect.

    \section1 High DPI Scaling

    The operating systems that \QC supports implement high dots-per-inch (DPI)
    scaling at varying levels. Therefore, \QC handles high DPI scaling
    differently on different operating systems:

    \list
        \li On \macos, \QC forces high DPI scaling, which means that it allows
            Qt to use the system scaling factor as the \QC scaling factor.
        \li On Windows, if you do not set \l{High DPI}
            {scaling environment variables}, \QC instructs Qt to detect the
            scaling factor and use it as the \QC scaling factor.
        \li On Linux, \QC leaves it to the user to enable high DPI scaling
            because the process varies so much on different distributions
            and windowing systems that it cannot be reliably done automatically.
    \endlist

    To override the default approach and always enable high-DPI scaling, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Environment >
    \uicontrol {Enable high DPI scaling}. The changes will take effect after you
    restart \QC.

    \section1 Navigating with Keyboard

    \QC caters not only to those users who like to use the mouse, but also
    to those who are more comfortable with the keyboard. A wide range of
    \l{keyboard-shortcuts}{keyboard} and \l{Searching with the Locator}
    {navigation} shortcuts are available to help you work faster.

    \if defined(qtcreator)
    \section1 Useful Features

    For a list of useful \QC features described in other parts of the
    documentation, see \l{How-tos}.
    \endif

    \section1 Viewing Images

    \QC opens image files in the image viewer.

    \image qtcreator-image-viewer.png "Image viewer"

    Use the toolbar buttons or \l{Keyboard Shortcuts}{keyboard shortcuts} to:

    \list
        \li \inlineimage icons/export.png
            - Export SVG images to pixmaps or copy an image as a data URL, which
            enables you to include it in web pages as if it were an external
            resource
        \li \inlineimage icons/original-size.png
            - Return images to their original size
        \li \inlineimage icons/zoom-in.png
            - Zoom in and out (\inlineimage icons/zoom-out.png
            )
        \li \inlineimage icons/run_small.png
            - Play and pause animated GIF and MNG images
        \li \inlineimage icons/qtcreator-desktopdevice-button.png
            - Show and hide the image background
        \li \inlineimage icons/switch-outline.png
            - Show and hide the image outline
        \li \inlineimage icons/fittoview.png
            - Fit images to screen
    \endlist

    Select \uicontrol {Set as Default} to use the current settings for the
    background and outline modes and fitting images to screen as default
    values for the image viewer.

    \section2 Exporting SVG Images

    If you have a freely scalable icon in the SVG format,
    you can export it to several images of different sizes to create a set of
    pixmaps.
    \if defined(qtcreator)
    You can then use QIcon::addPixmap() to add the pixmaps to icons in
    different modes and states.
    \endif

    \if defined(qtcreator)
    \section1 Location of Settings Files

    \QC creates the following files and directories:

    \list

        \li QtCreator.db

        \li QtCreator.ini

        \li qtversion.xml

        \li toolChains.xml

        \li qtcreator

        \li qtc-qmldump

    \endlist

    The location of the above files and directories depends on the platform:

    \list

        \li On Linux and other Unix platforms, look in
            \c {~/.config/QtProject} and
            \c {~/.local/share/data/QtProject/qtcreator}.

        \li On \macos, look in \c {~/.config/QtProject} and
            \c {~/Library/Application Support/QtProject/Qt Creator}.

        \li On Windows, look in
            \c {%appdata%\QtProject} and \c {%localappdata%\QtProject}.

    \endlist
    \endif


*/

/*!
    \page creator-modes.html
    \previouspage creator-quick-tour.html
    \if defined(qtdesignstudio)
    \nextpage creator-using-qt-quick-designer.html
    \else
    \nextpage creator-sidebars.html
    \endif

    \title Selecting Modes

    \image qtcreator-mode-selector.png

    \if defined(qtcreator)
    The mode selector allows you to quickly switch between tasks such as editing
    project and source files, designing application UIs, configuring projects for
    building and running, and debugging your applications. To change
    modes, click the icons, or use the \l{keyboard-shortcuts}
    {corresponding keyboard shortcut}.

    To hide the mode selector and to save space on the display, select
    \uicontrol View > \uicontrol {Mode Selector Style} > \uicontrol Hidden.
    To only show icons on the mode selector, select the \uicontrol {Icons Only}
    style.
    \endif


    You can use \QC in the following modes:

    \list

        \li \uicontrol {\l{User Interface}{Welcome}} mode for opening projects,
            tutorials, and examples.

        \li \uicontrol{\l{Coding}{Edit}} mode for editing project and source
            files.

        \if defined(qtcreator)
        \li \uicontrol{\l{Designing User Interfaces}{Design}}
            mode for designing and developing application user interfaces.
            This mode is available for UI files.
        \else
        \li \uicontrol{\l{Design Views}{Design}}
            mode for designing and developing application user interfaces.
            As a designer, you'll do most of your work in this mode.
        \endif

        \if defined(qtcreator)
        \li \uicontrol{\l{Debugging}{Debug}}
        \else
        \li \uicontrol {\l{Debugging and Profiling}{Debug}}
        \endif
            mode for inspecting the state of your
            application while debugging and for using code analysis tools
            to detect memory leaks and profile code.

        \if defined(qtcreator)
        \li \uicontrol{\l{Specifying Build Settings}{Projects}} mode
            for configuring project building and execution.
        \else
        \li \uicontrol{\l{Selecting the Preview Tool}{Projects}} mode
            for selecting the tool to use for live preview.
        \endif
            This mode is available when a project is open.

        \li \uicontrol{\l{Using the Help Mode}{Help}} mode for viewing
            documentation.

    \endlist

    \if defined(qtcreator)
    Certain actions in \QC trigger a mode change. Clicking on \uicontrol {Debug} >
    \uicontrol {Start Debugging} > \uicontrol {Start Debugging} automatically switches to
    \uicontrol {Debug} mode.
    \endif
*/

/*!
    \page creator-output-panes.html
    \if defined(qtdesignstudio)
    \previouspage creator-views.html
    \nextpage creator-highlighting.html
    \else
    \previouspage creator-open-documents-view.html
    \nextpage creator-configuring.html
    \endif

    \title Viewing Output

    \image qtcreator-general-messages.png "General Messages"

    The taskbar in \QC can display following types of output:

    \list

       \li \uicontrol{Issues}

       \li \uicontrol{Search Results}

       \li \uicontrol{Application Output}

       \li \uicontrol{Compile Output}

       \li \uicontrol {QML Debugger Console}

       \li \uicontrol{General Messages}

       \li \uicontrol{Version Control}

       \if defined(qtcreator)
       \li \l{Running Autotests}{Test Results}

       \li \l{Using Squish}{Squish} test results and Squish Server and
            Runner logs

       \li \uicontrol {To-Do Entries}

       \endif

    \endlist

    Output is available on the taskbar in all \l{Selecting Modes}{modes}.

    \image qtcreator-output-panes-taskbar.png "Output on the taskbar"

    You can view output in the following ways:

    \list
        \li Select the output view on the taskbar.
        \li Select \key Alt (\key Cmd on \macos) and the number of the view on
            the taskbar.
        \li Select \inlineimage icons/output-pane-menu.png
            , and then select the view to open.
        \li Select \uicontrol View > \uicontrol Output.
            The menu items also display the keyboard shortcuts that you can use.
    \endlist

    To maximize an open output view, select the \inlineimage icons/arrowup.png
    (\uicontrol Maximize) button or press \key {Alt+Shift+9}.


    To increase or decrease the output text size, select \inlineimage icons/plus.png
    (\uicontrol {Zoom In}) or \inlineimage icons/minus.png
    (\uicontrol {Zoom Out}), or press \key Ctrl++ or \key Ctrl+-. Zooming is
    not supported in all output views.

    To open the \uicontrol{General Messages} and
    \if defined(qtcreator)
    \l{Using Version Control Systems}{Version Control}
    \else
    \l{Using Git}{Version Control}
    \endif
     views, select \uicontrol View > \uicontrol Output.
    \if defined(qtcreator)
    To view \uicontrol {To-Do Entries}, enable the \uicontrol Todo plugin.
    \endif

    For more information about the \uicontrol {QML Debugger Console} view, see
    \l{Executing JavaScript Expressions}.

    If the text in the output is not displayed correctly, \QC might
    be using a different codec from the one used by the tools that generate
    the output. To specify the codec to use, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Environment > \uicontrol Interface, and
    then select the codec in the \uicontrol {Text codec for tools} field.

    \image qtcreator-options-environment-interface.png "Interface tab in the Environment preferences"

    \section1 Finding and Filtering Output

    To search from output, press \key {Ctrl+F} when the view is active. Enter
    search criteria in the \uicontrol Find field. For more information, see
    \l{Finding and Replacing}.

    Besides searching from the output, you can enter a string in the
    \uicontrol Filter field to filter it. To specify filtering options,
    select the \inlineimage icons/magnifier.png "Filtering options menu"
    button. You can filter output by using regular expressions or
    case-sensitivity. Select \uicontrol {Show Non-matching Lines} to
    hide the lines that match the filter.

    Finding and filtering are not supported in all output views.

    \section1 Issues

    \uicontrol{Issues} provides lists of following types of issues:

    \list

        \if defined(qtdesignstudio)
        \li \uicontrol {Asset Importer Error} - Errors and warnings encountered
            while importing assets from a design tool.
        \else
        \li \uicontrol Autotests - Errors and warnings encountered while running
            tests.
        \endif

        \li \uicontrol {Build System} - Errors and warnings encountered during a
            build.

        \if defined(qtcreator)
        \li \uicontrol {Clang Code Model} -
            \l {Parsing C++ Files with the Clang Code Model}
            {Errors and warnings from the current editor}.
        \endif

        \li \uicontrol Compile - Selected output from the compiler. Open
            \uicontrol {Compile Output} for more detailed information.

       \li \uicontrol{Debug Information} - Lists debug information packages that might
            be missing.

        \if defined(qtcreator)
        \li \uicontrol Debugger - Errors encountered while running the
            \l{Analyzing Code}{Valgrind code analysis tools}.
        \endif

       \li \uicontrol{Debugger Runtime} - Errors encountered when starting \QC. For
            example, information about missing DLLs.

       \li \uicontrol Deployment - Errors encountered between building an application
            successfully and starting it on a \l{glossary-device}{device}.

        \if defined(qtcreator)
        \li \uicontrol {My Tasks} - Entries from a task list file (.tasks) generated
            by \l{Showing Task List Files in Issues}
            {code scanning and analysis tools}.

        \li \uicontrol Python - Runtime errors and exceptions of Python scripts.
        \endif

        \li \uicontrol QML and \uicontrol {QML Analysis} -
            \l{JavaScript and QML Error Codes}
            {QML and JavaScript syntax errors}.

    \endlist

    The view filters out irrelevant output from the build tools and presents the
    issues in an organized way. To further filter the output by type, select
    \inlineimage icons/filtericon.png
    (\uicontrol {Filter Tree}) and then select a filter.

    \image qtcreator-issues.png "Issues"

    Select one or several lines to apply context-menu actions to their contents.
    You can remove the selected lines or copy their contents to the clipboard.
    For single lines, you can search the Internet for a solution using the
    contents of the line as search criteria or open a version control
    annotation view of the line that causes the error message.

    To navigate to the corresponding source code, click an issue or
    select \uicontrol {Show in Editor} in the context menu. The entry must contain the
    name of the file where the issue was found.

    To view more information about an issue in \l {Compile Output},
    select \uicontrol {Show Output} in the context menu.

    To jump from one issue to the next or previous one, press \key F6 and
    \key Shift+F6.

    By default, a new build clears the \uicontrol Issues view. To keep
    the issues from the previous build rounds, deselect \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Build & Run} > \uicontrol General >
    \uicontrol {Clear issues list on new build}.

    \section1 Search Results

    In \uicontrol{Search Results}, you can search through projects, files on
    a file system or the currently open files:

    \image qtcreator-search-results.png "Search Results"

    The search history (1) stores the search results. You can select earlier
    searches from the history.

    The figure below shows an example search result for all
    occurrences of the search string in the specified directory.

    \image qtcreator-searchresults.png

    For more information about the different search options, see
    \l {Finding and Replacing}.

    \section1 Application Output

    \uicontrol{Application Output} displays the status of a program when
    you execute it, and the debug output.

    \image qtcreator-application-output.png

    \if defined(qtcreator)
    If you specify command line arguments in the run settings that are passed
    to the application when running it, they are displayed as a part of the
    application output. For more information, see
    \l{Specifying Run Settings for Desktop Device Types}.
    \endif

    Select toolbar buttons to run applications, to attach the debugger to the
    running application, and to stop running or debugging.

    To specify settings for displaying application output, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Build & Run} >
    \uicontrol {Application Output}, or click the \inlineimage icons/settings.png
    (\uicontrol {Open Settings Page}) button. You can select whether to open
    \uicontrol{Application Output} on output when running or debugging
    applications, to clear old output on a new run,
    to word-wrap output, and to limit output to the specified number of lines.

    \section1 Compile Output

    \uicontrol{Compile Output} provides all output from the compiler.
    The \uicontrol{Compile Output} is a more detailed version of information
    displayed in \l Issues.

    \image qtcreator-compile-output.png "Compile Output"

    Double-click on a file name in an error message to open the file in the
    code editor.

    Select the \uicontrol {Cancel Build} button to cancel the build.

    To specify whether to open the \uicontrol {Compile Output} view on output
    when building applications, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Build & Run} > \uicontrol {Compile Output}, and then select the
    \uicontrol {Open Compile Output when building} check box.
    In the \uicontrol {Limit output to} field, you can specify the maximum
    amount of build output lines to display.

    You can also open the preferences page by clicking \inlineimage icons/settings.png
    (\uicontrol {Open Settings Page}).

    To copy the output to the clipboard, select \uicontrol {Select All} in the
    context menu, and then select \uicontrol Copy. Save the output as a file if
    you want to examine it later without having to build the project again.
    This is useful for large projects that take a long time to build.

    \section2 Parsing Existing Compile Output

    You can use \QC's output parsers to parse output from builds done outside
    of \QC or stored from previous build runs. By default, the parsers from the
    kit selected for the active project are used, but you can select another
    kit.

    To parse compile output:

    \list 1
        \li Select \uicontrol Tools > \uicontrol {Parse Build Output}.
            \image qtcreator-parse-build-output.png
        \li Paste the build output in the \uicontrol {Build Output} field, or
            select \uicontrol {Load from File} to load it from a file.
        \li Deselect the \uicontrol {Output went to stderr} check box if the
            parser expects issues on \c stdout.
        \li In the \uicontrol {Use parsers from kit} field, select the kit to
            use for parsing the output. Select \uicontrol Manage to view
            and modify kit settings.
        \li The parser displays the parsed output in \l Issues. By default, the
            view is cleared before adding the new output.
            Deselect the \uicontrol {Clear existing tasks} check box to append
            the new output to the old output.
        \li Select \uicontrol OK to start parsing.
    \endlist

    \if defined(qtcreator)
    \section1 To-Do Entries

    \uicontrol {To-Do Entries} lists the BUG, FIXME, NOTE, TODO, and
    WARNING keywords from the current file, from all project files, or from a
    subproject. Click the icons on the toolbar to show only the selected
    keywords.

    \image qtcreator-todo-pane.png

    To add keywords, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {To-Do} > \uicontrol Add. Set an icon and a line background color
    for the keyword.

    \image qtcreator-todo-options.png "To-Do preferences"

    To change the icon and line background color of the selected keyword, select
    \uicontrol Edit.

    To remove the selected keyword, select \uicontrol Remove.

    To reset the list to predefined keywords, select \uicontrol Reset. All your
    changes will be permanently lost.

    To determine whether the keywords in the whole project, in the current file,
    or in a subproject are displayed by default, select the appropriate option
    in the \uicontrol {Scanning scope} group.

    To exclude files from scanning, select \uicontrol {Project Settings} >
    \uicontrol {To-Do} in the \uicontrol Projects mode.

    \image qtcreator-todo-excluded-files.png "Excluded Files in To-Do preferences"

    Select \uicontrol Add and double-click the placeholder text in
    \uicontrol {Exclude Files} to enter a regular expression that
    matches the path to files to exclude. Use a forward slash (/)
    as a separator in the path also on Windows.

    Select the link in \uicontrol {Use global settings} to open global
    To-Do preferences.

    The Todo plugin is disabled by default. To enable the plugin, select
    \uicontrol Help > \uicontrol {About Plugins} > \uicontrol Utilities >
    \uicontrol Todo. Then select \uicontrol {Restart Now} to restart \QC
    and load the plugin.

    In addition, you can open task list files generated by code scanning and
    analysis tools in \l Issues. For more information, see
    \l{Showing Task List Files in Issues}.
    \endif

*/
