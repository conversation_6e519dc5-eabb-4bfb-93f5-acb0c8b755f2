// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-vcs-subversion.html
    \page creator-configuring-projects.html
    \nextpage creator-targets.html

    \title Configuring Projects

    When you install Qt for a target platform, such as Android or QNX, the
    \l{https://www.qt.io/download-qt-installer}{Qt Online Installer}
    creates \l{glossary-buildandrun-kit}{kits} for the development
    targets. Select the kits to use in the \uicontrol {Configure Projects}
    view when you open a project for the first time. At least one kit must be
    active. For more information about selecting the initial kit, see
    \l{Opening Projects}.

    To maintain the list of active kits for a currently open project, switch to
    the \uicontrol Projects mode by pressing \key Ctrl+5.

    \section1 Activating Kits for a Project

    The \uicontrol {Build & Run} section of the sidebar lists the kits that are
    compatible with your project. To activate one or more kits, click them.

    \image qtcreator-project-kits.png

    The list displays kits from \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Kits. Warning and error icons indicate that the kit configuration
    is not suitable for the project type. To view the warning and error messages,
    move the mouse pointer over the kit name.

    In the list of kits, you may see entries described as \e {Replacement for
    <kit-name>}. \QC generates them to save your project-specific settings,
    such as custom build flags or run configuration arguments that would
    disappear if the corresponding kits were simply removed when you remove
    Qt versions while updating your Qt installation. You can modify the kit
    configuration to use a currently installed Qt version and save the kit
    under a new name.

    To modify kit configuration or to add kits to the list or to remove
    them from it, select \uicontrol {Manage Kits}. For more information
    about managing kits, see \l{Adding Kits}.

    Each kit consists of a set of values that define one environment, such as a
    \l{glossary-device}{device}, compiler, and Qt version. For more information,
    see \l{Adding Qt Versions}, \l{Adding Compilers}, and \l{Adding Debuggers}.

    To copy the build and run settings for a kit to another kit, select
    \uicontrol {Copy Steps from Other Kit} in the context menu.

    To deactivate a kit, select \uicontrol {Disable Kit for Project} in the
    context menu.

    \note Deactivating a kit removes all custom build and run settings for the
    kit.

    To import an existing build for the project, select
    \uicontrol {Import Existing Build}.

    \section1 Specifying Settings

    To specify build or run settings for a kit, select \uicontrol Build or
    \uicontrol Run below the kit. For more information, see
    \l{Specifying Build Settings} and \l{Specifying Run Settings}.

    In addition, you can modify the following global settings for each project:

            \list

                \li \l{Specifying Editor Settings}{Editor}

                \li \l{Specifying Code Style Settings}{Code Style}

                \li \l{Specifying Dependencies}{Dependencies}

                \li \l{Specifying Environment Settings}{Environment}

                \li \l{Using Custom Output Parsers}{Custom Output Parsers}

                \li \l{Applying Refactoring Actions}{Quick Fixes}

                \li \l{Using Clang Tools}{Clang Tools}

                \li \l{To-Do Entries}{To-Do} (experimental)

                \li \l{Parsing C++ Files with the Clang Code Model}
                    {Clangd}

            \endlist

    If you have multiple projects open in \QC, select the project to configure
    in the list of projects.

*/
