// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-user-interaction-methods.html
    \previouspage quick-images.html
    \nextpage quick-controls.html

    \title User Interaction Methods

    You can create instances of preset basic components to add interaction
    methods to UIs, such as performing actions by using a pointing device or
    the keyboard, or flicking the visible area of the screen horizontally or
    vertically. They are availabe in \uicontrol Components >
    \uicontrol {Default Components} > \uicontrol Basic.

    In addition, you can create instances of preset \l{UI Controls} to inform
    users about the progress of the application or to gather input from users.

    The following basic components are available for user interaction:

    \list
        \li \l {Mouse Area}
        \li \l {Focus Scope}
        \li \l {Flickable}
        \li \l {Summary of Basic Interaction Methods}
    \endlist

    You can specify values for the properties of component instances in the
    \l Properties view.

    \section1 Mouse Area

    Signals and handlers are used to deliver mouse interactions. Specifically,
    you can use a \uicontrol {Mouse Area} component to define JavaScript
    callbacks (also called signal handlers), which accept mouse events within
    a defined area.

    A mouse area receives events within a defined area. One quick way to define
    this area is to \l{Setting Anchors and Margins}{anchor} the mouse area to
    its parent's area. If the parent is a \l {basic-rectangle}{Rectangle} (or
    any component that is derived from an \l {basic-item}{Item}), the mouse area
    will fill the area defined by the parent's dimensions. Alternatively, you
    can define an area smaller or larger than the parent. Several controls, such
    as \l {Button}{buttons}, contain a mouse area.

    A mouse area emits \l{Connecting Components to Signals}{signals} in response
    to different mouse events:

    \list
        \li \c canceled()
        \li \c clicked()
        \li \c doubleClicked()
        \li \c entered()
        \li \c exited()
        \li \c positionChanged()
        \li \c pressAndHold()
        \li \c pressed()
        \li \c released()
    \endlist

    \if defined(qtcreator)
    A more modern way of handling events from all pointing devices, including
    mouse and touchscreen, is via \l {Qt Quick Input Handlers}.
    \endif

    \section2 Mouse Area Properties

    A \uicontrol {Mouse Area} is an invisible component that is typically used
    in conjunction with a visible component in order to provide mouse handling
    for that component. By effectively acting as a proxy, the logic for mouse
    handling can be contained within a \uicontrol {Mouse Area} component.

    Select the \uicontrol Enable check box to enable mouse handling for the
    proxied component. When disabled, the mouse area becomes transparent to
    mouse events.

    \image qtquick-properties-mouse-area.png "Mouse Area properties"

    By default, \uicontrol {Mouse Area} components only report mouse clicks and
    not changes to the position of the mouse cursor. Select the \uicontrol Hover
    check box to ensure that the appropriate handlers are used and the values of
    other properties are updated as necessary even when no mouse buttons are
    pressed.

    Even though \uicontrol {Mouse Area} is an invisible component, it has a
    \uicontrol Visible property. Deselect the \uicontrol Visible check box in
    the \uicontrol Visibility section to make the mouse area transparent to
    mouse events.

    In the \uicontrol {Accepted buttons} field, select the mouse button that
    the mouse area reacts to. Select \uicontrol AllButtons to have the mouse
    area react to all mouse buttons. You can add support for several buttons
    in the \l {Code} view or \uicontrol {Binding Editor} by combining the
    values with the OR operator (|). For more information about the available
    values, see the developer documentation for \l {MouseArea::acceptedButtons}
    {acceptedButtons}.

    \image qtquick-properties-mouse-area-accepted-buttons.png "Adding accepted buttons in Binding Editor"

    In the \uicontrol {Cursor shape} field, select the cursor shape for this
    mouse area. On platforms that do not display a mouse cursor, this value
    may have no effect.

    In the \uicontrol {Hold interval} field, specify a value to override the
    elapsed time in milliseconds before the \c pressAndHold() signal is emitted.
    If you do not explicitly set the value or it is reset, it follows the
    globally set application style hint. Set this value if you need particular
    intervals for particular \uicontrol {Mouse Area} instances.

    Select the \uicontrol {Scroll gesture} check box to respond to scroll
    gestures from non-mouse devices, such as the 2-finger flick gesture on
    a trackpad. If the check box is not selected, the wheel signal is emitted
    only when the wheel event comes from an actual mouse with a wheel, while
    scroll gesture events will pass through to any other component that will
    handle them. For example, the user might perform a flick gesture while the
    cursor is over a component containing a \uicontrol {Mouse Area} instance,
    intending to interact with a \uicontrol Flickable component which is
    underneath. Setting this property to \c false will allow the \l PinchArea
    component to handle the mouse wheel or the pinch gesture, while the
    \uicontrol Flickable handles the flick gesture.

    Information about the mouse position and button clicks are provided via
    signals for which event handler properties are defined. If a mouse area
    overlaps with the area of other instances of the \uicontrol {Mouse Area}
    components, you can propagate \c clicked(), \c doubleClicked(), and
    \c pressAndHold() events to these other components by selecting the
    \uicontrol {Propagate events} check box. Each event is propagated to the
    next enabled \uicontrol {Mouse Area} beneath it in the stacking order,
    propagating down this visual hierarchy until a \uicontrol {Mouse Area}
    accepts the event.

    \section2 Advanced Mouse Area Properties

    You can place a \uicontrol {Mouse Area} instance within a component that
    filters child mouse events, such as \uicontrol Flickable. However, the
    mouse events might get stolen from the \uicontrol {Mouse Area} if a gesture,
    such as a flick, is recognized by the parent component.

    Select the \uicontrol {Prevent stealing} check box to stop mouse events from
    being stolen from the \uicontrol {Mouse Area} instance. This value will take
    no effect until the next \c press() event if it is set once a component has
    started stealing events.

    For more information, see the developer documentation for the \l {MouseArea}
    {Mouse Area} component.

    \section2 Drag Properties

    You can specify properties for dragging components in the \uicontrol Drag
    section. Select the component to drag in the \uicontrol Target field.
    Keep in mind that anchored components cannot be dragged.

    \image qtquick-properties-mouse-area-drag.png "Drag properties"

    In the \uicontrol Axis field, specify whether dragging can be done
    horizontally, vertically, or both.

    In the \uicontrol Threshold field, set the threshold in pixels of when the
    drag operation should start. By default, this value is bound to a platform
    dependent value.

    Select the \uicontrol {Filter children} check box to enable dragging to
    override descendant \uicontrol {Mouse Area} instances. This enables a
    parent \uicontrol {Mouse Area} instance to handle drags, for example, while
    the descendant areas handle clicks.

    Select the \uicontrol Smoothed check box to move the target component only
    after the drag operation has started. If this check box is not selected, the
    target component is moved straight to the current mouse position.

    \section1 Focus Scope

    When a key is pressed or released, a key event is generated and delivered
    to the focused component. If no component has active focus, the key event
    is ignored. If the component with active focus accepts the key event,
    propagation stops. Otherwise the event is sent to the component's parent
    until the event is accepted, or the root component is reached and the event
    is ignored.

    A component has focus when the \uicontrol Focus property in the
    \uicontrol Advanced section is set to \c true. However, for reusable
    or imported components, this is not sufficient, and you should use
    a \uicontrol {Focus Scope} component.

    Within each focus scope, one object may have focus enabled. If more
    than one component have it enabled, the last component to enable it
    will have the focus and the others are unset, similarly to when there
    are no focus scopes.

    When a focus scope receives active focus, the contained component with
    focus set (if any) also gets the active focus. If this component is
    also a focus scope, both the focus scope and the sub-focused component
    will have active focus.

    The \uicontrol {Focus Scope} component is not a visual component and
    therefore the properties of its children need to be exposed to the parent
    component of the focus scope. \l{Using Layouts}{Layouts} and
    \l{Using Positioners}{positioners} will use these visual and styling
    properties to create the layout.

    For more information, see \l {Keyboard Focus in Qt Quick}.

    \section1 Flickable

    \uicontrol Flickable places its children on a surface that can be dragged
    and flicked, causing the view onto the child components to scroll. This
    behavior forms the basis of components that are designed to show large
    numbers of child components, such as \uicontrol {List View} and
    \uicontrol {Grid View}. For more information, see \l{List and Grid Views}.

    In traditional user interfaces, views can be scrolled using standard
    controls, such as scroll bars and arrow buttons. In some situations, it
    is also possible to drag the view directly by pressing and holding a
    mouse button while moving the cursor. In touch-based user interfaces,
    this dragging action is often complemented with a flicking action, where
    scrolling continues after the user has stopped touching the view.

    The contents of a \uicontrol Flickable component are not automatically
    clipped. If the component is not used as a full-screen component, consider
    selecting the \uicontrol Clip check box in the \uicontrol Visibility
    section.

    \image qtquick-designer-flickable-properties.png "Flickable properties"

    Users can interact with a flickable component if the \uicontrol Interactive
    check box is set to \c true. Set it to \c false to temporarily disable
    flicking. This enables special interaction with the component's children.
    For example, you might want to freeze a flickable map while scrolling
    through a pop-up that is a child of the \uicontrol Flickable component.

    The \uicontrol {Flick direction} field determines whether the view can be
    flicked horizontally or vertically. Select \uicontrol AutoFlickDirection
    to enable flicking vertically if the content height is not equal to height
    of the flickable and horizontally if the content width is not equal
    to the width of the flickable. Select \uicontrol AutoFlickIfNeeded if
    the content height or width is greater than that of the flickable.

    Specify the maximum velocity for flicking the view in pixels per second in
    the \uicontrol {Max. velocity} field. Specify the rate at which a flick
    will decelerate in the \uicontrol Deceleration field.

    The value of the \uicontrol Movement field determines whether the flickable
    will give a feeling that the edges of the view are soft, rather than a hard
    physical boundary. Select \uicontrol StopAtBounds for custom edge effects
    where the contents do not follow drags or flicks beyond the bounds of the
    flickable. Select \uicontrol FollowBoundsBehavior to have the contents
    follow drags or flicks beyond the bounds of the flickable depending on the
    value of the \uicontrol Behavior field.

    In the \uicontrol {Press delay} field, specify the time in milliseconds
    to delay delivering a press to children of a flickable. This can be useful
    when reacting to a press before a flicking action has undesirable effects.
    If the flickable is dragged or flicked before the delay times out,
    the press event will not be delivered. If the button is released
    within the timeout, both the press and release will be delivered.

    \note For nested flickables with press delay set, the press delay of
    outer flickables is overridden by the innermost flickable. If the drag
    exceeds the platform drag threshold, the press event will be delivered
    regardless of this property.

    The \uicontrol {Pixel aligned} check box sets the unit of alignment set in
    the \uicontrol Content \uicontrol X and \uicontrol Y fields to pixels
    (\c true) or subpixels (\c false). Set it to \c true to optimize for still
    content or moving content with high constrast edges, such as one-pixel-wide
    lines, text, or vector graphics. Set it to \c false when optimizing for
    animation quality.

    If \uicontrol {Synchronous drag} is set to \c true, then when the mouse or
    touchpoint moves far enough to begin dragging the content, the content will
    jump, so that the content pixel which was under the cursor or touchpoint
    when pressed remains under that point. The default is \c false, which
    provides a smoother experience (no jump) at the cost of losing some of the
    drag distance at the beginning.

    \section2 Flickable Geometry

    The \uicontrol {Content size} field specifies the dimensions of the
    surface controlled by a flickable. Typically, set the values of the
    \uicontrol W and \uicontrol H fields to the combined size of the components
    placed in the flickable. You can set additional margins around the
    content in the \uicontrol {Left margin}, \uicontrol {Right margin},
    \uicontrol {Top margin}, and \uicontrol {Bottom margin} fields.

    \image qtquick-designer-flickable-geometry.png "Flickable geometry properties"

    The \uicontrol Origin field specifies the origin of the content. It
    refers to the top-left position of the content regardless of layout
    direction. Usually, the \uicontrol X and \uicontrol Y values are set to 0.
    However, a \l{ListView}{List View} and \l {GridView}{Grid View}
    may have an arbitrary origin due to delegate size variation, or component
    insertion or removal outside the visible region.

    \section1 Summary of Basic Interaction Methods

    The following table lists the components that you can use to add basic
    interaction methods to UIs with links to their developer documentation.
    They are availabe in \uicontrol Components >
    \uicontrol {Default Components} > \uicontrol Basic. The \e MCU column
    indicates which components are supported on MCUs.

    \table
    \header
        \li Icon
        \li Name
        \li MCU
        \li Purpose
    \row
        \li \inlineimage flickable-icon16.png
        \li \l [QML]{Flickable}
        \li \inlineimage ok.png
        \li Enables flicking components horizontally or vertically.
    \row
        \li \inlineimage focusscope-icon16.png
        \li \l{FocusScope}{Focus Scope}
        \li
        \li Assists in keyboard focus handling when building reusable
            components.
    \row
        \li \inlineimage mouse-area-icon16.png
        \li \l [QtQuick]{MouseArea}{Mouse Area}
        \li \inlineimage ok.png
        \li Enables simple mouse handling.
    \endtable
*/
