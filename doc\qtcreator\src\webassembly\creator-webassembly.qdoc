// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

    /*!
    \previouspage creator-developing-generic-linux.html
    \page creator-setup-webassembly.html
    \nextpage creator-build-process-customizing.html

    \title Building Applications for the Web

    WebAssembly is a binary format that allows sand-boxed executable code in
    web pages. This format is nearly as fast as native machine code, and is
    now supported by all major web browsers.

    \l {Qt for WebAssembly} enables building Qt applications so that they can be
    integrated into web pages. It doesn't require any client-side installations
    and reduces the use of server-side resources.

    The experimental WebAssembly plugin enables you to build your applications
    in WebAssembly format and deploy and run them in the local web browser.
    You can change the web browser in the project's \l{Specifying Run Settings}
    {run settings}.

    To build applications for the web and run them in a web browser, you need to
    install Qt for WebAssembly and the tool chain for compiling to WebAssembly.

    \section1 Requirements

    You need the following software to build Qt applications for the web and run
    them in a browser:

    \list
        \li Qt for WebAssembly 5.15, or later
        \li On Windows: \l{http://wiki.qt.io/MinGW}{\MinGW} 7.3.0, or later
        \li \l{https://emscripten.org/docs/introducing_emscripten/index.html}
            {emscripten} tool chain for compiling to WebAssembly
    \endlist

    \section1 Setting Up the Development Environment

    You need to install and configure Qt for WebAssembly and the tool chain for
    compiling to WebAssembly. The Qt Installer automatically adds a build and
    run kit to \QC.

    \section2 Setting Up Qt for WebAssembly

    To set up Qt for WebAssembly:

        \list 1
        \li Use the Qt maintenance tool to install Qt for WebAssembly and, on
            Windows, \MinGW (found in \uicontrol {Developer and Designer Tools}).
        \li Check out a known-good Emscripten version supported by the Qt for
            WebAssembly version that you installed, and install and activate
            \c emscripten, as instructed in
            \l {https://doc.qt.io/qt-5/wasm.html#install-emscripten}
            {Install Emscripten}.
        \endlist

    \section2 Enabling the WebAssembly Plugin

    To enable the plugin:

    \list 1
        \li In \QC, select \uicontrol Help > \uicontrol {About Plugins} >
            \uicontrol {Device Support} > \uicontrol {WebAssembly}.
        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.
    \endlist

    \section2 Specifying WebAssembly Settings

    To configure \QC for building Qt apps for the web:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Devices >
            \uicontrol WebAssembly.
        \li In the \uicontrol {Emscripten SDK path} field, enter the root
            directory where \c emsdk is installed.
        \li \QC configures the \uicontrol {Emscripten SDK environment} for you
            if the \c emsdk is supported by the Qt for WebAssembly version that
            you will use for developing the application.
            \image qtcreator-webassembly-options.png "Qt for WebAssembly device preferences"
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits.
            \image qtcreator-kit-webassembly.png "Qt for WebAssembly kit"
        \li In the \uicontrol Compiler group, \uicontrol {Emscripten Compiler}
            should have been automatically detected for both C++ and C. If not,
            check that emscripten is set up correctly.
    \endlist

    \section2 Adding WebAssembly Kits

    The Qt for Web Assembly installation automatically adds build and run kits
    to \QC. To add kits:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
            \uicontrol Add.
        \li In the \uicontrol Name field, specify a name for the kit.
        \li In the \uicontrol {Device type} field, select
            \uicontrol {WebAssembly Runtime}.
            The value of the \uicontrol Device field is automatically
            set to \uicontrol {Web Browser}.
        \li In the \uicontrol Compiler field, select
            \uicontrol {Emscripten Compiler} for both C and C++.
        \li Select \uicontrol Apply to add the kit.
    \endlist

    \section1 Running Applications in a Web Browser

    To run a project:

    \list 1
        \li Open a project for an application you want to run in a web browser.
        \li Select \uicontrol Projects > \uicontrol {Build & Run}, and then
            select the WebAssembly kit as the build and run kit for the project.
        \li Select \uicontrol Run to specify run settings.
        \li In the \uicontrol {Web browser} field, select the browser to run
            the application in.
            \image qtcreator-settings-run-webassembly.png "Selecting the browser to run in"
    \endlist

    You can now build Qt applications in WebAssembly format and run them in
    a web browser as described in \l {Building for Multiple Platforms} and
    \l{Running on Multiple Platforms}.
*/
