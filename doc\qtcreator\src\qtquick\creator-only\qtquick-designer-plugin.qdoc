// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-qtquickdesigner-plugin.html
    \previouspage quick-projects.html
    \nextpage quick-converting-ui-projects.html

    \title Using \QMLD

    We recommend that you use a separate visual editor,
    \l{Qt Design Studio Manual}{\QDS} to open and edit
    \l{UI Files}{UI files} (.ui.qml).

    However, you can enable the \QMLD plugin in \QC for editing
    UI files. The functionality is restricted and not all \QDS
    features are supported.

    To use \QMLD, switch to the \uicontrol Design mode when a ui.qml or or .qml
    file is open.

    For more information about using \QMLD, see \l{Qt Design Studio Manual}.

    \section1 Enabling the \QMLD Plugin

    To enable the \QMLD plugin:

    \list 1
        \li Select \uicontrol Help > \uicontrol {About Plugins} >
            \uicontrol {Qt Quick} > \uicontrol {QmlDesigner}.
        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.
    \endlist
*/
