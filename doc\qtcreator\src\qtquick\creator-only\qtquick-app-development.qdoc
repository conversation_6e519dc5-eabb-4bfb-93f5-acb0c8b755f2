// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-visual-editor.html
    \previouspage creator-design-mode.html
    \nextpage quick-projects.html

    \title Developing Qt Quick Applications

    You can develop Qt Quick applications in the \uicontrol Edit mode or use
    a separate visual editor, \QDS.

    If you have installed \QDS, and open a .ui.qml or a .qml file in \QC, it
    asks you whether you would like to open the file in \QDS instead. Select
    \uicontrol {Open in \QDS} to open the file in \QDS. To continue editing
    the file in \QC, close the info bar. However, we do not recommend editing
    \l{UI Files}{UI files} in the \uicontrol Edit mode because it is easy to
    add code that is not supported by \QDS. To hide the question, select
    \uicontrol {Do Not Show Again}.

    For more information about using \QDS, see \l{Qt Design Studio Manual}.

    For more information about \l{Qt QML}, \l{Qt Quick}, and \l{All QML Types}
    {QML types}, see the Qt reference documentation available online and
    in the \uicontrol Help mode.

    The following topics describe Qt Quick application development using \QC:

    \list

        \li \l {Creating Qt Quick Projects}

            You can use wizards to create Qt Quick projects.

        \li \l {Using \QMLD}

            You can enable the \QMLD plugin to visually edit
            \l{UI Files}{UI files} (.ui.qml).

        \li \l {Converting UI Projects to Applications}

            Qt Quick UI projects (.qmlproject) are useful for creating user
            interfaces. To use them for application development, you have to
            convert them to Qt Quick Application projects that contain .pro,
            .cpp, and .qrc files.

        \li \l {UI Files}

            If you switch between \QC and \QDS or cooperate with designers on
            a project, you might encounter .ui.qml files. They are intended to
            be edited in \QDS only, so you need to be careful not to break the
            code. To visually edit the files in \QC, enable the \QMLD plugin.

        \li \l{Using QML Modules with Plugins}

            You can load C++ plugins for QML to simulate data.
    \endlist
*/
