// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-writing-program.html
    \example accelbubble
    \nextpage creator-project-managing.html

    \title Creating a Mobile Application

    This tutorial describes how to use \QC to develop Qt Quick applications for
    Android and iOS devices when using Qt 6 as the minimum Qt version and CMake
    as the build system.

    We implement a Qt Quick application that accelerates an SVG (Scalable Vector
    Graphics) image based on the changing accelerometer values.

    \note You must have the \l{Qt Sensors} module from Qt 6.2 or later installed
    to be able to follow this tutorial.

    \image creator_android_tutorial_ex_app.png

    \section1 Setting up the Development Environment

    To build the application for and run it on a mobile device, you must
    set up the development environment for the device platform and configure a
    connection between \QC and the mobile device.

    To develop for Android devices, you must install \l{Qt for Android}
    and set up the development environment, as instructed in
    \l{Connecting Android Devices}.

    To develop for iOS devices, you must install Xcode and use it to configure
    a device. For this, you need an Apple developer account and iOS Developer
    Program certificate that you receive from Apple. For more information, see
    \l{Connecting iOS Devices}.

    \include qtquick-tutorial-create-empty-project.qdocinc qtquick empty application

    \section1 Adding Images as Resources

    The main view of the application displays an SVG bubble image that moves
    around the screen when you tilt the device.

    We use \e {Bluebubble.svg} in this tutorial, but you can use any other
    image or component instead.

    For the image to appear when you run the application, you must specify it
    as a resource in the \c RESOURCES section of \e CMakeLists.txt file that
    the wizard created for you:

    \quotefromfile accelbubble/CMakeLists.txt
    \skipto qt_add_qml_module
    \printuntil )

    \section1 Creating the Accelbubble Main View

    We create the main view in the \e main.qml file by adding an \l Image
    component with \e Bluebubble.svg as the source:

    \quotefromfile accelbubble/main.qml
    \skipto Image
    \printuntil smooth

    Next, we add custom properties to position the image in respect to the width
    and height of the main window:

    \printuntil y:

    We now want to add code to move the bubble based on Accelerometer sensor
    values. First, we add the following import statement:

    \quotefromfile accelbubble/main.qml
    \skipto QtSensors
    \printline QtSensors

    Next, we add the \l{Accelerometer} component with the necessary properties:

    \skipto Accelerometer
    \printuntil active

    Then, we add the following JavaScript functions that calculate the
    x and y position of the bubble based on the current Accelerometer
    values:

    \quotefromfile accelbubble/main.qml
    \skipto function
    \printuntil }
    \printuntil }

    We add the following JavaScript code for \c onReadingChanged signal of
    Accelerometer component to make the bubble move when the Accelerometer
    values change:

    \quotefromfile accelbubble/main.qml
    \skipto onReadingChanged
    \printuntil }

    We want to ensure that the position of the bubble is always
    within the bounds of the screen. If the Accelerometer returns
    \e {not a number} (NaN), the value is ignored and the bubble
    position is not updated.

    We add \l SmoothedAnimation behavior on the \c x and \c y properties of
    the bubble to make its movement look smoother.

    \quotefromfile accelbubble/main.qml
    \skipto Behavior
    \printuntil x
    \printuntil }
    \printuntil }

     \section1 Locking Device Orientation

     The device display is rotated by default when the device orientation
     changes between portrait and landscape. For this example, it would be
     better for the screen orientation to be fixed.

    To lock the orientation to portrait or landscape on Android, specify it in
    an \e AndroidManifest.xml that you can generate in \QC. For more information,
    see \l{Editing Manifest Files}.

    \image qtquick-mobile-tutorial-manifest.png "Accelbubble manifest file"

    To generate and use a manifest file, you must specify the Android package
    source directory, \c QT_ANDROID_PACKAGE_SOURCE_DIR in the \e CMakeLists.txt
    file:

    \quotefromfile accelbubble/CMakeLists.txt
    \skipto set_property
    \printuntil )

    Because our CMake version is older than 3.19, we must add a manual
    finalization step to the \c qt_add_executable function:

    \quotefromfile accelbubble/CMakeLists.txt
    \skipto qt_add_executable
    \printuntil )

    We also need to add the \c qt_finalize_executable function:

    \skipto qt_finalize_executable
    \printuntil )

    On iOS, you can lock the device orientation in an \e Info.plist file
    that you specify in the \e CMakeLists.txt file as the value of the
    \c MACOSX_BUNDLE_INFO_PLIST variable:

    \quotefromfile accelbubble/CMakeLists.txt
    \skipto set_target_properties
    \printuntil )

    \section1 Adding Dependencies

    You must tell the build system which Qt modules your application needs by
    specifying dependencies in the project file. Select \uicontrol Projects to
    update the CMake configuration with the following Qt module information:
    \c Sensors, \c Svg, \c Xml.

    The \e CMakeLists.txt file should contain the following entries that tell
    CMake to look up the Qt installation and import the Qt Sensors, Qt SVG,
    and Qt XML modules needed by the application:

    \quotefromfile accelbubble/CMakeLists.txt
    \skipto find_package
    \printuntil REQUIRED

    You also need to add the Qt modules to the list of target link libraries.
    \c target_link_libraries tells CMake that the accelbubble executable uses
    the Qt Sensors, Qt SVG, and Qt XML modules by referencing the targets
    imported by the \c find_package() call above. This adds the necessary
    arguments to the linker and makes sure that the appropriate include
    directories and compiler definitions are passed to the C++ compiler.

    \skipto target_link_libraries(appaccelbubble
    \printuntil Qt6

    After adding the dependencies, select \uicontrol Build >
    \uicontrol {Run CMake} to apply configuration changes.

    For more information about the CMakeLists.txt file, see
    \l{Getting started with CMake}.

    \section1 Running the Application

    You can now deploy the application to a device:

    \list 1

        \li Enable \e{USB Debugging} on the Android device or \e{developer mode}
            on the iOS device.

        \li Connect the device to the development PC.

    If you are using a device running Android v4.2.2, it should prompt you to
    verify the connection to allow USB debugging from the PC. To avoid such
    prompts every time you connect the device, select the
    \uicontrol {Always allow from this computer} check box, and then select
    \uicontrol OK.

        \li To run the application on the device, press \key {Ctrl+R}.

    \endlist
*/
