// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page pluginmanager.html
    \title The Plugin Manager, the Object Pool, and Registered Objects

    Usually, plugins do not need to access the plugin manager directly.
    They interact with it mostly indirectly through the ExtensionSystem::IPlugin interface.
    There are occasions though, where using the plugin manager API is necessary.
    Plugins need to access the plugin manager's object pool to extend
    some aspects of \QC, for example to add pages to the options dialog. They
    can also utilize the object pool to provide extension points for other plugins.

    \section1 Plugin Manager

    The plugin manager handles all the details regarding finding plugins, reading their
    description files, resolving plugin dependencies, loading and initializing all plugins
    in the right order, and passing on command line arguments.

    In addition, the plugin manager manages an \e{object pool}, where objects can be registered
    and retrieved depending on different criteria.

    Most interaction of plugins with the plugin manager should be done through the
    ExtensionSystem::IPlugin interface, but the following tables summarize some functions
    and signals that can be useful for plugins.
    See the ExtensionSystem::PluginManager reference documentation for the complete list.

    \table
        \header
            \li Function
            \li Description
        \row
            \li instance()
            \li Returns the singleton plugin manager instance, for example for connecting to signals.
        \row
            \li addObject()
            \li Registers an object in the object pool.
                Also available through ExtensionSystem::IPlugin::addObject().
        \row
            \li removeObject()
            \li Unregisters an object from the object pool.
                Also available through ExtensionSystem::IPlugin::removeObject().
        \row
            \li allObjects()
            \li Returns an unfiltered list of all objects that are registered
                in the object pool.
        \row
            \li getObject<T>()
            \li Returns one object of type T that is registered in the object pool.
                This respects \l{Aggregation}.
        \row
            \li getObjectByName(const QString &name)
            \li Returns one object with the given object name that is registered in the object pool.
    \endtable

    \table
        \header
            \li Signal
            \li Description
        \row
            \li objectAdded(QObject *object)
            \li Sent after an object is registered in the object pool.
        \row
            \li aboutToRemoveObject(QObject *object)
            \li Sent just before an object is unregistered from the object pool.
        \row
            \li initializationDone()
            \li Sent when plugins are running and all delayed initialization is done.
    \endtable

    \target object pool
    \section1 Object Pool and Registered Objects

    Plugins can register objects to a common \e pool that is managed by
    the plugin manager. Objects in the pool must derive from QObject, there are no other
    prerequisites.

    All objects of a specified type can be retrieved from the object pool
    via the \l{ExtensionSystem::PluginManager::getObject()}{getObject()} function.
    It is aware of Aggregation::Aggregate, and uses the Aggregation::query()
    function instead of \c qobject_cast to determine the matching objects.

     An unfiltered list of all objects that are registered in the object pool
     can be retrieved via the \l{ExtensionSystem::PluginManager::allObjects()}
     {allObjects()} function.

    It is also possible to retrieve an object with a specific object name with
    \l{ExtensionSystem::PluginManager::getObjectByName()}{getObjectByName()}
    (see QObject::objectName()).

    Whenever the state of the object pool changes, a corresponding
    \c objectAdded() or \c aboutToRemoveObject() signal is emitted
    by the plugin manager.

    A common use case for the object pool is that a plugin (or the application) provides
    an \e{extension point} for other plugins, which is a class that can
    be implemented and added to the object pool to be retrieved by the providing plugin.
    It is also possible to use the object pool to provide access to an object without actually
    linking against the providing plugin library.
*/
