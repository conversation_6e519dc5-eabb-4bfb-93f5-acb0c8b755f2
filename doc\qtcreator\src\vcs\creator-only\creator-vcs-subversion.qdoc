// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-vcs-perforce.html
    \page creator-vcs-subversion.html
    \nextpage creator-configuring-projects.html

    \title Using Subversion

    Subversion is an open source version control system.

    In addition to the standard version control system functions described in
    \l {Using Common Functions}, you can select \uicontrol Tools >
    \uicontrol Subversion > \uicontrol Describe to display commit log messages
    for a revision.

    By default, you must confirm that you want to submit changes. To suppress the
    confirmation prompt, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Version Control} > \uicontrol Subversion, and then deselect the
    \uicontrol {Prompt on submit} check box.

    To show whitespace changes in annotation views, deselect the
    \uicontrol {Ignore whitespace changes in annotation} check box.

    You can use Git as a client for a Subversion server. For more information,
    see \l{Using Git with Subversion}.
*/
