// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-qml-performance-monitor.html
    \if defined(qtdesignstudio)
    \previouspage creator-qml-debugging-example.html
    \nextpage studio-advanced.html
    \else
    \previouspage creator-analyze-mode.html
    \nextpage creator-coco.html
    \endif

    \title Profiling QML Applications

    You can use the QML Profiler to find causes for typical performance problems
    in your applications, such as slowness and unresponsive, stuttering user
    interfaces. Typical causes include executing too much JavaScript in too few
    frames. All JavaScript must return before the GUI thread can proceed, and
    frames are delayed or dropped if the GUI thread is not ready.

    Another typical cause for similar performance problems is creating,
    painting, or updating invisible items, which takes time in the GUI thread.

    \if defined(qtcreator)
    Triggering long-running C++ functions, such as paint methods and signal
    handlers, also takes time in the GUI thread, but is more difficult to see in
    the QML Profiler because it does not profile C++ code.
    \endif

    To find excessive use of JavaScript, check the frame rate in animations and
    Scene Graph events, look for gaps, and check whether the application behaves
    as expected. The JavaScript category displays the run time of functions,
    which you should try to keep below 16 ms per frame.

    To find problems caused by handling invisible items, look for dropped
    frames and check that you are not using too many short bindings or signal
    handlers that are updated per frame. You can also \l{Visualizing Overdraw}
    {visualize Scene Graph overdraw} to check scene layout and find items that
    are never visible to the users because they are located outside the screen
    or hidden beneath other, visible elements.

    \if defined(qtcreator)
    If frames get dropped even though JavaScript is not being run, and there are
    large, unexplained gaps in the timeline, check your custom QQuickItem
    implementations. You can use \l{Using Valgrind Code Analysis Tools}
    {Valgrind} or other general purpose profilers to analyze C++ code.

    You can use \e {full stack tracing} to trace from the top level QML or
    JavaScript down to the C++ and all the way to the kernel space. You can
    view the collected data in the \l{Visualizing Chrome Trace Events}
    {Chrome Trace Format Viewer}.
    \endif

    \if defined(qtdesignstudio)
    \note In this section, you are using advanced menu items. These are not
    visible by default. To toggle the visibility of advanced menu items, see
    \l{Customizing the Menu}.
    \endif

    \section1 Using QML Profiler

    To monitor the performance of an application in the QML Profiler:

    \list 1

        \li To be able to profile an application, you must set up QML debugging
            for the project. For more information, see
            \l{Setting Up QML Debugging}.

        \if defined(qtcreator)
        \li In the \uicontrol Projects mode, select a \l{glossary-buildandrun-kit}
            {kit} with Qt version 4.7.4 or later.
       \endif

            \note To profile applications on \l{glossary-device}{devices}, you
            must install Qt libraries on them.

        \li Select \uicontrol Analyze > \uicontrol {QML Profiler} to profile the
            current application.

        \li Select the
            \inlineimage icons/qtcreator-analyze-start-button.png
            (\uicontrol Start) button to start the application from the
            QML Profiler.

            \note If data collection does not start automatically, select the
            \inlineimage icons/recordfill.png
            (\uicontrol {Enable Profiling}) button.

    \endlist

    When you start analyzing an application, the application is launched, and
    the QML Profiler immediately begins to collect data. This is indicated by
    the time running in the \uicontrol Elapsed field.

    Data is collected until you select the \uicontrol {Enable Profiling} button. Data
    collection
    takes time, and therefore, there might be a delay
    before the data is displayed.

    Do not use application commands to exit the application because data is
    sent to the QML Profiler when you select the \uicontrol {Enable Profiling} button.
    The application continues to run for some seconds, after which it is stopped
    automatically. If you exit the application, the data is not sent.

    Select the \uicontrol {Disable Profiling} button to disable the automatic
    start of the data collection when an
    application is launched. Data collection starts when you select the button
    again.

    To save all the collected data, select \uicontrol Analyze >
    \uicontrol {QML Profiler Options} > \uicontrol {Save QML Trace}. To view the saved
    data, select \uicontrol {Load QML Trace}. You can also deliver the saved data to
    others for examination or load data saved by them.

    \section1 Specifying Flushing Settings

    You can specify flushing settings for the QML Profiler either globally for
    all projects or separately for each project. To specify global settings,
    select \uicontrol Edit > \uicontrol Preferences > \uicontrol Analyzer.

    To specify custom QML Profiler settings for a particular project, select
    \uicontrol Projects > \uicontrol Run and then select \uicontrol Custom in
    \uicontrol {QML Profiler Settings}. To restore the global settings for the
    project, select \uicontrol {Restore Global}.

    \image qml-profiler-settings.png "QML Profiler Settings"

    Select the \uicontrol {Flush data while profiling} check box to flush the
    data periodically instead of flushing all data when profiling stops. This
    saves memory on the target device and shortens the wait between the
    profiling being stopped and the data being displayed.

    In the \uicontrol {Flush interval} field, set the flush interval in
    milliseconds. The shorter the interval, the more often the data is flushed.
    The longer the interval, the more data has to be buffered in the target
    application, potentially wasting memory. However, the flushing itself takes
    time, which can distort the profiling results.

    If you have multiple QML engines and you want to aggregate the data produced
    by all of them into one trace, select the \uicontrol {Process data only when
    process ends} check box. Otherwise, the profiling stops when one of the
    engines stops.

    \section1 Attaching to Running Qt Quick Applications

    You can profile Qt Quick applications that are not launched by \QC.
    However, you must enable QML debugging and profiling for the application
    in the project build settings. For more information, see
    \l{Setting Up QML Debugging}.

    To attach to waiting applications:

    \list 1
        \li Select \uicontrol Analyze >
            \uicontrol {QML Profiler (Attach to Waiting Application)}.
            \image qml-profiler-start-dialog.png "Start QML Profiler dialog"
         \li In \uicontrol Kit, select the kit used to build the application.
         \li In \uicontrol Port, specify the port to listen to.
         \li Select \uicontrol OK.
    \endlist

    \section1 Analyzing Collected Data

    The \uicontrol Timeline view displays graphical representations of QML and
    JavaScript execution and a condensed view of all recorded events.

    \image qtcreator-qml-performance-monitor.png "QML Profiler"

    Each row in the timeline (6) describes a type of QML events that were
    recorded. Move the cursor on an event on a row to see how long it takes and
    where in the source it is being called. To display the information only when
    an event is selected, disable the \uicontrol {View Event Information on Mouseover}
    button (4).

    The outline (10) summarizes the period for which data was collected. Drag
    the zoom range (8) or click the outline to move on the outline. You can
    also move between events by selecting the \uicontrol {Jump to Previous Event}
    and \uicontrol {Jump to Next Event} buttons (1).

    Select the \uicontrol {Show Zoom Slider} button (2) to open a slider that you can
    use to set the zoom level. You can also drag the zoom handles (9). To reset
    the default zoom level, right-click the timeline to open the context menu,
    and select \uicontrol {Reset Zoom}.

    Click the time ruler to add vertical orientation lines (5) to the timeline.

    \section2 Selecting Event Ranges

    You can select an event range (7) to view the frame rate of events and to
    compare it with the frame rate of similar events. Select the
    \uicontrol {Select Range} button (3) to activate the selection tool. Then click in
    the timeline to specify the beginning of the event range. Drag the selection
    handle to define the end of the range. The length of the range indicates the
    frame rate of the event.

    You can use event ranges also to measure delays between two subsequent
    events. Place a range between the end of the first event and the beginning
    of the second event. The \uicontrol Duration field displays the delay between the
    events in milliseconds.

    To zoom into an event range, double-click it.

    To narrow down the current range in the \uicontrol Timeline,
    \uicontrol Statistics, and \uicontrol {Flame Graph} views, right-click
    the range and select \uicontrol {Analyze Current Range}. To return to
    the full range, select \uicontrol {Analyze Full Range} in the context menu.

    To remove an event range, close the \uicontrol Selection dialog.

    \section2 Understanding the Data

    Generally, events in the timeline view indicate how long QML or JavaScript
    execution took. Move the mouse over them to see details. For most events,
    they include location in source code, duration and some relevant parts of
    the source code itself.

    You can click on an event to move the cursor in the code editor to the part
    of the code the event is associated with.

    The following types of events are displayed in the timeline view on one or
    several rows. The availability of event types depends on the version of Qt
    the application was compiled with and the version of Qt Quick it is using.

    \table

    \header
        \li Event Category
        \li Description
        \li Minimum Qt Version
        \li Qt Quick Version

    \row
        \li \uicontrol {Pixmap Cache}
        \li Displays the general amount of pixmap data cached, in pixels. In
            addition, displays a separate event for each picture being loaded,
            with specifics about its file name and size.
        \li Qt 5.1
        \li Qt Quick 2

    \row
        \li \uicontrol {Scene Graph}
        \li Displays the time when scene graph frames are rendered and some
            additional timing information for the various stages executed to do
            so.
        \li Qt 5.1
        \li Qt Quick 2

    \row
        \li \uicontrol {Memory Usage}
        \li Displays block allocations of the JavaScript memory manager.
            Generally, the memory manager will reserve larger blocks of memory
            in one piece and later hand them out to the application in smaller
            bits. If the application requests single blocks of memory
            surpassing a certain size, the memory manager will allocate those
            separately. Those two modes of operation are shown as events of
            different colors.
            The second row displays the actual usage of the allocated memory.
            This is the amount of JavaScript heap the application has actually
            requested.
        \li Qt 5.4
        \li Qt Quick 2

    \row
        \li \uicontrol {Input Events}
        \li Displays mouse and keyboard events.
        \li Qt 4.7.4
        \li Qt Quick 1 or Qt Quick 2

    \row
        \li \uicontrol Painting
        \li Displays the time spent painting the scene for each frame.
        \li Qt 4.7.4
        \li Qt Quick 1

    \row
        \li \uicontrol Animations
        \li Displays the amount of animations that are active and the frame
            rate that they are running at.
            Information about render thread animations is displayed for
            applications that are built with Qt 5.3 or later. Render thread
            animations are shown in a separate row then.
        \li Qt 5.0 (Qt 5.3)
        \li Qt Quick 2

    \row
        \li \uicontrol Compiling
        \li Displays the time spent compiling the QML files.
        \li Qt 4.7.4
        \li Qt Quick 1 or Qt Quick 2

    \row
        \li \uicontrol Creating
        \li Displays the time spent creating the elements in the scene. In Qt
            Quick 2, creation of elements takes place in two stages. The first
            stage is for the creation of the data structures, including child
            elements. The second stage represents the completion callbacks. Not
            all elements trigger completion callbacks, though. The stages are
            shown as separate events in the timeline.
            For Qt Quick 2 applications compiled with versions of Qt before
            5.2.1 only the creation of top-level elements is shown, as single
            events.
        \li Qt 4.7.4 (Qt 5.2.1)
        \li Qt Quick 1 or Qt Quick 2

    \row
        \li \uicontrol Binding
        \li Displays the time when a binding is evaluated and how long the
            evaluation takes.
        \li Qt 4.7.4
        \li Qt Quick 1 or Qt Quick 2

    \row
        \li \uicontrol {Handling Signal}
        \li Displays the time when a signal is handled and how long the
            handling takes.
        \li Qt 4.7.4
        \li Qt Quick 1 or Qt Quick 2

    \row
        \li \uicontrol JavaScript
        \li Displays the time spent executing the actual JavaScript behind
            bindings and signal handlers. It lists all the JavaScript functions
            you may be using to evaluate bindings or handle signals.
        \li Qt 5.3
        \li Qt Quick 2

    \row
        \li \uicontrol Quick3D
        \li Displays the time spent rendering Qt Quick 3D frames, timing information
            for frame preparation and synchronization, particle system update times
            and particle update count, as well as texture and mesh memory allocations
            and memory consumption.
        \li Qt 6.3
        \li Qt Quick 3D

    \endtable

    \section2 Analyzing Scene Graph Events

    In order to understand the scene graph category, it's important to
    understand how the Qt Quick scene graph works. See
    \l {Qt Quick Scene Graph} and \l {Qt Quick Scene Graph Default Renderer}
    for a detailed description. The following events are reported in the
    \uicontrol {Scene Graph} category. Not all events are generated by all render
    loops. In the Windows and Basic render loops everything runs in the same
    thread and the distinction between GUI thread and render thread is
    meaningless.

    If you set the environment variable QSG_RENDER_TIMING, you get a textual
    output of similar, but slightly different timings from the application
    being profiled. For easier orientation, the differences are listed below.

    \table
    \header
        \li Event Type
        \li Thread
        \li Render Loop Types
        \li Label in output of QSG_RENDER_TIMING
        \li Description
        \li Caveats
    \row
        \li \uicontrol {Polish}
        \li GUI
        \li Threaded, Basic, Windows
        \li polish
        \li Final touch-up of items before they are rendered using
            QQuickItem::updatePolish().
        \li Versions of Qt prior to Qt 5.4 record no polish times for the basic
            render loop and incorrect ones for the windows render loop.
    \row
        \li \uicontrol {GUI Thread Wait}
        \li GUI
        \li Threaded
        \li lock
        \li Executing slots connected to the QQuickWindow::afterAnimating()
            signal and then locking the render thread's mutex before waiting on
            the same mutex at \uicontrol {GUI Thread Sync}. If this starts long
            before \uicontrol {Render Thread Sync}, there is \e free  time in the GUI
            thread you could be using for running additional QML or JavaScript.
        \li None
    \row
        \li \uicontrol {GUI Thread Sync}
        \li GUI
        \li Threaded
        \li blockedForSync
        \li The time the GUI thread is blocked, waiting for the render thread.
        \li None
    \row
        \li \uicontrol {Animations}
        \li GUI
        \li Threaded, Windows
        \li animations
        \li Advancing animations in the GUI thread. The basic render loop does
            not drive animations in sync with the rendering. This is why no
            animation events will be shown when using the basic render loop.
            Watch the \uicontrol {Animations} category to see animation timing in
            this case.
        \li None
    \row
        \li \uicontrol {Render Thread Sync}
        \li Render
        \li Threaded, Basic, Windows
        \li Frame rendered ... sync
        \li Synchronizing the QML state into the scene graph using
            QQuickItem::updatePaintNode().
        \li None
    \row
        \li \uicontrol {Render}
        \li Render
        \li Threaded, Basic, Windows
        \li Frame rendered ... render
        \li Total time spent rendering the frame, including preparing and
            uploading all the necessary data to the GPU. This is the \e gross
            render time. Do not confuse it with the \e net \uicontrol{Render Render}
            time below.
        \li With versions of Qt prior to Qt 5.5, the gross render time and the
            below breakup of render times may be misaligned by some
            microseconds due to different, unsynchronized timers being used to
            measure them. For example \uicontrol {Render Preprocess} might seem to
            start before \uicontrol {Render Thread Sync} is finished.
    \row
        \li \uicontrol {Swap}
        \li Render
        \li Threaded, Basic, Windows
        \li Frame rendered ... swap
        \li Swapping frames after rendering.
        \li The output of swap times triggered by setting QSG_RENDER_TIMING is
            incorrect for the basic render loop and versions of Qt prior to
            Qt 5.4. The QML profiler shows the correct swap times.
    \row
        \li \uicontrol {Render Preprocess}
        \li Render
        \li Threaded, Basic, Windows
        \li time in renderer ... preprocess
        \li Calling QSGNode::preprocess() on all nodes that need to be
            preprocessed. This is part of the gross \uicontrol {Render} step.
        \li May not be properly aligned with \uicontrol {Render} with versions of Qt
            prior to Qt 5.5.
    \row
        \li \uicontrol {Render Update}
        \li Render
        \li Threaded, Basic, Windows
        \li time in renderer ... updates
        \li Iterating and processing all the nodes in the scene graph to update
            their geometry, transformations, opacity, and other state. In the
            \uicontrol {Render Thread Sync} stage, each node is updated separately
            with state from the GUI thread. In \uicontrol {Render Update}, all the
            nodes are combined to create the final scene. This is part of the
            gross \uicontrol {Render} step.
        \li May not be properly aligned with \uicontrol {Render} with versions of Qt
            prior to Qt 5.5.
    \row
        \li \uicontrol {Render Bind}
        \li Render
        \li Threaded, Basic, Windows
        \li time in renderer ... binding
        \li Binding the correct framebuffer for OpenGL rendering. This is part
            of the gross \uicontrol {Render} step.
        \li May not be properly aligned with \uicontrol {Render} with versions of Qt
            prior to Qt 5.5.
    \row
        \li \uicontrol {Render Render}
        \li Render
        \li Threaded, Basic, Windows
        \li time in renderer ... rendering
        \li The actual process of sending all the data to the GPU via OpenGL.
            This is part of the gross \uicontrol {Render} step.
        \li May not be properly aligned with \uicontrol {Render} with versions of Qt
            prior to Qt 5.5.
    \row
        \li \uicontrol {Material Compile}
        \li Render
        \li Threaded, Basic, Windows
        \li shader compiled
        \li Compiling GLSL shader programs.
        \li None
    \row
        \li \uicontrol {Glyph Render}
        \li Render
        \li Threaded, Basic, Windows
        \li glyphs ... rendering
        \li Rendering of font glyphs into textures.
        \li Versions of Qt prior to Qt 5.4 report incorrect times for these
            events.
    \row
        \li \uicontrol {Glyph Upload}
        \li Render
        \li Threaded, Basic, Windows
        \li glyphs ... upload
        \li Uploading of glyph textures to the GPU.
        \li Versions of Qt prior to Qt 5.4 report incorrect times for these
            events.
    \row
        \li \uicontrol {Texture Bind}
        \li Render
        \li Threaded, Basic, Windows
        \li plain texture ... bind
        \li Binding a texture in the OpenGL context using glBindTextures.
        \li None
    \row
        \li \uicontrol {Texture Convert}
        \li Render
        \li Threaded, Basic, Windows
        \li plain texture ... convert
        \li Converting the format and downscaling an image to make it suitable
            for usage as a texture.
        \li None
    \row
        \li \uicontrol {Texture Swizzle}
        \li Render
        \li Threaded, Basic, Windows
        \li plain texture ... swizzle
        \li Swizzling the texture data on the CPU if necessary.
        \li None
    \row
        \li \uicontrol {Texture Upload}
        \li Render
        \li Threaded, Basic, Windows
        \li plain texture ... upload / atlastexture uploaded
        \li Uploading the texture data to the GPU.
        \li None
    \row
        \li \uicontrol {Texture Mipmap}
        \li Render
        \li Threaded, Basic, Windows
        \li plain texture ... mipmap
        \li Mipmapping a texture on the GPU.
        \li None
    \row
        \li \uicontrol {Texture Delete}
        \li Render
        \li Threaded, Basic, Windows
        \li plain texture deleted
        \li Deleting a texture from the GPU that became unnecessary.
        \li None
    \endtable

    \section2 Analyzing Qt Quick 3D Events

    The following is the list of events for Qt Quick 3D. Each rendered frame
    consists of synchronize, prepare, and render phases, which are done in that order.
    Synchronize happens in scene graph synchronizing phase, while prepare and
    render happen in scene graph rendering phase.

    Setting the environment variable \c QSG_RENDERER_DEBUG=render will also give
    additional textual output of render call counts of different rendering passes.
    These call counts are summed up in the Render Frame event.

    \table
    \header
        \li Event Type
        \li Thread
        \li Description
    \row
        \li \uicontrol {Render Frame}
        \li Render
        \li Render time of a frame. Also shows the number of draw calls.
    \row
        \li \uicontrol {Prepare Frame}
        \li Render
        \li Time taken for preparing a frame. Resources are allocated and loaded
            in the prepare phase. The first frame after scene loading usually takes
            longer than others since most resources are loaded at that time.
    \row
        \li \uicontrol {Synchronize Frame}
        \li Render
        \li Synchronize time of a frame. Synchronize takes care of updating backend
            values from the frontend. Also manages shared resources between Qt Quick
            Scene Graph and Qt Quick 3D.
    \row
        \li \uicontrol {Mesh Load}
        \li Render
        \li Load time of a mesh. Shows total memory usage of all meshes. Also shows
            unloads.
    \row
        \li \uicontrol {Custom Mesh Load}
        \li Render
        \li Load time of a custom mesh. Shows total memory usage of all meshes.
            Also shows unloads.
    \row
        \li \uicontrol {Texture Load}
        \li Render
        \li Load time of a texture. Shows total memory usage of all textures.
            Also shows unloads.
    \row
        \li \uicontrol {Generate Shader}
        \li Render
        \li Time for generating a shader for a material.
    \row
        \li \uicontrol {Load Shader}
        \li Render
        \li Time for loading a built-in shader.
    \row
        \li \uicontrol {Particle Update}
        \li GUI
        \li Update time of a particle system. Shows the number of particles updated.
    \row
        \li \uicontrol {Mesh Memory Consumption}
        \li Render
        \li Shows a bar view of total mesh memory consumption.
    \row
        \li \uicontrol {Texture Memory Consumption}
        \li Render
        \li Shows a bar view of total texture memory consumption.
    \endtable

    \section1 Viewing Statistics

    The \uicontrol Statistics view displays the number of times each binding, create,
    compile, JavaScript, or signal event is triggered and the average time it
    takes. This allows you to examine which events you need to optimize. A high
    number of occurrences might indicate that an event is triggered
    unnecessarily. To view the median, longest, and shortest time for the
    occurrences, select \uicontrol {Extended Event Statistics} in the context menu.

    Click on an event to move to it in the source code
    in the code editor.

    \image qml-profiler-statistics.png "Statistics view"

    The \uicontrol Callers and \uicontrol Callees panes show dependencies between events.
    They allow you to examine the internal functions of the application.
    The \uicontrol Callers pane summarizes the QML events that trigger a binding.
    This tells you what caused a change in a binding.
    The \uicontrol Callees pane summarizes the QML events that a binding triggers.
    This tells you which QML events are affected if you change a binding.

    Click on an event to move to it in the source code in the code editor.

    When you select an event in the \uicontrol Timeline view, information about
    it is displayed in the \uicontrol Statistics and \uicontrol {Flame Graph}
    views.

    To copy the contents of one view or row to the clipboard, select
    \uicontrol {Copy Table} or \uicontrol {Copy Row} in the context menu.

    JavaScript events are shown in the \uicontrol Statistics view only for applications
    that use Qt Quick 2 and are compiled with Qt 5.3 or later.

    \section2 Visualizing Statistics as Flame Graphs

    The \uicontrol {Flame Graph} view shows a more concise statistical overview
    of QML and JavaScript execution. In the \uicontrol {Total Time} view, the
    horizontal bars show the amount of
    time all invocations of a certain function took together, relative to the
    total runtime of all JavaScript and QML events. The nesting shows which
    functions were called by which other ones.

    \image qml-profiler-flamegraph.png "Flame Graph View"

    To view the total amount of memory allocated by the functions, select
    \uicontrol Memory in the drop-down menu.

    To view the the number of memory allocations performed by the functions,
    select \uicontrol Allocations.

    Double-click an item in a view to zoom into it. Double-click an empty
    area in the view to zoom out again.

    Unlike the
    \uicontrol Timeline view, the \uicontrol {Flame Graph} view does not show the
    time spans when no QML or JavaScript is running at all. Thus, it is not
    suitable for analyzing per frame execution times. However, it is very easy
    to see the total impact of the various QML and JavaScript events there.

*/
