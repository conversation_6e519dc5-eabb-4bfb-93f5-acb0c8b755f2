// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-project-conan.html
    \previouspage creator-project-incredibuild.html
    \nextpage creator-cli.html

    \title Setting Up Conan

    \l {https://conan.io}{<PERSON>} is a C/C++ package manager that speeds up the
    integration of C or C++ libraries into your own project. It's available on
    all the \l{Supported Platforms}{supported development platforms}.

    <PERSON> can be integrated into most build systems that are integrated into
    \QC:

    \list
        \li \l{https://docs.conan.io/en/latest/integrations/cmake.html}{CMake}
        \li \l{https://docs.conan.io/en/latest/integrations/qbs.html}{Qbs}
        \li \l{https://docs.conan.io/en/latest/integrations/qmake.html}{qmake}
        \li \l{https://docs.conan.io/en/latest/integrations/meson.html}{Meson}
    \endlist

    The client-server architecture of <PERSON> enables the client to fetch packages
    from and upload them to remote servers that act as package storage. The
    client creates the packages, and if necessary, handles building them from
    sources. Because the client has a local cache for package storage, you can
    work offline, as long as no new packages are needed from remote servers.

    To use Conan, install it by using the Qt installer or the tools provided by
    your operating system. For example, on Windows, you can use the
    \c {choco install conan} or \c {pip install conan} command.

    To enable the experimental Conan plugin, select \uicontrol Help >
    \uicontrol {About Plugins} > \uicontrol Utilities > \uicontrol Conan.
    Then select \uicontrol {Restart Now} to restart \QC and load the plugin.

    For each project, you must write a
    \l{https://docs.conan.io/en/latest/reference/conanfile.html}{conanfile.py}
    or \l{https://docs.conan.io/en/latest/reference/conanfile_txt.html}
    {conanfile.txt} file that specifies the needed libraries and packages.
    Then, you must edit the build settings of the project to specify the
    location of the file and the contents of the Conan install command.
    For more information, see \l {Conan Build Steps}.

    Alternatively, you can automatically set up the Conan package manager for
    use with CMake. For more information, see \l{Using CMake with Conan}.
*/
