// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-optimizing-designs.html
    \previouspage qtquick-production-quality-animation.html
    \nextpage studio-optimized-3d-scenes.html

    \title Optimizing Designs

    You can test your UIs on the target devices to make sure you get the best
    performance out of your animations. To solve performance problems, you
    typically need to optimize the graphical assets used in the UI, such as
    images, effects, or 3D scenes.

    How to optimize UIs for different target devices:

    \list
        \li Minimize image size
        \li Use transparency sparingly
    \endlist

    For more useful information for application developers, see
    \l {Performance Considerations And Suggestions}.

    For more information about optimizing 3D scenes, see
    \l{Creating Optimized 3D Scenes}.

    \section1 Minimizing Image Size

    Images are a vital part of any user interface. Unfortunately, they are also
    a big source of problems due to the time it takes to load them, the amount
    of memory they consume, and the way in which they are used.

    We recommend that you make image size as small as possible without
    negatively affecting image quality.

    For more information about how to use images efficiently in your UI, see
    \l{Images}.

    \section1 Avoid Transparency

    Opaque content is generally a lot faster to draw than transparent because
    the latter needs blending and the renderer can potentially optimize opaque
    content better.

    An image with one transparent pixel is treated as fully transparent, even
    though it is mostly opaque. The same is true for a \l {Border Image} with
    transparent edges.
*/
