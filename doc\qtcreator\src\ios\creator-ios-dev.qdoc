// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

    /*!
    \previouspage creator-adding-docker-devices.html
    \page creator-developing-ios.html
    \nextpage creator-developing-mcu.html

    \title Connecting iOS Devices

    You can connect iOS devices to your local machine with a USB cable to
    run applications built for them from \QC.

    To be able to use \QC on \macos, you must install Xcode, and therefore,
    you already have the tool chain for building applications for iOS. \QC
    automatically detects the tool chain and creates the necessary
    \l{glossary-buildandrun-kit}{kits} to build applications for and run them on
    configured iOS devices.

    You only need Qt libraries that are built for iOS. You can install them as
    part of Qt 5.2, or later.

    \section1 Configuring Devices

    The connections between \QC and an iOS device are protected by using a
    certificate that you receive from Apple when you
    \l{https://developer.apple.com/programs/enroll/}
    {enroll in the Apple Developer Program}. The certificate is copied to
    the device when you configure the device.

    The first time you connect the device to your local machine, you are asked to enable
    developer mode on the device. The next time you connect the device, \QC
    detects it automatically. To disable automatic connections to a device that
    you do not use for development, select \uicontrol Preferences >
    \uicontrol iOS, and deselect the \uicontrol {Ask about devices not in
    developer mode} check box.

    \note The process of configuring devices and the UI varies slightly
    depending on the Xcode version that you use. We recommend that you use the
    latest available Xcode version.

    To configure connections between \QC and an iOS device:

    \list 1

        \li Make sure that you have Xcode and Qt for iOS installed.

        \li Connect the device to your local machine with a USB cable.

        \li Start Xcode to configure the device.

            For example, in Xcode version 7.3.0, select \uicontrol Window >
            \uicontrol Device > \uicontrol + > \uicontrol {Add Device} to add
            the connected device.

        \li To specify build settings:

        \list 1

            \li Open a project for an application you want to develop for the
                device.

            \li Select \uicontrol Projects > \uicontrol {Build & Run} to select
                the kit for building applications for and running them on iOS.

                \image qtcreator-ios-add-kit.png "Build & Run Settings"

            \li In \uicontrol {iOS Settings}, select the development team to use
                for signing and provisioning applications. You must configure
                development teams and provisioning profiles in Xcode using an
                Apple developer account.

                \image qtcreator-build-settings-ios.png "iOS build settings"

            \li Select the \uicontrol {Automatically manage signing} check box
                to automatically select the provisioning profile and signing
                certificate on your local machine that matches the entitlements
                and the bundle identifier of the iOS device.

        \endlist

        \li Select \uicontrol Run to specify run settings.

            Usually, you can use the default settings.

    When you run the project, \QC uses Xcode to deploy the application to the
    device.

    Your signing certificate is used to sign application packages for deployment
    to the device.

    \endlist

    \note If you cannot deploy applications because a provisioning profile is
    missing, check that provisioning profiles are listed in Xcode by selecting
    \uicontrol Xcode > \uicontrol Preferences > \uicontrol Accounts. For more
    information about how to acquire and install a provisioning profile, see
    Apple documentation.

    \section1 Viewing Device Connection Status

    When you connect an iOS device to your local machine with USB, \QC
    automatically detects the device if you have configured it by using Xcode.
    To view information about the connected device, select \uicontrol Preferences >
    \uicontrol Devices.

    \image qtcreator-ios-device-configurations.png "Devices dialog"

    If the current device state is \uicontrol Connected, (the traffic light icon is
    orange), you need to configure the device using Xcode.

    \section1 Specifying Supported iOS Versions

    You can build applications for the latest iOS version and deploy them to
    previous versions. For the most part, this works automatically. However,
    you must take care when you manually set your own target version. If you set
    it to a value higher than what Qt requires and supply your own \c Info.plist
    file, you must add an \c LSMinimumSystemVersion entry to the \c Info.plist
    that matches the value of \l{CMake: CMAKE_OSX_DEPLOYMENT_TARGET}
    {CMAKE_OSX_DEPLOYMENT_TARGET} (when using CMake),
    \l QMAKE_IOS_DEPLOYMENT_TARGET (when using qmake), or
    \l{https://doc.qt.io/qbs/qml-qbsmodules-cpp.html#minimumIosVersion-prop}
    {cpp.minimumIosVersion} (when using Qbs) because iOS (and the App Store)
    will use the \c LSMinimumSystemVersion value as the authoritative one.

    If you specify a deployment target value lower than what Qt requires, your
    application will almost certainly crash somewhere in the Qt libraries when
    run on an older version than Qt supports. Therefore, make sure that the
    actual build system code reflects the minimum iOS version that is actually
    required.

    For more information, see \l{Expressing Supported iOS Versions}.

    \section1 Testing on Simulator

    If you do not have an iOS device or you do not want to create an account,
    you can test applications on
    \l{http://developer.apple.com/library/ios/documentation/IDEs/Conceptual/iOS_Simulator_Guide/Introduction/Introduction.html}
    {Simulator}, which is installed as part of Xcode. Each Xcode version
    simulates a predefined set of hardware devices and software versions.

    You can change the simulated hardware and software version in the run
    settings for the project. Select \uicontrol Projects > \uicontrol Run, and then select
    the device to simulate in the \uicontrol {Device type} field.

    \image qtcreator-ios-simulator-deploy.png

    The simulator is started automatically when you run the application.
    To start the simulator manually, select \uicontrol Preferences >
    \uicontrol Devices > \uicontrol iOS > \uicontrol Start.

    To take screenshots of the simulator, select \uicontrol Preferences >
    \uicontrol Devices > \uicontrol iOS > \uicontrol Screenshot. The screenshots
    are stored in the directory specified in the
    \uicontrol {Screenshot directory} field.

    \section2 Managing Simulators

    The available simulators are listed in \uicontrol Preferences >
    \uicontrol Devices > \uicontrol iOS.

    \image qtcreator-ios-preferences.png

    To create a new simulator instance:

    \list

        \li Select \uicontrol Create.

        \li In the \uicontrol {Device type} field, select the device type from
            a list of devices supported by the Xcode version set as current on
            your local machine.

        \li In the \uicontrol {OS version} field, select an OS version from a
            list of OS versions supported by the selected device and the current
            Xcode version.

    \endlist

    To rename the selected simulator, select \uicontrol Rename.

    To reset the contents and settings of the selected simulators, select
    \uicontrol Reset.

    To delete the selected simulator, select \uicontrol Delete.

    \section2 Checking Current Xcode Version

    To check the current Xcode version, enter the following command:

    \c {xcode-select --print-path}

    To change the version, enter the following command:

    \c {xcode-select --version}

*/
