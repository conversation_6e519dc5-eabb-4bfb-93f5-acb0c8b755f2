// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-tool-chains.html
    \page creator-debuggers.html
    \nextpage creator-build-settings.html

    \title Adding Debuggers

    The \QC debugger plugin acts as an interface between the \QC core and
    external native debuggers such as the GNU Symbolic Debugger (GDB),
    the Microsoft Console Debugger (CDB), a QML/JavaScript debugger, and the
    debugger of the low level virtual machine (LLVM) project, LLDB.

    The debugger plugin automatically selects a suitable native debugger for
    each \l{glossary-buildandrun-kit}{kit} from the ones found on your system.
    To override this choice, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Kits.

    For more information about setting up the debugger, see
    \l {Setting Up Debugger}. If you encounter problems, see
    \l {Troubleshooting Debugger}.

    To add debuggers:

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
            \uicontrol Debuggers > \uicontrol Add.

            \image qtcreator-preferences-kits-debuggers.webp "Debuggers tab in Kits preferences"

        \li In the \uicontrol Name field, give a descriptive name for the debugger.

        \li In the \uicontrol Path field, specify the path to the debugger binary:

            \list

                \li For CDB (Windows only), specify the path to the Windows
                    Console Debugger executable, \c cdb.exe.

                    The 32-bit version can only debug 32-bit executables, whereas
                    the 64-bit version can debug both 64-bit and 32-bit
                    executables. For more information, see
                    \l {Debugging Tools for Windows}.

                \li For GDB, specify the path to the GDB executable. The
                    executable must be built with Python scripting support
                    enabled.

                \li For LLDB (experimental), specify the path to the LLDB
                executable.

            \endlist

            \QC attempts to identify the type and version of the debugger and
            shows them in the \uicontrol Type and \uicontrol Version fields.
            In addition, \QC shows the ABI version that will be used on embedded
            devices in the \uicontrol ABIs field.

        \li In the \uicontrol {Working directory} field, specify the working
            directory of the application process. If the application runs
            locally, the working directory defaults to the build directory. If
            the application runs remotely on a device, the value depends on
            the shell or the device. Usually, you can leave this field empty.

    \endlist

    To remove the selected manually added debugger, select \uicontrol Remove.
    The debugger disappears from the list when you select \uicontrol Apply.
    Until then, you can cancel the deletion by clicking \uicontrol Restore.
*/
