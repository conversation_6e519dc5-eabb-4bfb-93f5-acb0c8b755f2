// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [settings valgrind]

    \section1 Specifying Valgrind Settings

    \QC integrates \l{Analyzing Code}{Valgrind code analysis tools} for
    detecting memory leaks and profiling function execution. You can configure
    the tools according to your needs.

    You can specify Valgrind settings either globally for all projects or separately for each
    project.

    To specify Valgrind settings for the current project:

    \list 1

        \li In the \uicontrol {Valgrind Settings} section, select \uicontrol Custom.

        \li Specify Valgrind settings for the project.

            \image qtcreator-analyzer-settings.png "Valgrind Settings"

        \li In \uicontrol {Valgrind executable}, specify the path to the
            Valgrind executable.

        \li In \uicontrol {Valgrind arguments}, specify additional arguments
            for Valgrind.

        \li In \uicontrol {Detect self-modifying code}, select whether to
            detect self-modifying code and where to detect it: only on stack,
            everywhere, or everywhere except in file-backend mappings.

    \endlist

    For more information about the CallGrind and MemCheck settings, see:

    \list

        \li \l{Selecting Profiling Options}

        \li \l{Selecting Options for Memory Analysis}

    \endlist

    Click \uicontrol {Restore Global} to revert to the global settings.

    To specify global Valgrind settings, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Analyzer.

//! [settings valgrind]
*/
