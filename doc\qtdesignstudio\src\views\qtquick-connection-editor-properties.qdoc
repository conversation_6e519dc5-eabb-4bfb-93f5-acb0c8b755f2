// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-dynamic-properties.html
    \previouspage quick-property-bindings.html
    \if defined(qtdesignstudio)
    \nextpage quick-states.html
    \else
    \nextpage quick-connections-backend.html
    \endif
    \sa {Specifying Component Properties}
    \title Specifying Custom Properties

    Each \l{Preset Components}{preset component }has a set of preset properties
    that you can specify values for. You can add custom properties that would
    not otherwise exist for a particular \l{Component Types}{component type}.
    You bind the properties to dynamic expressions to define global properties
    for a component that can be read by other components. For example, you can
    specify global properties for the root component that you can use in the
    child components.

    For example, to specify spacing between UI elements, you could define a
    margin for a component that does not have a margin property, and then use
    \l{Adding Bindings Between Properties}{bindings} to refer to the value of
    the margin property from other components.

    Similarly, you can add custom properties for your own components that are
    based on preset components.

    Any content that is data-driven should be exported as a public property
    (alias property) of the relevant component. For example, a speedometer
    should have an \e int or \e real property for speed to which the UI is
    bound.

    \section1 Adding Properties for a Component

    To add a custom property for a component:

    \list 1
        \li Go to the \uicontrol {Local Custom Properties} section in the
        \uicontrol Properties view.
        \li Select the \inlineimage icons/plus.png
            (\uicontrol Add) button to add a custom property for the currently
            selected component.
            \image add-local-custom-property.png
        \li Set the \uicontrol Name and \uicontrol Type for the property.
            \image add-new-property-dialog.png
    \endlist

    \section1 Binding a Property Value

    To bind the value of the property to that of another one or to data
    accessible in the application.

    \list 1
      \li In the \uicontrol Properties view, select
        \inlineimage icons/action-icon.png
        next to the property.
      \li Select \uicontrol {Set Binding}.
        \image qmldesigner-binding-editor.png "Binding Editor"
    \endlist

    For more information, see \l{Setting Bindings}.

    \section1 Supported Property Types

    The following table describes the supported property types:

    \table
    \header
        \li Type
        \li Description
    \row
        \li alias
        \li \l{Property Aliases}{Property alias} that holds a reference to
            another property
    \row
        \li bool
        \li Binary \c true or \c false value
    \row
        \li color
        \li Color value that can be specified by using an SVG color name, such
            as "red", "green", or "lightsteelblue", or a hexadecimal triplet or
            quad in the form "#RRGGBB" and "#AARRGGBB", respectively. For
            example, the color red corresponds to a triplet of "#FF0000" and
            a slightly transparent blue to a quad of "#800000FF".
            In addition, you can use the following Qt functions: \l{Qt::rgba()}
            {Qt.rgba()}, \l{Qt::hsva()}{Qt.hsva()}, \l{Qt::hsla()}{Qt.hsla()},
            \l{Qt::darker()}{Qt.darker()}, \l{Qt::lighter()}{Qt.lighter()}, and
            \l{Qt::tint()}{Qt.tint()}.
    \row
        \li int
        \li Whole integer number, such as 0, 10, or -20
    \row
        \li real
        \li Number with a decimal point
    \row
        \li string
        \li Free form text string
    \row
        \li TextureInput
        \li Specifies a texture exposed to the shaders of a CustomMaterial or Effect.
    \row
        \li url
        \li Resource locator, such as a file name. It can be either  absolute,
            (\c http://qt-project.org), or relative  (\c pics/logo.png). A
            relative URL is resolved relative to the URL of the parent
            component.
    \row
        \li variant
        \li Generic property type. For example, variant properties can store
            numbers, strings, objects, arrays, and functions.
    \row
        \li vector2d
        \li Refers to a value with x and y attributes.
    \row
        \li vector3d
        \li Refers to a value with x, y, and z attributes.
    \row
        \li vector4d
        \li Refers to a value with x, y, z, and w attributes.
    \endtable
*/
