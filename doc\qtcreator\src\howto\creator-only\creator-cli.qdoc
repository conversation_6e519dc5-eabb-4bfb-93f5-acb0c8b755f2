// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-conan.html
    \page creator-cli.html
    \nextpage creator-keyboard-shortcuts.html

    \title Using Command Line Options

    You can start \QC and specify some options from the command line. For
    example, you can open a file to any line and column.

    To specify command line options, enter the following command in the \QC
    installation or build directory:

    \c {qtcreator [option] [filename[:line_number[:column_number]]]}

    \note You can use either a colon (:) or a plus sign (+) as a separator
    between the filename and line number and the line number and the column
    number. You can also use a space between the separator and the line number.

    For example, on Windows:

    \list

        \li \c {C:\qtcreator\bin>qtcreator -help}

        \li \c {C:\qtcreator\bin>qtcreator C:\TextFinder\textfinder.cpp:100:2}

        \li \c {C:\qtcreator\bin>qtcreator C:\TextFinder\textfinder.cpp +100+2}

    \endlist

    On \macos:

    \list

        \li \c {Qt\ Creator.app/Contents/MacOS/Qt\ Creator -help}

    \endlist

    To open a project that is located in a particular folder, you can pass on
    the folder name as a command line argument. \QC looks for a session that
    matches the folder name and loads it. Or it looks for a project file in the
    folder and opens it. For example:

    \c {qtcreator .}

    \note To run a self-built \QC from the command line on Windows, make sure
    that the Qt installation directory is included in the PATH environment
    variable. You can enter the following command on the command line to add Qt
    to the path:

    \code
    set PATH=<Qt_installation_directory>\mingw\bin;c:<Qt_installation_directory>\bin;%PATH%
    \endcode

    The following table summarizes the available options:

    \table
        \header
            \li Option
            \li Description

        \row
            \li -help
            \li Display help on command line options.

        \row
            \li -version
            \li Display \QC version.

        \row
            \li -client
            \li Attempt to connect to an already running instance of \QC.

       \row
            \li -pid
            \li Attempt to connect to an already running instance of \QC with
                the specified process ID.

        \row
            \li -block
            \li Open files in editors in a running \QC instance and block the
                command line until the first editor is closed.

       \row
            \li -nocrashcheck
            \li Disable the startup check for a previously crashed \QC instance.

       \row
            \li -load <plugin>
            \li Enable the specified plugin and all plugins that it depends on.
                You can combine \c -load and \c -noload options and specify both
                options multiple times to enable and disable several plugins.
                The actions are executed in the specified order.

       \row
            \li -load all
            \li Enable all plugins.

        \row
            \li -noload <plugin>
            \li Disable the specified plugin and all plugins that depend on it.

        \row
            \li -noload all
            \li Disable all plugins.

        \row
            \li -profile
            \li Output profiling data about plugin startup and shutdown.

       \row
            \li -pluginpath <path>
            \li Add a path where \QC looks for plugins. To specify several
                paths, add the \c{-pluginpath} option for each path.

        \row
            \li -settingspath <path>
            \li Override the default path where user settings are stored.

        \row
            \li -installsettingspath <path>
            \li Override the default path from where user-independent settings are read
                (for example written by the installer).

        \row
            \li -temporarycleansettings, -tcs
            \li Use clean settings for debug or testing reasons. The settings
                will be deleted when \QC exits.

        \row
            \li -language <locale>
            \li Set the UI language.

       \row
            \li -test <plugin>[,testfunction[:testdata]] ...
            \li For \QC plugin developers: run the plugin's tests using a
                separate settings path by default.

       \row
            \li -test all
            \li For \QC plugin developers: run tests from all plugins.

       \row
            \li -notest <plugin>
            \li For \QC plugin developers: exclude all of the plugin's
                tests from the test run.

       \row
            \li -scenario <scenarioname>
            \li For \QC plugin developers: run the specified scenario.

        \row
            \li -color <color>
            \li Core plugin: override the selected UI color.

       \row
            \li -presentationMode
            \li Core plugin: display keyboard shortcuts as popups when you
                press them. Mostly useful when presenting \QC to someone else.

        \row
            \li -theme <default | dark>
            \li Core plugin: apply a dark color theme to \QC, without using
                stylesheets.

        \row
            \li -notour
            \li Welcome plugin: skip the UI tour on startup.

        \row
            \li -debug <pid>
            \li Debugger plugin: attach to the process with the given process
                ID.

        \row
            \li -debug <executable>[,kit=<kit>]
            \li Debugger plugin: launch and debug the executable with the name
                \c{executable}.
                A \c{kit} can be specified by ID or name to point to non-default
                debuggers and sysroots.

        \row
            \li -debug [executable,]core=<corefile>[,kit=<kit>]
            \li Debugger plugin: load the core file named \c{corefile}.
                The parameter \c{executable} specifies the executable that
                produced the core file.
                If this parameter is omitted, \QC will attempt to reconstruct
                it from the core file itself. This will fail for paths with more
                than about 80 characters.
                In such cases the \c{executable} parameter is mandatory.
                A \c{kit} can be specified by ID or name to point to non-default
                debuggers and sysroots.

        \row
            \li -debug <executable>,server=<server:port>[,kit=<kit>]
            \li Debugger plugin: attach to a debug server running on the port
                \c{port} on the server \c{server}. The parameter \c{executable}
                specifies a local copy of the executable the remote debug server
                is manipulating.
                A \c{kit} can be specified by ID or name to point to non-default
                debuggers and sysroots.

        \row
            \li -wincrashevent <event-handle:pid>
            \li Debugger plugin: attach to crashed processes by using the
                specified event handle and process ID.

        \row
            \li -git-show <git commit hash>
            \li Git plugin: show the specified commit hash.

        \row
            \li -customwizard-verbose
            \li ProjectExplorer plugin: display additional information when
                loading custom wizards. For more information about custom
                wizards, see \l{Adding New Custom Wizards}

        \row
            \li -ensure-kit-for-binary <path to binary>
            \li ProjectExplorer plugin: create a kit with a toolchain
                corresponding to the given binary's architecture.

        \row
            \li -lastsession
            \li ProjectExplorer plugin: load the last session when \QC starts.
                Open the projects and files that were open when you last exited
                \QC. For more information about managing sessions, see
                \l{Managing Sessions}.

        \row
            \li <session>
            \li ProjectExplorer plugin: load the given session when \QC starts.
                Open the projects and files that were open when you last exited
                \QC. For more information about managing sessions, see
                \l{Managing Sessions}.

    \endtable

    \section1 Using Custom Styles

    \QC is a \l{QApplication}{Qt application}, and therefore, it accepts the
    command line options that all Qt applications accept. For example, you can
    use the \c {-style} and \c {-stylesheet} options to apply custom styles and
    \l{Qt Style Sheets}{stylesheets}. The styling is only applied during the
    current session.

    Exercise caution when applying styles, as overriding the existing styling
    may make some items difficult to see. Also, setting a stylesheet may affect
    the \l{Specifying Text Editor Settings}{text editor color scheme} and the
    styling of the integrated \QD.

    You can also switch to a dark theme to customize the appearance of widgets,
    colors, and icons without using stylesheets.

*/
