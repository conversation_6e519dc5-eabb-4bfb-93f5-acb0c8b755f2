// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-property-bindings.html
    \previouspage quick-signals.html
    \nextpage quick-dynamic-properties.html

    \title Adding Bindings Between Properties

    To dynamically change the behavior of a component, you can create a
    \e binding between the properties of two components. To create a property
    binding, a property is assigned a JavaScript expression that evaluates to
    the desired value. Behind the scenes, the variables in the binding
    expression are monitored. When a change is detected, the binding
    expression is re-evaluated and the new result is applied to the property.

    At its simplest, a binding may be a reference to another property.
    For example, the height of a component can be bound to the height of its
    parent, so that when the parent height changes, the component height is
    adjusted automatically.

    For more information about the use of property bindings, see
    \l{Property Binding}.

    You can create bindings between components in \uicontrol Bindings.

    \image qmldesigner-bindings.png

    To bind a property of a component to the property of another component:

    \list 1
        \li Go to the \uicontrol Binding tab in the \l Connections view.
        \li Select the \inlineimage icons/plus.png
            (\uicontrol Add) button to add a binding for the currently selected
            component. The component ID is displayed in the \uicontrol Item
            column.
          \li Double-click the value in the \uicontrol Property column to select
            the property to bind to a source property.
        \li Double-click the value in the \uicontrol {Source Item} column to
            select the component whose property you want to use to determine the
            behavior of the target component.
        \li Double-click the value in the \uicontrol {Source Property} column
            to select the property to bind the target property to.
    \endlist

    Right-click a binding and select \uicontrol {Open Binding Editor} in
    the context menu to specify the binding as a JavaScript expression in
    \uicontrol {Binding Editor}. For more information, see \l{Setting Bindings}.

    \image qmldesigner-binding-editor.png "Binding Editor"

    For examples of creating property bindings, see:

    \list
        \li \l{Using States to Change Component Property Values}
        \if defined(qtdesignstudio)
        \li \l{Exporting Properties}
        \else
        \li \l{Moving the Bubble} in \l{Creating a Mobile Application}
        \endif
    \endlist

    For more information, watch the following video:

    \youtube UfvA04CIXv0

    \include creator-logical-operators.qdocinc logical operators

*/
