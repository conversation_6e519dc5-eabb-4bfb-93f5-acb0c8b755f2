// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-qml-modules-with-plugins.html
    \page creator-using-qt-designer.html
    \nextpage adding-plugins.html

    \title Developing Widget Based Applications

    \QC automatically opens all .ui files in the integrated \QD, in \uicontrol Design
    mode.

    \image qtcreator-formedit.png

    For more information about \QD, see the \l{Qt Designer Manual}.

    Generally, the integrated \QD contains the same functions as the standalone
    \QD. The following sections describe the differences.

    \section1 Code Editor Integration

    To switch between forms (\uicontrol Design mode) and code (\uicontrol Edit mode),
    press \key Shift+F4.

    You can use \QC to create stub implementations of slot functions. In the
    \uicontrol Design mode, right-click a widget to open a context menu, and then
    select \uicontrol {Go to Slot}. Select a signal in the list to go to an existing
    slot function or to create a new slot function.

    \section1 Managing Image Resources

    In standalone \QD, image resources are created using the built-in
    \uicontrol {Resource Editor}. In \QC, .ui files are usually part of a project,
    which may contain several resource files (.qrc). They are created and
    maintained by using the \QC Resource Editor. The \QD \uicontrol {Resource Editor}
    is de-activated and the image resources are displayed in the \QD
    \uicontrol {Resource Browser}.

    To open resource files from the code editor, select
    \uicontrol {Follow Symbol Under Cursor} in the context menu
    or press \key F2 when the cursor is over a string literal.

    \section1 Specifying Settings for Qt Designer

    You can drag and drop the views in \QD to new positions on the screen.

    To specify settings for \QD:

    \list

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Designer.

        \li Specify embedded device profiles, that determine style, font, and
            screen resolution, for example, in \uicontrol{Embedded Design}.

        \li Specify settings for the grid and previewing forms in \uicontrol Forms.

        \li Specify an additional folder for saving templates in \uicontrol{Template
            Paths}.

    \endlist

    To preview the settings, select \uicontrol Tools > \uicontrol{Form Editor} >
    \uicontrol Preview, or press \key Alt+Shift+R.

    \section1 Previewing Forms Using Device Profiles

    A \e {device profile} is a set of configuration files that describe a mobile
    device. It includes a border image that surrounds the form and depicts a
    mobile device with its buttons.

    To preview your form using device profiles, select \uicontrol Tools >
    \uicontrol {Form Editor} > \uicontrol {Preview In}, and then select a
    device profile.

    \section2 Adding Device Profiles

    To add device profiles:

    \list 1

        \li \uicontrol Edit > \uicontrol Preferences > \uicontrol Designer.
            \image qtdesigner-embedded-design.png "Qt Designer Embedded Design preferences"
        \li In \uicontrol {Embedded Design}, select \inlineimage icons/plus.png
            to open the \uicontrol {Add Profile} dialog.
            \image qtdesigner-add-profile.png "Add Profile dialog"
        \li In \uicontrol Name, enter a name for the device. The name is
            displayed as an option in \uicontrol {Preview In}.
        \li In \uicontrol Family, select the font to use.
        \li In \uicontrol {Point Size}, select the font size.
        \li In \uicontrol Style, select one of the predefined styles.
        \li In \uicontrol {Device DPI}, select one of the predefined DPI
            values or \uicontrol {User defined} to specify a custom value.
        \li Select \uicontrol OK to add the device profile to the list.
    \endlist

    To import device profiles from .qdp files, select \uicontrol Open. To save
    them as .qdp files, select \uicontrol Save.

    \section1 Adding Widgets

    You can use Qt APIs to create plugins that extend Qt applications. This
    enables you to add your own widgets to \QD. For more information, see
    \l{Adding Qt Designer Plugins}.
*/
