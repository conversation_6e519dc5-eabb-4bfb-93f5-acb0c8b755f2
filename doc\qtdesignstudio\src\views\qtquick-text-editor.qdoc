// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-text-editor.html
    \previouspage qtquick-curve-editor.html
    \if defined(qtdesignstudio)
    \nextpage creator-projects-view.html
    \else
    \nextpage quick-uis.html
    \endif

    \title Code

    In the \uicontrol {Code} view, you can view and modify the code in a
    \l{UI Files}{UI file} (.ui.qml) or component file (.qml) that is generated
    when you create components in the \l {2D} or \l {3D} view and specify
    their properties in \l Properties.

    \image qtquick-text-editor.png "The Code view"

    You can also view and modify other types of text files.

    To open the search dialog in the \uicontrol {Code} view, go to
    \uicontrol Edit > \uicontrol {Find/Replace} > \uicontrol {Find/Replace}.
    You can also select \key Ctrl + \key {F}.

    You can use a subset of the functions available in the
    \l{Writing Code}{Edit mode}:

    \list
        \li \l{Semantic Highlighting}
        \li \l{Checking Code Syntax}
        \li \l{Completing Code}
        \li \l{Indenting Text or Code}
        \li \l{Using Qt Quick Toolbars}
    \endlist
*/
