// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-navigator.html
    \previouspage quick-assets.html
    \nextpage qtquick-properties-view.html

    \title Navigator

    The \uicontrol Navigator view displays the components in the current
    component file and their relationships. \l{glossary-component}{Components}
    (1) are listed in a tree structure, below their parent (2). You can preview
    components by hovering the mouse over them (3).

    \image qmldesigner-navigator.png "Navigator with component preview"

    You can select components in the \uicontrol Navigator view to edit their
    properties in the \l Properties view. Components can access the
    properties of their parent component. To select components in the
    \l {2D} view, right-click a component, and select another
    component in the \uicontrol Selection submenu.

    Typically, child components are located within the parent component in
    the \uicontrol {2D} view. However, they do not necessarily have to
    fit inside the parent component. For example, you might want to make a
    mouse area larger than the rectangle or image beneath it.

    \image qmldesigner-element-size.png "Mouse area for a button"

    When you copy a component, all its child components are also copied. When
    you remove a component, the child components are also removed.

    Select context menu commands to apply commands to components. The
    availability of the commands depends on the component type. For example,
    you can change the source of an Image component by selecting
    \uicontrol {Change Source URL} in the context menu.

    \section1 Summary of Navigator Buttons

    The following table lists the \uicontrol Navigator buttons:

    \table
    \header
        \li Icon
        \li Tooltip
        \li Read More
    \row
        \li \inlineimage icons/arrowleft.png
        \li Moves the component one level up in the component tree, so that
            it becomes the last sibling of its current parent.
        \li \l{Arranging Components}
    \row
        \li \inlineimage icons/arrowright.png
        \li Moves the component one level down in the component tree, so that it
            becomes the child of its last sibling.
        \li \l{Arranging Components}
    \row
        \li \inlineimage icons/navigator-arrowdown.png
        \li Moves the component down within its parent.
        \li \l{Arranging Components}
    \row
        \li \inlineimage icons/navigator-arrowup.png
        \li Moves the component up within its parent.
        \li \l{Arranging Components}
    \row
        \li \inlineimage icons/filtericon.png
        \li Shows and hides invisible components in \uicontrol Navigator.
        \li \l{Showing and Hiding Components}
    \row
        \li \inlineimage icons/alias.png
        \li Adds a property alias that you can use from outside of the
            component.
        \li \l{Adding Property Aliases}
    \row
        \li \inlineimage icons/visibilityon.png
        \li Shows and hides components in the \uicontrol {2D} view.
        \li \l{Showing and Hiding Components}
    \row
        \li \inlineimage icons/lockon.png
        \li Locks components in all views.
        \li \l{Locking Components}
    \endtable

    \section1 Showing and Hiding Components

    To show and hide components in the \uicontrol {2D} view when focusing on
    specific parts of the application, click \inlineimage icons/visibilityon.png
    in \uicontrol Navigator.

    To change the visibility of a component in the application code, select the
    \uicontrol Visibility check box in the \uicontrol Properties view or select
    \uicontrol Edit > \uicontrol Visibility in the context menu.

    You can also set the \uicontrol Opacity field to 0 in \uicontrol Properties
    to hide components in the UI that you want to apply animation to.

    As all properties, visibility and opacity are inherited from the parent
    component. To hide or show child components, edit the properties of the
    parent component.

    To hide invisible components in \uicontrol Navigator, click
    \inlineimage icons/filtericon.png
    (\uicontrol {Filter Tree}) and select
    \uicontrol {Show Only Visible Components}.

    \section1 Locking Components

    When designing complex applications, it is easy to accidentally modify
    the properties of a component in one of the \QDS views in ways that lead to
    surprising results. For example, the \uicontrol {2D} view can become
    crowded and other components can get in the way when you are trying to
    select or transform a particular component, so that you end up transforming
    more components than you wanted to.

    To lock components that you are not currently editing and their children,
    click \inlineimage icons/lockon.png
    in \uicontrol Navigator. Locked components cannot be handled in any \QDS
    views. You can unlock the components when you want to edit them again.

    \image qtquick-designer-navigator-lock.gif "Locking components in Navigator"

    You cannot select locked components in the \uicontrol {2D} view or
    the \l {3D} view nor access their properties in
    \uicontrol Properties.

    If you attempt to \l{Working with States}{remove a state} that changes the
    properties of a locked component, you are prompted to confirm the removal.

    If you have \l{Editing Animation Curves}{added easing curves} to keyframe
    animations, you can lock and unlock them in the \l {Curves}
    view. If you lock the components that contain the easing curves, the lock
    status is synchronized between \uicontrol Navigator and
    \uicontrol {Curves}.

    \section1 Arranging Components

    You can view the order of components in a component file in \uicontrol Navigator
    and the \l {Code} view. The order of components in the file also
    determines the order in which they are drawn in the \uicontrol {2D} view.
    By default, components that are located at the top of the file are listed at
    the bottom of the \uicontrol Navigator tree and behind overlapping
    components in the \uicontrol {2D} view. To list the components in the order
    in which they appear in the file, as some other tools do, click
    \inlineimage icons/filtericon.png
    (\uicontrol {Filter Tree}), and select \uicontrol {Reverse Component Order}.

    To move a component to the top or bottom of the tree within its parent,
    right-click it in the \uicontrol Navigator or \uicontrol {2D} view
    and select \uicontrol Arrange > \uicontrol {Bring to Front} or
    \uicontrol {Send to Back}. To move a component up or down, select
    \uicontrol {Bring Forward} or \uicontrol {Send Backward}.

    To reverse the order of the selected components in the \uicontrol Navigator and
    \uicontrol {Code} views, select \uicontrol Arrange > \uicontrol Reverse.

    \image qtquick-designer-navigator-arrange.gif "Reversing component order"

    You can also drag-and-drop the component to another position in the tree or
    use the arrow buttons to move the component in the tree. You can use the
    left and right arrow buttons to change the parent of the component.

    \image qmldesigner-navigator-arrows.png "Navigator buttons"

    When you drag-and-drop instances of components to the \uicontrol {2D} view,
    the new component is added as a child of the component
    beneath it. When you move the components, it is not possible to determine
    whether you want to adjust their position or attach them to a new parent component.
    Therefore, the parent component is not automatically changed. To change the
    parent of the component, press down the \key Shift key before you drag-and-drop
    the component into a new position. The topmost component under the cursor becomes the
    new parent of the component.

    \section1 Adding Property Aliases

    A \e {property alias} is a property that you can use from outside the
    component. When you view the code in the \l {Code} view, a property alias
    declaration looks like an ordinary property definition, except that it
    requires the \e alias keyword instead of a \l{Supported Property Types}
    {property type}, and the right-hand-side of the property declaration must
    be a valid alias reference:

    \badcode
    property alias <name>: <alias reference>
    \endcode

    For example, the following alias refers to a button component instance
    within an item component instance:

    \badcode
    property alias button: item.button
    \endcode

    A valid alias reference:

    \list
        \li Can only point to a component instance or property within
            the component where the property alias is declared.
        \li Cannot contain arbitrary JavaScript expressions.
        \li Cannot point to components of another type than the component
            where the property alias is declared.
        \li Must be defined when the alias is first declared.
        \li Cannot point to attached properties.
        \li Cannot point to properties of nested component instances
            below the third level.
    \endlist

    You can use the \inlineimage icons/alias.png
    (\uicontrol Export) button in \uicontrol Navigator to export
    a component as a property alias with a valid alias reference.

    \image qmldesigner-export-item.png

    You can then use the property alias in other components to
    \l{Working with Connections}{create connections} to this component.

    \section1 Moving Within Components

    The files that specify components (\c ui.qml, \c .qml) can contain
    instances of other components specified in separate files. You can
    open the file that specifies a component in different ways from
    different views:

    \list
        \li In the \uicontrol {2D} or \uicontrol Navigator view,
            right-click an instance of a component and then select
            \uicontrol {Go into Component} in the context menu or
            press \key F2.
        \li In \uicontrol Properties, select \uicontrol {Edit Base Component}.
    \endlist

    The component hierarchy is displayed as a bread crumb path, where you can
    click the component names to open the respective files. This enables you
    to easily navigate back to the top level when you are done editing the
    component.

    \image qmldesigner-breadcrumbs.png "Component hierarchy"

    \include qtquick-component-context-menu.qdocinc context-menu
*/
