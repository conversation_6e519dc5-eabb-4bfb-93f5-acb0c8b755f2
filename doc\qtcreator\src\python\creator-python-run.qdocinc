// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [running python]
    \section1 Running Python Projects

    You can execute Qt for Python applications directly from \QC. If you
    used the \l{Using Project Wizards}{new project wizard}
    to create the application project, the \c main.py file is automatically
    executed when you select the \uicontrol Run button.

    You can specify another file to execute in the
    \l{Specifying Run Settings for Python Projects}{run settings}
    of the project.

//! [running python]


//! [run settings python]

    \section1 Specifying Run Settings for Python Projects

    You can specify settings for running Qt for Python applications:

    \image qtcreator-python-run-settings.png

    \list
        \li In the \uicontrol Interpreter field, specify the path to the
            Python executable.
        \li Select the \uicontrol {Buffered output} check box to buffer the
            output. This improves output performance, but causes delays in
            output.
        \li In the \uicontrol Script field, you can see the path to the
            main file of the project that will be run.
        \li In the \uicontrol {Command line arguments} field, specify
            command line arguments to be passed to the executable.
    \endlist

    If you want to run some other Python file than \c main.py, create a custom
    executable run configuration:

    \image qtcreator-python-run-settings-custom-executable.png

    \list 1
        \li Select \uicontrol Add > \uicontrol {Custom Executable}.
        \li In the \uicontrol Executable field, specify the path to the
            Python executable.
        \li In the \uicontrol {Command line arguments} field, select
            the Python file to run.
    \endlist

//! [run settings python]
*/
