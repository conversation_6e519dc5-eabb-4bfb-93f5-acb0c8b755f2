<?xml version="1.0" encoding="UTF-8"?>
<qmt>
 <project>
  <uid>{f7c67e69-57c5-4eb1-9303-f12bb2ae0a23}</uid>
  <root-package>
   <instance>
    <MPackage>
     <base-MObject>
      <MObject>
       <base-MElement>
        <MElement>
         <uid>{9a360a3d-a5ff-4fa0-9a86-540d2cbdfa28}</uid>
        </MElement>
       </base-MElement>
       <name>actionmanager</name>
       <children>
        <handles>
         <handles>
          <qlist>
           <item>
            <handle>
             <uid>{6f6e0572-b8cc-4d97-95c5-edae83d0a3d2}</uid>
             <target>
              <instance type="MCanvasDiagram">
               <MCanvasDiagram>
                <base-MDiagram>
                 <MDiagram>
                  <base-MObject>
                   <MObject>
                    <base-MElement>
                     <MElement>
                      <uid>{6f6e0572-b8cc-4d97-95c5-edae83d0a3d2}</uid>
                     </MElement>
                    </base-MElement>
                    <name>actionmanager</name>
                   </MObject>
                  </base-MObject>
                  <elements>
                   <qlist>
                    <item>
                     <instance type="DItem">
                      <DItem>
                       <base-DObject>
                        <DObject>
                         <base-DElement>
                          <DElement>
                           <uid>{0a2e2d40-ad38-4b91-b9c1-6af2e3d2cf95}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{0fa7cf39-5531-4f41-9e2f-d890ef2a248d}</object>
                         <name>ActionManager</name>
                         <pos>x:300;y:90</pos>
                         <rect>x:-50;y:-15;w:100;h:30</rect>
                         <visual-role>0</visual-role>
                         <visual-role2>1</visual-role2>
                        </DObject>
                       </base-DObject>
                       <shape-editable>false</shape-editable>
                      </DItem>
                     </instance>
                    </item>
                    <item>
                     <instance type="DItem">
                      <DItem>
                       <base-DObject>
                        <DObject>
                         <base-DElement>
                          <DElement>
                           <uid>{d918082a-dd32-48b8-bdcb-21ade3c1d067}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{a3950ef4-9fd9-466f-a575-dc75790749c3}</object>
                         <name>QAction</name>
                         <pos>x:125;y:340</pos>
                         <rect>x:-30;y:-15;w:60;h:30</rect>
                         <visual-role>0</visual-role>
                        </DObject>
                       </base-DObject>
                       <shape-editable>false</shape-editable>
                      </DItem>
                     </instance>
                    </item>
                    <item>
                     <instance type="DBoundary">
                      <DBoundary>
                       <base-DElement>
                        <DElement>
                         <uid>{d197d1e0-23ef-4041-845f-27c65fe11b70}</uid>
                        </DElement>
                       </base-DElement>
                       <text>Context</text>
                       <pos>x:125;y:335</pos>
                       <rect>x:-50;y:-40;w:100;h:80</rect>
                      </DBoundary>
                     </instance>
                    </item>
                    <item>
                     <instance type="DBoundary">
                      <DBoundary>
                       <base-DElement>
                        <DElement>
                         <uid>{613641ea-8fec-4b44-890b-8c6ea42eb9eb}</uid>
                        </DElement>
                       </base-DElement>
                       <text>Plugin</text>
                       <pos>x:125;y:330</pos>
                       <rect>x:-65;y:-70;w:130;h:140</rect>
                      </DBoundary>
                     </instance>
                    </item>
                    <item>
                     <instance type="DItem">
                      <DItem>
                       <base-DObject>
                        <DObject>
                         <base-DElement>
                          <DElement>
                           <uid>{c9758c28-e527-4f1b-b911-9c4f7571571c}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{ac06f227-7618-422e-a87e-5913950c91d7}</object>
                         <name>QAction</name>
                         <pos>x:275;y:340</pos>
                         <rect>x:-30;y:-15;w:60;h:30</rect>
                         <visual-role>0</visual-role>
                        </DObject>
                       </base-DObject>
                       <shape-editable>false</shape-editable>
                      </DItem>
                     </instance>
                    </item>
                    <item>
                     <instance type="DItem">
                      <DItem>
                       <base-DObject>
                        <DObject>
                         <base-DElement>
                          <DElement>
                           <uid>{d87082a8-6b2b-404f-abd3-9082585166f4}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{0cc14318-53df-4193-8aeb-0d29d4cd1561}</object>
                         <name>QAction</name>
                         <pos>x:405;y:340</pos>
                         <rect>x:-30;y:-15;w:60;h:30</rect>
                         <visual-role>0</visual-role>
                        </DObject>
                       </base-DObject>
                       <shape-editable>false</shape-editable>
                      </DItem>
                     </instance>
                    </item>
                    <item>
                     <instance type="DBoundary">
                      <DBoundary>
                       <base-DElement>
                        <DElement>
                         <uid>{168c5e3d-54bc-412c-9cb1-9b5481100f2b}</uid>
                        </DElement>
                       </base-DElement>
                       <text>Context</text>
                       <pos>x:275;y:335</pos>
                       <rect>x:-50;y:-40;w:100;h:80</rect>
                      </DBoundary>
                     </instance>
                    </item>
                    <item>
                     <instance type="DBoundary">
                      <DBoundary>
                       <base-DElement>
                        <DElement>
                         <uid>{3447fe60-8ac5-4fe9-b518-80daba7e960a}</uid>
                        </DElement>
                       </base-DElement>
                       <text>Context</text>
                       <pos>x:405;y:335</pos>
                       <rect>x:-50;y:-40;w:100;h:80</rect>
                      </DBoundary>
                     </instance>
                    </item>
                    <item>
                     <instance type="DBoundary">
                      <DBoundary>
                       <base-DElement>
                        <DElement>
                         <uid>{fcf93e87-8ce6-43ea-ab5e-7ca651d1f429}</uid>
                        </DElement>
                       </base-DElement>
                       <text>Plugin</text>
                       <pos>x:340;y:335</pos>
                       <rect>x:-135;y:-70;w:270;h:140</rect>
                      </DBoundary>
                     </instance>
                    </item>
                    <item>
                     <instance type="DClass">
                      <DClass>
                       <base-DObject>
                        <DObject>
                         <base-DElement>
                          <DElement>
                           <uid>{8df1e5cf-5e76-453a-ba69-0fcf31f46d4e}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{68e762c3-f1cc-479b-9700-bdb01f208340}</object>
                         <name>Command</name>
                         <pos>x:195;y:180</pos>
                         <rect>x:-40;y:-35;w:80;h:70</rect>
                         <visual-role>0</visual-role>
                        </DObject>
                       </base-DObject>
                       <show-all-members>true</show-all-members>
                      </DClass>
                     </instance>
                    </item>
                    <item>
                     <instance type="DDependency">
                      <DDependency>
                       <base-DRelation>
                        <DRelation>
                         <base-DElement>
                          <DElement>
                           <uid>{a4401c84-57c3-4f34-a88d-f35e28239944}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{42259fbc-5917-4caf-836e-0828a032d908}</object>
                         <a>{0a2e2d40-ad38-4b91-b9c1-6af2e3d2cf95}</a>
                         <b>{8df1e5cf-5e76-453a-ba69-0fcf31f46d4e}</b>
                        </DRelation>
                       </base-DRelation>
                      </DDependency>
                     </instance>
                    </item>
                    <item>
                     <instance type="DClass">
                      <DClass>
                       <base-DObject>
                        <DObject>
                         <base-DElement>
                          <DElement>
                           <uid>{7ff85acd-0d85-4fe2-8cc9-d0511f96443c}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{39911558-001e-4d0b-9827-d1d372049e86}</object>
                         <name>Command</name>
                         <pos>x:405;y:180</pos>
                         <rect>x:-40;y:-35;w:80;h:70</rect>
                         <visual-role>0</visual-role>
                        </DObject>
                       </base-DObject>
                       <show-all-members>true</show-all-members>
                      </DClass>
                     </instance>
                    </item>
                    <item>
                     <instance type="DDependency">
                      <DDependency>
                       <base-DRelation>
                        <DRelation>
                         <base-DElement>
                          <DElement>
                           <uid>{815d855b-8814-4ac7-8b75-17c3adb45344}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{fa3943f6-6bf7-4040-acae-d04c880fb329}</object>
                         <a>{0a2e2d40-ad38-4b91-b9c1-6af2e3d2cf95}</a>
                         <b>{7ff85acd-0d85-4fe2-8cc9-d0511f96443c}</b>
                        </DRelation>
                       </base-DRelation>
                      </DDependency>
                     </instance>
                    </item>
                    <item>
                     <instance type="DDependency">
                      <DDependency>
                       <base-DRelation>
                        <DRelation>
                         <base-DElement>
                          <DElement>
                           <uid>{f4567b84-e58c-4212-8d0b-c7040c00639a}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{8d18c30e-51c8-4fa2-ad22-ad6962273120}</object>
                         <a>{8df1e5cf-5e76-453a-ba69-0fcf31f46d4e}</a>
                         <b>{d918082a-dd32-48b8-bdcb-21ade3c1d067}</b>
                        </DRelation>
                       </base-DRelation>
                      </DDependency>
                     </instance>
                    </item>
                    <item>
                     <instance type="DDependency">
                      <DDependency>
                       <base-DRelation>
                        <DRelation>
                         <base-DElement>
                          <DElement>
                           <uid>{df1469a1-91d8-4c05-8b19-fddffbfc05d6}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{8d11e47d-b745-4bf4-b7d2-eb042c307ddf}</object>
                         <a>{8df1e5cf-5e76-453a-ba69-0fcf31f46d4e}</a>
                         <b>{c9758c28-e527-4f1b-b911-9c4f7571571c}</b>
                        </DRelation>
                       </base-DRelation>
                      </DDependency>
                     </instance>
                    </item>
                    <item>
                     <instance type="DDependency">
                      <DDependency>
                       <base-DRelation>
                        <DRelation>
                         <base-DElement>
                          <DElement>
                           <uid>{b6288087-3aca-4e28-988c-c11ca3def425}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{9f1e7858-c06c-4c7c-97d6-209d3c96360f}</object>
                         <a>{7ff85acd-0d85-4fe2-8cc9-d0511f96443c}</a>
                         <b>{d87082a8-6b2b-404f-abd3-9082585166f4}</b>
                        </DRelation>
                       </base-DRelation>
                      </DDependency>
                     </instance>
                    </item>
                    <item>
                     <instance type="DItem">
                      <DItem>
                       <base-DObject>
                        <DObject>
                         <base-DElement>
                          <DElement>
                           <uid>{ee71f328-354b-4993-8a63-8f4605285440}</uid>
                          </DElement>
                         </base-DElement>
                         <object>{158de17f-753a-4b00-8ddf-2f4432871d07}</object>
                         <name>Menu</name>
                         <pos>x:100;y:190</pos>
                         <rect>x:-25;y:-15;w:50;h:30</rect>
                         <visual-role>0</visual-role>
                         <visual-role2>1</visual-role2>
                        </DObject>
                       </base-DObject>
                       <shape-editable>false</shape-editable>
                      </DItem>
                     </instance>
                    </item>
                    <item>
                     <instance type="DBoundary">
                      <DBoundary>
                       <base-DElement>
                        <DElement>
                         <uid>{5b8fc43d-fb36-4523-ac54-4262dc0affce}</uid>
                        </DElement>
                       </base-DElement>
                       <pos>x:270;y:190</pos>
                       <rect>x:-205;y:-20;w:410;h:40</rect>
                      </DBoundary>
                     </instance>
                    </item>
                   </qlist>
                  </elements>
                  <last-modified>1586427331500</last-modified>
                  <toolbarid>General</toolbarid>
                 </MDiagram>
                </base-MDiagram>
               </MCanvasDiagram>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{0fa7cf39-5531-4f41-9e2f-d890ef2a248d}</uid>
             <target>
              <instance type="MItem">
               <MItem>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{0fa7cf39-5531-4f41-9e2f-d890ef2a248d}</uid>
                   </MElement>
                  </base-MElement>
                  <name>ActionManager</name>
                  <relations>
                   <handles>
                    <handles>
                     <qlist>
                      <item>
                       <handle>
                        <uid>{d5111c81-0745-4724-8d01-8ac36994e31c}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{d5111c81-0745-4724-8d01-8ac36994e31c}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{0fa7cf39-5531-4f41-9e2f-d890ef2a248d}</a>
                             <b>{c2d3f5b7-87c5-4f67-9911-96a4a251ddd5}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                      <item>
                       <handle>
                        <uid>{6308a511-1fd1-472d-bdc2-0bf173c6850c}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{6308a511-1fd1-472d-bdc2-0bf173c6850c}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{0fa7cf39-5531-4f41-9e2f-d890ef2a248d}</a>
                             <b>{d6694b35-bb04-4830-9713-99470b22b7d7}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                      <item>
                       <handle>
                        <uid>{42259fbc-5917-4caf-836e-0828a032d908}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{42259fbc-5917-4caf-836e-0828a032d908}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{0fa7cf39-5531-4f41-9e2f-d890ef2a248d}</a>
                             <b>{68e762c3-f1cc-479b-9700-bdb01f208340}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                      <item>
                       <handle>
                        <uid>{fa3943f6-6bf7-4040-acae-d04c880fb329}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{fa3943f6-6bf7-4040-acae-d04c880fb329}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{0fa7cf39-5531-4f41-9e2f-d890ef2a248d}</a>
                             <b>{39911558-001e-4d0b-9827-d1d372049e86}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                     </qlist>
                    </handles>
                   </handles>
                  </relations>
                 </MObject>
                </base-MObject>
               </MItem>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{c2d3f5b7-87c5-4f67-9911-96a4a251ddd5}</uid>
             <target>
              <instance type="MItem">
               <MItem>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{c2d3f5b7-87c5-4f67-9911-96a4a251ddd5}</uid>
                   </MElement>
                  </base-MElement>
                  <name>Command</name>
                  <relations>
                   <handles>
                    <handles>
                     <qlist>
                      <item>
                       <handle>
                        <uid>{e5b7a324-70ab-46b9-8d36-9f2ad6c0db57}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{e5b7a324-70ab-46b9-8d36-9f2ad6c0db57}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{c2d3f5b7-87c5-4f67-9911-96a4a251ddd5}</a>
                             <b>{a3950ef4-9fd9-466f-a575-dc75790749c3}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                      <item>
                       <handle>
                        <uid>{703dd2bc-f99c-41b7-8f90-a7292645feb8}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{703dd2bc-f99c-41b7-8f90-a7292645feb8}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{c2d3f5b7-87c5-4f67-9911-96a4a251ddd5}</a>
                             <b>{ac06f227-7618-422e-a87e-5913950c91d7}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                     </qlist>
                    </handles>
                   </handles>
                  </relations>
                 </MObject>
                </base-MObject>
               </MItem>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{d6694b35-bb04-4830-9713-99470b22b7d7}</uid>
             <target>
              <instance type="MItem">
               <MItem>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{d6694b35-bb04-4830-9713-99470b22b7d7}</uid>
                   </MElement>
                  </base-MElement>
                  <name>Command</name>
                  <relations>
                   <handles>
                    <handles>
                     <qlist>
                      <item>
                       <handle>
                        <uid>{4dc1c5bd-a124-4961-ad65-476e66cb6efe}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{4dc1c5bd-a124-4961-ad65-476e66cb6efe}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{d6694b35-bb04-4830-9713-99470b22b7d7}</a>
                             <b>{0cc14318-53df-4193-8aeb-0d29d4cd1561}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                     </qlist>
                    </handles>
                   </handles>
                  </relations>
                 </MObject>
                </base-MObject>
               </MItem>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{a3950ef4-9fd9-466f-a575-dc75790749c3}</uid>
             <target>
              <instance type="MItem">
               <MItem>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{a3950ef4-9fd9-466f-a575-dc75790749c3}</uid>
                   </MElement>
                  </base-MElement>
                  <name>QAction</name>
                 </MObject>
                </base-MObject>
               </MItem>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{ac06f227-7618-422e-a87e-5913950c91d7}</uid>
             <target>
              <instance type="MItem">
               <MItem>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{ac06f227-7618-422e-a87e-5913950c91d7}</uid>
                   </MElement>
                  </base-MElement>
                  <name>QAction</name>
                 </MObject>
                </base-MObject>
               </MItem>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{0cc14318-53df-4193-8aeb-0d29d4cd1561}</uid>
             <target>
              <instance type="MItem">
               <MItem>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{0cc14318-53df-4193-8aeb-0d29d4cd1561}</uid>
                   </MElement>
                  </base-MElement>
                  <name>QAction</name>
                 </MObject>
                </base-MObject>
               </MItem>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{68e762c3-f1cc-479b-9700-bdb01f208340}</uid>
             <target>
              <instance type="MClass">
               <MClass>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{68e762c3-f1cc-479b-9700-bdb01f208340}</uid>
                   </MElement>
                  </base-MElement>
                  <name>Command</name>
                  <relations>
                   <handles>
                    <handles>
                     <qlist>
                      <item>
                       <handle>
                        <uid>{8d18c30e-51c8-4fa2-ad22-ad6962273120}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{8d18c30e-51c8-4fa2-ad22-ad6962273120}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{68e762c3-f1cc-479b-9700-bdb01f208340}</a>
                             <b>{a3950ef4-9fd9-466f-a575-dc75790749c3}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                      <item>
                       <handle>
                        <uid>{8d11e47d-b745-4bf4-b7d2-eb042c307ddf}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{8d11e47d-b745-4bf4-b7d2-eb042c307ddf}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{68e762c3-f1cc-479b-9700-bdb01f208340}</a>
                             <b>{ac06f227-7618-422e-a87e-5913950c91d7}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                     </qlist>
                    </handles>
                   </handles>
                  </relations>
                 </MObject>
                </base-MObject>
                <members>
                 <qlist>
                  <item>
                   <MClassMember>
                    <uid>{5d8da03d-d556-4eed-ae0d-6d306453496e}</uid>
                    <type>1</type>
                    <declaration>QAction</declaration>
                   </MClassMember>
                  </item>
                 </qlist>
                </members>
               </MClass>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{39911558-001e-4d0b-9827-d1d372049e86}</uid>
             <target>
              <instance type="MClass">
               <MClass>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{39911558-001e-4d0b-9827-d1d372049e86}</uid>
                   </MElement>
                  </base-MElement>
                  <name>Command</name>
                  <relations>
                   <handles>
                    <handles>
                     <qlist>
                      <item>
                       <handle>
                        <uid>{9f1e7858-c06c-4c7c-97d6-209d3c96360f}</uid>
                        <target>
                         <instance type="MDependency">
                          <MDependency>
                           <base-MRelation>
                            <MRelation>
                             <base-MElement>
                              <MElement>
                               <uid>{9f1e7858-c06c-4c7c-97d6-209d3c96360f}</uid>
                              </MElement>
                             </base-MElement>
                             <a>{39911558-001e-4d0b-9827-d1d372049e86}</a>
                             <b>{0cc14318-53df-4193-8aeb-0d29d4cd1561}</b>
                            </MRelation>
                           </base-MRelation>
                          </MDependency>
                         </instance>
                        </target>
                       </handle>
                      </item>
                     </qlist>
                    </handles>
                   </handles>
                  </relations>
                 </MObject>
                </base-MObject>
                <members>
                 <qlist>
                  <item>
                   <MClassMember>
                    <uid>{2d428a77-4751-4fa7-bbb7-7f58995da129}</uid>
                    <type>1</type>
                    <declaration>QAction</declaration>
                   </MClassMember>
                  </item>
                 </qlist>
                </members>
               </MClass>
              </instance>
             </target>
            </handle>
           </item>
           <item>
            <handle>
             <uid>{158de17f-753a-4b00-8ddf-2f4432871d07}</uid>
             <target>
              <instance type="MItem">
               <MItem>
                <base-MObject>
                 <MObject>
                  <base-MElement>
                   <MElement>
                    <uid>{158de17f-753a-4b00-8ddf-2f4432871d07}</uid>
                   </MElement>
                  </base-MElement>
                  <name>Menu</name>
                 </MObject>
                </base-MObject>
               </MItem>
              </instance>
             </target>
            </handle>
           </item>
          </qlist>
         </handles>
        </handles>
       </children>
      </MObject>
     </base-MObject>
    </MPackage>
   </instance>
  </root-package>
 </project>
</qmt>
