// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-visual-editor.html
    \page quick-projects.html
    \nextpage creator-qtquickdesigner-plugin.html

    \title Creating Qt Quick Projects

    \image qtcreator-new-qt-quick-project.png "New Project dialog"

    The following table lists the wizard templates for creating a new
    Qt Quick project from scratch.

    \table
        \header
            \li Category
            \li Wizard Template
            \li Purpose
        \row
            \li Application (Qt)
            \li Qt Quick Application
            \li Creates a Qt Quick 2 application project that can contain both
                QML and C++ code. You can build the application and deploy it
                to desktop, embedded, and mobile target platforms.
        \row
            \li Application (Qt for Python)
            \li Qt for Python - Qt Quick Application
            \li Creates a Python project that contains an empty Qt Quick
                Application.
        \row
            \li Other Project
            \li Qt Quick UI Prototype
            \li Creates a Qt Quick UI project with a single QML file that
                contains the main view. You can preview Qt Quick 2 UI projects
                in the QML Scene preview tool. You do not need to build them
                because they do not contain any C++ code.

                This project type is compatible with \QDS. However, use this
                template only if you are prototyping. You cannot create
                a full application by using this template.

                Qt Quick UI projects cannot be deployed to embedded or mobile
                target platforms. For those platforms, create a Qt Quick
                application instead.
        \row
            \li Library
            \li Qt Quick 2 Extension Plugin
            \li Creates C++ plugins that make it possible to offer extensions
                that can be loaded dynamically into Qt Quick 2 applications.
    \endtable

    \note The SDK for a particular target platform might install additional
    templates for that platform. For example, the QNX templates are installed
    as part of the QNX SDK.

    \QC creates the necessary boilerplate files. Some of the files are
    specific to a particular target platform.

    \section1 Creating Qt Quick Applications

    \list 1

        \li Select \uicontrol File > \uicontrol {New Project} >
            \uicontrol {Application (Qt)} > \uicontrol {Qt Quick Application}
            > \uicontrol Choose.

        \li In the \uicontrol {Project Location} dialog, \uicontrol Name field,
            enter a name for the project. Keep in mind that you cannot easily
            change the project name later.

        \li In the \uicontrol {Create in} field, enter the path for the project
            files. Select the \uicontrol {Use as default project location} check
            box to create new projects in this folder by default. You can move
            project folders later without problems.

        \li Select \uicontrol Next (or \uicontrol Continue on \macos) to open
            the \uicontrol {Define Build System} dialog.

        \li In the \uicontrol {Build system} field, select the build system to
            use for building and running the project: \l qmake,
            \l {Setting Up CMake}{CMake}, or \l {Setting Up Qbs}{Qbs}.

        \li Select \uicontrol Next to open the
            \uicontrol {Define Project Details} dialog.

        \li Select the Qt version to develop with in the
            \uicontrol {Minimum required Qt version} field.
            The Qt version determines the Qt Quick imports
            that are used in the QML files.

        \li Select the \uicontrol {Use Qt Virtual Keyboard} check box to add
            support for \l{Qt Virtual Keyboard} to the application.

            \note If you have not installed the Qt Virtual Keyboard module when
            you installed Qt, an error message will appear when you try to open
            \e main.qml for editing. You can use the \l {Installing Qt}
            {Qt Maintenance Tool} to install Qt Virtual Keyboard.

        \li Select \uicontrol Next to open the \uicontrol {Translation File}
            dialog.

        \li In the \uicontrol Language field, select a language that you plan
            to \l {Using Qt Linguist}{translate} the application to. You can
            add other languages later by editing the project file.

        \li In the \uicontrol {Translation file} field, you can edit the
            name for the translation source file that will be generated
            for the selected language.

        \li Select \uicontrol Next to open the \uicontrol {Kit Selection}
            dialog.

        \li Select \l{glossary-buildandrun-kit}{kits} for the platforms that
            you want to build the application for.

            \note Kits are listed if they have been specified in \uicontrol Edit
            > \uicontrol Preferences > \uicontrol Kits (on Windows and Linux)
            or in \uicontrol {\QC} > \uicontrol Preferences >
            \uicontrol Kits (on \macos).
            For more information, see \l {Adding Kits}.

        \li Select \uicontrol Next to open the \uicontrol {Project Management}
            dialog.

        \li Review the project settings, and select \uicontrol Finish
            (on Windows and Linux) or \uicontrol Done (on \macos) to
            create the project.

    \endlist

    \QC creates a QML file, \e main.qml, that you can modify in the
    \uicontrol Edit mode.

    \include creator-python-project.qdocinc python qml project wizards

    \section1 Creating Qt Quick UI Projects

    Qt Quick UI Prototype projects are useful for testing or prototyping user
    interfaces,
    or for setting up a separate project just for QML editing, for example. You
    cannot use them for application development because they do not contain:

    \list
        \li C++ code
        \li Resource files (.qrc)
        \li Code needed for deploying applications to \l{glossary-device}
            {devices}
    \endlist

    For more information about how to turn Qt Quick UI Prototype projects into
    Qt Quick Application projects, see
    \l{Converting UI Projects to Applications}.

    To create a Qt Quick UI Prototype project:

    \list 1

        \li Select \uicontrol File > \uicontrol {New Project} >
            \uicontrol {Other Project} > \uicontrol {Qt Quick UI Prototype}.

        \li Select \uicontrol Choose to open the \uicontrol {Project Location}
            dialog.

        \li In the \uicontrol Name field, enter a name for the application.

        \li In the \uicontrol {Create in} field, enter the path for the project
            files. Select the \uicontrol {Use as default project location} check
            box to create new projects in this folder by default.

        \li Select \uicontrol Next (or \uicontrol Continue on \macos) to open
            the \uicontrol {Define Project Details} dialog.

        \li In the \uicontrol {Minimum required Qt version} field, select the Qt
            version to develop with. The Qt version determines the Qt Quick
            imports that are used in the QML files.

            You can add imports later to combine Qt Quick basic types with
            Qt Quick Controls, Qt Quick Dialogs, and Qt Quick Layouts (available
            since Qt 5.1).

        \li Select the \uicontrol {Use Qt Virtual Keyboard} check box to add
            support for \l{Qt Virtual Keyboard} to the application.

            \note If you have not installed the Qt Virtual Keyboard module when
            you installed Qt, an error message will appear when you try to open
            \e main.qml.

        \li Select \uicontrol Next to open the \uicontrol {Kit Selection}
            dialog.

        \li Select \l{glossary-buildandrun-kit}{kits} for the platforms that
            you want to build the application for.

            \note Kits are listed if they have been specified in \uicontrol Edit
            > \uicontrol Preferences > \uicontrol Kits (on Windows and Linux)
            or in \uicontrol {\QC} > \uicontrol Preferences >
            \uicontrol Kits (on \macos).
            For more information, see \l {Adding Kits}.

        \li Select \uicontrol Next to open the \uicontrol {Project Management}
            dialog.

        \li Review the project settings, and select \uicontrol Finish
            (on Windows and Linux) or \uicontrol Done (on \macos) to
            create the project.

    \endlist

    \QC creates the following files:

    \list

        \li .qmlproject project file defines that all QML, JavaScript, and image
            files in the project folder belong to the project. Therefore, you do
            not need to individually list all the files in the project.

        \li .qml file defines a UI item, such as a component or the
            whole application UI.

        \li ui.qml file defines a form for the application UI. This file is
            created if you selected the \uicontrol {With .ui.qml file} check
            box.

    \endlist

    To use JavaScript and image files in the application, copy them to the
    project folder.

*/
