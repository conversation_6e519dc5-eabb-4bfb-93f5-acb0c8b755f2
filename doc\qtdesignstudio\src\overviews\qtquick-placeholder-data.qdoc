// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-placeholder-data.html
    \previouspage studio-simulation-overview.html
    \nextpage studio-javascript.html

    \title Loading Placeholder Data

    \QDS supports views, models, and delegates, so that when you add
    a Grid View, List View, or Path View component, the ListModel and
    the delegate component are added automatically.

    However, the missing context of the application presents a challenge.
    Specific models defined in C++ are the most obvious case. Often,
    the context is missing simple properties, which are either defined in C++,
    or in other component files. A typical example is a component that uses the
    properties of its parent, such as \c parent.width.

    \section1 Using Dummy Models

    If you open a file in the \l {2D} view that references a C++ model, you see
    nothing in it. If the data in the model is fetched from the
    internet, you have no control over it. To get reliable data, \e {dummy data}
    was introduced.

    For example, the following code snippet describes the file example.qml that
    contains a ListView that in turn specifies a C++ model:

    \qml
        ListView {
            model: dataModel
            delegate: ContactDelegate {
                name: name
            }
        }
    \endqml

    Create a directory named \e dummydata in the root directory of the project,
    so that it is not deployed to the device. In the \c dummydata directory,
    create a file (.qml) that has the same name as the value of \c model:

    \code
        qml/exampleapp/example.qml
        dummydata/dataModel.qml
    \endcode

    Then create the dataModel.qml file that contains the dummy data:

    \qml
        import QtQuick 2.0

        ListModel {
             ListElement {
                 name: "Ariane"
             }
             ListElement {
                 name: "Bella"
             }
             ListElement {
                 name: "Corinna"
             }
        }
    \endqml

    \section1 Creating Dummy Context

    The following example presents a common pattern:

    \qml
        Item {
            width: parent.width
            height: parent.height
        }
    \endqml

    This works nicely for applications but the \uicontrol {2D} view displays a
    zero-sized component. A parent for the opened file does not exist because
    the context is missing. To get around the missing context, the idea of a
    \e {dummy context} is introduced. If you place a file with the same name as
    the application (here, example.qml) in the \c {dummydata/context} directory,
    you can fake a parent context:

    \qml
        import QtQuick 2.0
        import QmlDesigner 1.0

        DummyContextObject {
            parent: Item {
                width: 640
                height: 300
            }
        }
    \endqml
*/
