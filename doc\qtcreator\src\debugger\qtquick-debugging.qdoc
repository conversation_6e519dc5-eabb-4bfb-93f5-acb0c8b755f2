// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-debugging-qml.html
    \if defined(qtdesignstudio)
    \previouspage studio-debugging.html
    \nextpage creator-qml-debugging-example.html
    \else
    \previouspage creator-debugging-helpers.html
    \nextpage creator-debugging-example.html
    \endif

    \title Debugging Qt Quick Projects

    \if defined(qtcreator)
    \note You need Qt 5.0 or later to debug Qt Quick projects.
    \endif

    For an example of how to debug Qt Quick Projects, see
    \l{Debugging a Qt Quick Example Application}.

    \if defined(qtdesignstudio)
    \note In this section, you are using advanced menu items. These are not
    visible by default. To toggle the visibility of advanced menu items, see
    \l{Customizing the Menu}.
    \endif

    \section1 Setting Up QML Debugging

    \if defined(qtcreator)
    The process of setting up debugging for Qt Quick projects depends on the
    \l{Creating Qt Quick Projects}{type of the project}: Qt Quick UI or Qt Quick
    Application, and the Qt version used.
    \endif

    To debug Qt Quick UI projects, select the \uicontrol {Enable QML} check box in the
    \uicontrol {Debugger Settings} in \uicontrol Projects mode \uicontrol {Run Settings}.

    \if defined(qtcreator)
    To debug Qt Quick Applications:

        \list 1

        \li If you use qmake as the build system, make sure that
            debugging is enabled in the \uicontrol {Build Settings},
            \uicontrol {QML debugging and profiling} field, either
            explicitly for the project or globally by default.

            \image qtcreator-projectpane.png "qmake general build settings pane"

            \note Debugging requires opening a socket at a TCP port,
            which presents a security risk. Anyone on the Internet could connect
            to the application that you are debugging and execute any JavaScript
            functions. Therefore, you must make sure that the port is properly
            protected by a firewall.

        \li In the \uicontrol {Run Settings}, \uicontrol {Debugger Settings} section, select
            the \uicontrol {Enable QML} check box to enable
            QML debugging.

        \li Select \uicontrol Build > \uicontrol {Rebuild Project} to clean and
            rebuild the project.

        \li To debug applications on \l{glossary-device}{devices}, check that
            Qt 5.0, or later, libraries are installed on the device and
            \l{Running on Multiple Platforms}{select the corresponding kit for the device}
            before you start debugging.

    \endlist

    \note The \c qmltooling plugins that are required for debugging are
    automatically installed during \QC and Qt installation. Do not delete
    them if you plan to debug QML applications.

    \section1 Mixed C++/QML Debugging

    To debug both the C++ and QML parts of your application at the same time,
    select the \uicontrol {Enable C++} and \uicontrol {Enable QML} checkboxes for both
    languages in the \uicontrol {Debugger Settings} section in the project
    \uicontrol{Run Settings}.
    \endif

    \image qtquick-debugging-settings.png

    \section1 Starting QML Debugging

    To start the application, choose \uicontrol Debug > \uicontrol {Start Debugging}
    > \uicontrol {Start Debugging of Startup Project} or press \key F5. Once the
    application starts running, it behaves and performs as usual. You can then
    perform the following tasks:

    \list

        \li Debug JavaScript functions

        \li Execute JavaScript expressions to get information about the state of
            the application

        \li Inspect QML properties and JavaScript variables and change them
            temporarily at runtime

    \endlist

    To debug already running applications:

    \list 1

        \if defined(qtcreator)
        \li Build the application by using the appropriate configuration
            parameters (if you build the application with \QC, it automatically
            uses the correct configuration):
            \list
                \li When using CMake, the
                    \l{CMake: target_compile_definitions command}
                    {target_compile_definitions} command is defined
                    in the CMakeLists.txt file:
                    \c {target_compile_definitions(myapp PRIVATE QT_QML_DEBUG)}

                    Where \e myapp is the application to debug.
                \li When using qmake, the following value is defined for the
                    \l CONFIG property in the .pro file:
                    \c {CONFIG += qml_debug}
            \endlist
        \endif

        \li Start the application with the following arguments:

            \c {-qmljsdebugger=port:<port>[,host:<ip address>][,block]}

            Where \c port (mandatory) specifies the debugging port,
            \c {ip address} (optional) specifies the IP address of the host
            where the application is running, and \c block (optional) prevents
            the application from running until the debug client connects to the
            server. This enables debugging from the start.

            \note Setting breakpoints is only possible if the application is started with block mode.

        \li Select \uicontrol Debug > \uicontrol {Start Debugging} >
            \uicontrol {Attach to QML Port}.

            Choose the kit configured for the device where the application to
            be debugged is running. The port number to use is displayed in the
            standard output when the application starts.

    \endlist

    \section1 Debugging JavaScript Functions

    You can use the \QC \uicontrol Debug mode to inspect the state of your
    application while debugging. You can interact with the debugger by:

    \list

        \li \l{Setting Breakpoints}{Setting breakpoints}

        \li \l{Viewing Call Stack Trace}{Viewing call stack trace}

        \li \l{Local Variables and Function Parameters}
              {Viewing local variables and function parameters}

        \li \l{Evaluating Expressions}

    \endlist

    \if defined(qtdesignstudio)
    \include creator-debugger-common.qdocinc debugger-breakpoints
    \include creator-debugger-common.qdocinc debugger-call-stack-trace
    \include creator-debugger-common.qdocinc debugger-locals
    \include creator-debugger-common.qdocinc debugger-expressions
    \endif

    \section1 Inspecting Items

    While the application is running, you can use the \uicontrol {Locals}
    view to explore the QML item structure.

    \image qml-observer-view.png "QML item tree"

    To keep the application visible while you interact with the debugger, select
    \uicontrol Debug > \uicontrol {Show Application on Top}.

    You can view a QML item in the \uicontrol {Locals} view in the following
    ways:

    \list

        \li Expand the item in the object tree.

        \li Select the item in the code editor.

        \li Select \uicontrol Debug > \uicontrol Select to activate selection
            mode and then click an item in the running application.

    \endlist

    To change property values temporarily, without editing the source,
    double-click them and enter the new values. You can view the results in the
    running application.

    \section1 Inspecting User Interfaces

    When you debug complex applications, you can jump to the position in code
    where an item is defined.

    In the selection mode, you can click items in the running
    application to jump to their definitions in the code. The properties of the
    selected item are displayed in the \uicontrol {Locals} view.

    \if defined(qtcreator)
    The \uicontrol Select tool will be enabled either if your application is
    using Qt 5.7 or later, or if your application is using an earlier version
    of Qt and is based on the \c QQuickView class.
    \endif

    You can also view the item hierarchy in the running application:

    Double-click an item in the running application to cycle through the item
    stack at the cursor position.

    To switch out of the selection mode, toggle the \uicontrol Select menu item.

    To move the application running in \QQV to the front, select
    \uicontrol Debug > \uicontrol {Show Application on Top}.

    \section1 Executing JavaScript Expressions

    When the application is interrupted by a breakpoint, you can use the
    \uicontrol {QML Debugger Console} to execute JavaScript expressions in the
    current context. To open it, choose \uicontrol View > \uicontrol Output >
    \uicontrol {QML Debugger Console}.

    \image qml-script-console.png "QML Debugger Console"

    You can change property values temporarily, without editing the source, and
    view the results in the running application. You can change the property
    values permanently in
    \if defined(qtcreator)
    code.
    \else
    the \l Properties view.
    \endif

    \section1 Applying QML Changes at Runtime

    When you change property values in the \uicontrol {QML Debugger Console} or
    in the \uicontrol Locals or \uicontrol Expression view, they are immediately
    updated in the running application, but not in the source code.
*/
