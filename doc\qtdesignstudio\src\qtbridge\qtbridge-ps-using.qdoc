// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage qtbridge-ps-setup.html
    \page qtbridge-ps-using.html
    \nextpage xdqtbridge.html

    \title Using \QBPS

    \section1 Organizing Assets

    To get the best results when you use \QBPS to export designs from Adobe
    Photoshop for importing them to \QDS, follow these guidelines when working
    with Photoshop:

    \list
        \li Arrange your art into \e artboards and organize it into groups and
            layers that are imported into \QDS as separate files, depending
            on the choices you make when exporting designs.
        \li Use the Type tool to make sure that all of your text labels
            are exported to \l Text items.
        \li Store all assets in vector format to be able to easily rescale them
            for different screen sizes and resolutions.
    \endlist

    To use the fonts that you use in Photoshop also in \QDS, you need to load
    them to \QDS. \QDS deploys them to devices when you preview the UI. For more
    information, see \l{Using Custom Fonts}.

    \note You can export only files that are saved in the Photoshop file format,
    such as \e {.psd} and \e {.psb}. For all other document formats, \QDS
    displays the following error message: \e {Document is not supported.}

    \section2 Items You Can Export

    You can export the following parts of your design using \QBPS:
    \list
        \li Layers
        \li Text layers
        \li Groups
        \li Artboards
    \endlist

    You cannot export the following parts of your design:
    \list
        \li Smart Objects
        \li Frames
    \endlist

    \section2 Using Artboards

    The relationships between the groups and layers on an artboard are preserved
    when you export designs from Adobe Photoshop and import them into \QDS.

    When you use \QBPS to export your designs, you will determine how you want
    each group or layer exported: as a \e component or \e child. A component
    will be imported as a single \l {UI Files}{UI file} that can contain other
    assets. A child will be imported as a single image file that you can use
    within UI files.

    If you plan to use pieces of your design as separate images in the UI,
    group them on an artboard as separate layers. You can then export the group
    as a component and each layer within it as a child. The children are
    imported to \QDS as separate PNG files that you can use as image sources.

    To use the contents of an artboard as a single image in the UI, you can
    merge the groups and layers when you export them. During import, the
    contents are flattened into one PNG file. The merge is done in a way that
    enables you to change the groups and layers in Photoshop or Adobe
    Illustrator and then export and import the artboard again. This is an easy
    way to create a reference image of how the final UI should look like, for
    example.

    Place different parts of the UI, such as menus and pop-ups, on separate
    artboards to be able to export them as components or children and to
    import them as code and PNG files that you can drag and drop to
    the \l {2D} view in \QDS Design mode while creating a UI.

    \QDS offers predefined sets of UI controls that you can modify according
    to your needs. You can export your own controls as components, Qt Quick
    Controls, or Qt Quick Studio Components. The position and dimensions of your control
    are preserved.

    However, if you want your UI controls, such as check boxes, to look exactly
    like they do in Photoshop, you have to create the control in an Artboard and
    use the artboard in the layers where the control instance is to be created.
    \QB imports the control as a custom component that you can program in
    \QDS.

    \section1 Exporting Assets

    Each artboard is exported automatically as a component, which means that it
    will be imported as a separate file that contains all the artwork on the
    artboard, except layers that are set to be skipped or exported as child items.
    You determine how each group or layer in an artboard is exported: as a component
    or a child item. In addition, you can merge the groups and layers of an artboard
    into the parent as one item or skip layers completely.

    By default, layers are exported as follows:

    \list
        \li First level group layers are exported as child items of the
            artboard.
        \li Second level group layers are merged to their parent.
        \li Asset layers are exported as merged.
        \li Text layers are always exported as child items.
        \li Images are exported in PNG or JPG format, depending on your
            selection.
    \endlist

    \QBPS automatically proposes identifiers (IDs) for all groups and layers.
    The IDs will be used as filenames in \QDS. You can change the IDs, so that
    you can easily find them in \QDS. Just keep in mind that the IDs must be
    unique and that they must follow some naming conventions.

    You can export assets using the default settings and make all the changes
    later in \QDS. If you are familiar with the \l{QML Syntax Basics}
    {QML syntax}, you can modify the settings to tailor the generated code to
    a certain degree. For example, you can specify the component or
    \l {Shapes}{Qt Quick Studio Component} to use for a artboard or layer.
    If you have drawn an arc that you mean to animate, you can export it as
    an \l Arc Studio component to avoid having to replace the arc image with
    an Arc component in \QDS. Or you could export a button as a
    Qt Quick Controls \l Button type.

    You can specify effects, such as a blur effect, to use for a group or layer.

    The QML types supported by \QDS are listed in the \l {Components}
    view in the Design mode of \QDS.

    You can also specify values for the properties of the component or create
    \l{Property Aliases}{property aliases} to fetch the values from other
    properties.

    \image qt-bridge.png

    \section2 Specifying Settings for Exporting Assets

    To export your design using \QBPS:

    \list 1
        \li \QBPS automatically proposes identifiers for all groups and layers
            that you can change in the \uicontrol {ID} field. The IDs must
            be unique, they must begin with a lower-case letter or an
            underscore, and they can only contain letters, numbers, and
            underscore characters. For more information, see
            \l {The id Attribute}.
        \li In the \uicontrol {Export As} field, select the export type for the
            group or layer:
            \list
                \li \uicontrol Component exports the selected artboard, group,
                    or layer with metadata. The exported data can be used later
                    to import the component as a separate UI file that contains
                    all the artwork in it, except layers that are set to be
                    skipped or exported as child items.
                \li \uicontrol Child exports each asset of the selected group
                    or layer as a separate PNG file, with references
                    to the images in the component file.
                \li \uicontrol Merged merges the selected groups and layers into
                    the parent as one item.
                \li \uicontrol Skipped completely skips the selected layer.
            \endlist
        \li In the \uicontrol {As Artboard} field, select an artboard
            to reuse. For example, you can use an artboard to define a
            component, such as a button, and reuse it in other artboards.
        \li In the \uicontrol {Component} field, specify the component
            or Studio component to morph this layer into. The component that is
            generated during import will be of this type. For example, if you
            drew a rectangle, you can export it as a \l {Studio Rectangle}{Rectangle}
            Studio component.
            You can provide the import statement of the module where the
            component type is defined in the \uicontrol {Imports} field.
            \note The implicit properties except position and size are not
            applied when the \uicontrol {Component} is defined. For example, all text
            properties will be ignored if \uicontrol {Component} is defined
            for a text layer, but explicit properties defined in the \uicontrol
            {Properties} field will be applied.
        \li Select the \uicontrol {Render Text} check box to render the text
            layer as an asset. The layer will be exported as an asset and the
            text data will not be exported. This allows the text layer to be
            merged to parent artboard or group as well.
        \li In the \uicontrol {Add Imports} field, enter additional
            import statements to have them added to the generated UI file.
            For example, to use Qt Quick Controls 2.3, you need the import
            statement \c {QtQuick.Controls 2.3} and to use Qt Studio
            Components 1.0, you need the import statement
            \c {QtQuick.Studio.Components 1.0}. You can also import a module
            as an alias.
        \li In the \uicontrol {Properties} field, specify properties for
            the component. You can add and modify properties in \QDS.
        \li Select the \uicontrol {Clip Contents} check box to enable clipping
            in the type generated from the layer. The generated type will clip
            its own painting, as well as the painting of its children, to its
            bounding rectangle.
        \li Select the \uicontrol {Create Alias} check box to export the item
            generated from this layer as an alias in the parent component.
        \li Select \uicontrol {Cascade properties} to apply the current set of
            properties to all the children of the selected layer.
        \li In the \uicontrol Annotations field, specify annotation for the
            component. See \l {Annotating Designs}.
        \li Select \uicontrol Export to copy your assets to the export path you
            specified.
        \li When the exporting is done, select \uicontrol OK.
    \endlist

    All the assets and metadata are copied to the directory you specified. This
    might take a little while depending on the complexity of your project.

    You can now create a project in \QDS and import the assets to it, as
    described in \l {Creating Projects} and \l{Importing Designs}.

    \note
    Exporting your design using \QBPS can be slow for documents with large number
    of layers. Closing Photoshop's Info, Layers, and Properties windows can improve
    the time it takes to export.



    \section1 Customizing IDs

    \QBPS enables customizing auto generated IDs. In the \uicontrol {ID Prefix}
    field, specify an ID prefix that will be prepended to all auto generated IDs.
    In the \uicontrol {ID Suffix} field, specify an ID suffix that will be
    appended to all auto generated IDs.

    \image qt-bridge-qml-id-settings.png

    \section1 Cloning Documents

    \QBPS enables creating a clone of the current document. The clone workflow
    allows the user to filter out certain kind of layers and groups. In the \QBPS
    \uicontrol Settings dialog, select \uicontrol {Clone} to start cloning the document.

    \image qt-bridge-clone.png

    \section2 Clone Options
    The following exclusion options can be selected to exclude certain kind of layers and
    groups in the cloned document:
    \list
        \li \uicontrol Invisible: Select this option to exclude the invisible layers
            and groups.
        \li \uicontrol Skipped: Select this option to exclude the layers where the
            \uicontrol {Export As} field value is set to \uicontrol Skipped.
        \li \uicontrol {Empty Groups}: Select this option to exclude any empty groups.
            This also applies to groups which will eventually become empty because of
            the other selected exclusion options.
    \endlist

    \section1 Sanitizing Documents

    \QBPS enables removing all \QBPS related metadata from the active
    document. In the \QBPS \uicontrol Settings dialog, select
    \uicontrol {Sanitize Document} to sanitize the active document.
    Once the sanitization is successfully done, the document will
    contain no \QBPS related metadata and the \QBPS layer settings will fall
    back to the \l{Exporting Assets}{default} values.

    \note The sanitization is done in memory and the document must be saved to
    keep the sanitized state.

    \image qt-bridge-sanitize.png

    \section1 Extending \QBPS
    You can change the default behavior of \QBPS with the help of a JSX script. One can write
    specific functions in the script that are called by \QBPS with useful parameters.

    \section2 Overridable JSX Functions
    You can define the following functions in the override JSX.
    \list
        \li preExport(document)
            This function is called before the document is exported. The parameter \a document is
            the PSD Document instance. This function can be used to make temporary changes in
            the document before export.
        \li postExport(document)
            This function is called after the document is exported. The parameter \a document is the
            PSD Document instance. You can undo the temporary changes done in the function
            \e preExport(...).
        \li customDefaultQmlId(name, instance)
            The function is called for setting the default ID of the layer. The returned value
            is used for the ID. Return \c false to use the auto generated ID instead.
            The parameter \a name is the auto generated ID by the plugin and \a instance is the
            PSD layer instance.
    \endlist

    \note Please refer to \l {https://www.adobe.com/devnet/photoshop/scripting.html}
    {Adobe Photoshop CC Javascript} scripting guide to understand the object model and \e Document
    and \e Layer instances.

    \image qt-bridge-override.png

    In the \QBPS \uicontrol Settings dialog, select \uicontrol {Override JSX Script} to set the
    override JSX script.

    \section1 Importing Metadata & Assets

    \QBPS can import metadata generated from other tools and generate a Photoshop document. A
    conventional workflow would be to generate metadata and assets by \l {Exporting Components} {exporting}
    a QML project from \QDS and use \QBPS to generate a Photoshop document.

    Imported text and the assets are organized into Artboards, layers, and groups.

    Select the \uicontrol Import button to launch the \uicontrol Import panel. Alternatively, \QB
    import can be launched from \uicontrol Window > \uicontrol Extensions.

    \image qt-bridge-import.png

    Create a new PSD document and launch the \uicontrol Import dialog. Open the metadata file to
    import and select \uicontrol Import.

    \note The import process removes all the existing layers in the selected PSD document.

    \image qt-bridge-import-warning.png

    The following guidelines are followed to generate the Photoshop document:
    \list
        \li An Artboard is created for each component exported and defined in the metadata file.
        \li An image layer is created for an item with an asset.
        \li A solid fill layer is created for an item without an asset.
        \li A paragraph text layer is created for Text items.
    \endlist

    All metadata specified in the metadata file is assigned to the generated layers.

    An important concept of \e {Virtual parent} is applied to translate the QML DOM to Photoshop
    DOM. A QML \l Item can have children but a layer in a Photoshop document cannot have child
    layers. To mitigate this variance, a Group layer is created and child items are added to this
    Group layer. The Group layer acts as a virtual parent and the correct parent is assigned when
    the Photoshop document is exported and re-imported in \QDS.

    \note The parent-child relationship may be lost if the virtual parent Group layer is moved or
    deleted.

*/
