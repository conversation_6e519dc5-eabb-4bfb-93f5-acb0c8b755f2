import QmlProject

Project {
    mainFile: "content/App.qml"

    /* Include .qml, .js, and image files from current directory and subdirectories */
    QmlFiles {
        directory: "content"
    }

    JavaScriptFiles {
        directory: "content"
    }

    ImageFiles {
        directory: "content"
    }

    Files {
        filter: "*.conf"
        files: ["qtquickcontrols2.conf"]
    }

    Files {
        filter: "qmldir"
        directory: "."
    }

    Files {
        filter: "*.ttf;*.otf"
    }

    Files {
        filter: "*.wav;*.mp3"
    }

    Files {
        filter: "*.mp4"
    }

    Files {
        filter: "*.glsl;*.glslv;*.glslf;*.vsh;*.fsh;*.vert;*.frag"
    }

    Files {
        filter: "*.mesh"
        directory: "asset_imports"
    }

    Environment {
        QT_QUICK_CONTROLS_CONF: "qtquickcontrols2.conf"
        QT_AUTO_SCREEN_SCALE_FACTOR: "1"
        QT_LOGGING_RULES: "qt.qml.connections=false"
        QT_ENABLE_HIGHDPI_SCALING: "0"
        /* Useful for debugging
       QSG_VISUALIZE=batches
       QSG_VISUALIZE=clip
       QSG_VISUALIZE=changes
       QSG_VISUALIZE=overdraw
       */
    }

    qt6Project: true

    /* List of plugin directories passed to QML runtime */
    importPaths: [ "imports", "asset_imports" ]

    /* Required for deployment */
    targetDirectory: "/opt/Loginui1"

    qdsVersion: "3.0"

    /* If any modules the project imports require widgets (e.g. QtCharts), widgetApp must be true */
    widgetApp: true
}
