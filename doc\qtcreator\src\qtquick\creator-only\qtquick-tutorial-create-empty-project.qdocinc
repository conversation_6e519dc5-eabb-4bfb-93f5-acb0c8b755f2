// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [qtquick empty application]

    \section1 Creating the Project

    \list 1

        \li Select \uicontrol File > \uicontrol {New Project} >
            \uicontrol {Application (Qt)} > \uicontrol {Qt Quick Application}.

        \li Select \uicontrol Choose to open the
            \uicontrol {Project Location} dialog.

        \li In the \uicontrol Name field, enter a name for the application.
            When naming your own projects, keep in mind that they cannot be
            easily renamed later.

        \li In the \uicontrol {Create in} field, enter the path for the project
            files. You can move project folders later without problems.

        \li Select \uicontrol Next (or \uicontrol Continue on \macos) to open
            the \uicontrol {Define Build System} dialog.

        \li In the \uicontrol {Build system} field, select \l {Setting Up CMake}
            {CMake} as the build system to use for building and running the
            project.

            \note If you select \l qmake, the instructions for configuring the
            project won't apply.

        \li Select \uicontrol Next to open the
            \uicontrol {Define Project Details} dialog.

        \li In the \uicontrol {Minimum required Qt version} field, select
            Qt 6.2.

        \li Select \uicontrol Next to open the \uicontrol {Translation File}
            dialog.

        \li Select \uicontrol Next to use the default settings and to open
            the \uicontrol {Kit Selection} dialog.

        \li Select Qt 6.2 or later \l{glossary-buildandrun-kit}{kits} for the
            platforms that you want to build the application for. To build
            applications for mobile devices, select kits also for Android and
            iOS.

            \note The list shows kits that you specify in \uicontrol Edit
            > \uicontrol Preferences > \uicontrol Kits (on Windows and Linux)
            or in \uicontrol {\QC} > \uicontrol Preferences >
            \uicontrol Kits (on \macos).
            For more information, see \l {Adding Kits}.

        \li Select \uicontrol Next to open the \uicontrol {Project Management}
            dialog.

        \li Review the project settings, and select \uicontrol{Finish}
            (or \uicontrol Done on \macos) to create the project.

    \endlist

    For more information about the settings that you skipped and the other
    wizard templates available, see \l{Creating Qt Quick Applications}.

//! [qtquick empty application]
*/
