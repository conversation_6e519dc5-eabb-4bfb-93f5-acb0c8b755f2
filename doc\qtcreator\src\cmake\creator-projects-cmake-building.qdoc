// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-build-settings.html
    \page creator-build-settings-cmake.html
    \nextpage creator-build-settings-qmake.html

    \title CMake Build Configuration

    Configuring medium-sized to large CMake projects in \QC can be a
    challenge due to the number of variables that you need to pass to
    CMake to configure the project correctly. To make this easier,
    \QC creates an initial configuration for you based on the kit
    settings and displays it in \uicontrol {Initial Configuration} in
    the \l{Specifying Build Settings}{Build Settings} of the project.
    Alternatively, you can use CMake presets to configure CMake.

    The \uicontrol Configuration field displays the effective CMake call that
    is constructed by using the values of the \uicontrol {Build directory} and
    \uicontrol {Build type} fields.

    \image qtcreator-cmake-build-settings-initial.png "CMake build settings"

    \uicontrol {Initial Configuration} lists the variables that are used to
    configure the CMake project for the first time. The default values that
    are inherited from the kit's CMake configuration are displayed in italic.
    The initial configuration list of variables is saved in the project's source
    directory as the \e CMakeLists.txt.user file.

    \uicontrol {Current Configuration} lists the CMake variables in the
    \c cmake-file-api JSON export in the \c {.cmake/api/v1/reply} directory.
    The variables that are inherited from the initial configuration are
    displayed in italic. Mismatched values are displayed in red.

    You can view and edit the actual values of the variables that are passed
    to CMake. Variable names are listed in the \uicontrol Key column and their
    current values in the \uicontrol Value column. For more information about
    the available variables, select \uicontrol Help in the context menu or see
    \l{CMake: cmake-variables(7)}. For more information about Qt-specific
    variables, see \l{CMake Variable Reference}.

    You can specify additional CMake options, such as \c {--find-debug},
    \c {--trace-expand}, or \c {--warn-uninitialized}, in
    \uicontrol {Additional CMake options}. For more information about
    the available options, click the link in the field name or see
    \l{CMake: cmake(1)}.

    After successfully running CMake, you can view and modify the current
    configuration in \uicontrol {Current Configuration}.

    Select \uicontrol {Kit Configuration} to edit the CMake settings for the
    build and run kit selected for the project.

    \section1 CMake Presets

    You can use CMake presets files to specify common configure, build, and test
    options and share them with others. \c CMakePresets.json contains options for
    project-wide builds, whereas \c CMakeUserPresets.json contains options for
    your local builds.

    Create the presets files in the format described in
    \l{https://cmake.org/cmake/help/latest/manual/cmake-presets.7.html}
    {cmake-presets(7)} and store them in project's root directory.

    \QC supports presets up to version 3 (introduced in CMake 3.21), but version
    checking is not enforced. All the fields from version 3 are read and used if
    present. Test presets are not supported.

    You can import the presets the first time you \l {Opening Projects}
    {open a project}, when no \c CMakeLists.txt.user file exists or you have
    disabled all kits in the project. To update changes to the
    \c CMakePresets.json file, delete the \c CMakeLists.txt.user file.

    \image qtcreator-cmake-presets-configure.webp "Opening a project that contains Cmake presets"

    You can view the presets in the \uicontrol {Initial Configuration} field and
    in the environment configuration field below it.

    \image qtcreator-cmake-presets-environment.webp "CMake environment configuration"

    \section2 Configure Presets

    The following configure presets instruct CMake to use the default generator
    on the platform and specify the build directory for all build types.
    \c NOT_COMMON_VALUE is displayed in \uicontrol {Initial Parameters}
    and \c AN_ENVIRONMENT_FLAG in the environment configuration field.

    \badcode
    {
      "version": 1,
      "configurePresets": [
        {
          "name": "preset",
          "displayName": "preset",
          "binaryDir": "${sourceDir}/build/preset",
          "cacheVariables": {
            "NOT_COMMON_VALUE": "NOT_COMMON_VALUE"
            },
          "environment": {
            "AN_ENVIRONMENT_FLAG": "1"
          }
        }
      ]
    }
    \endcode

    \section2 MinGW Example

    The following example configures a Qt project with:

    \list
        \li MinGW compiler
        \li build directory – \c <sourceDir>/build-release
        \li build type – \c CMAKE_BUILD_TYPE as \c Release
        \li generator – MinGW Makefiles
        \li path to a CMake executable
        \li path to the Qt installation via \c CMAKE_PREFIX_PATH
    \endlist

    \badcode
    {
      "version": 1,
      "configurePresets": [
        {
          "name": "mingw",
          "displayName": "MinGW 11.2.0",
          "generator": "MinGW Makefiles",
          "binaryDir": "${sourceDir}/build-release",
          "cmakeExecutable": "C:/Qt/Tools/CMake_64/bin/cmake.exe",
          "cacheVariables": {
            "CMAKE_BUILD_TYPE": "Release",
            "CMAKE_PREFIX_PATH": "C:/Qt/6.4.0/mingw_64"
          },
          "environment": {
            "PATH": "C:/Qt/Tools/mingw1120_64/bin;$penv{PATH}"
          }
        }
      ]
    }
    \endcode

    To speed up the process on Windows, specify the \c CMAKE_C_COMPILER and
    \c CMAKE_CXX_COMPILER in the \c cacheVariables section.

    \section2 Ninja Generator Example

    The following configure and build presets set Ninja Multi-Config as the
    generator, add \c Debug and \c Release build steps, and specify the path
    to \c ninja.exe as a value of the \c CMAKE_MAKE_PROGRAM variable:

    \badcode
    {
      "version": 2,
      "configurePresets": [
        {
          "name": "ninja-nmc",
          "displayName": "Ninja Multi-Config MinGW",
          "generator": "Ninja Multi-Config",
          "binaryDir": "${sourceDir}/build",
          "cacheVariables": {
            "CMAKE_BUILD_TYPE": "Debug;Release",
            "CMAKE_PREFIX_PATH": "C:/Qt/6.4.0/mingw_64"
            "CMAKE_MAKE_PROGRAM": "C:/Qt/Tools/Ninja/ninja.exe"
          },
          "environment": {
            "PATH": "c:/Qt/Tools/mingw1120_64/bin;$penv{PATH}"
          }
        }
      ],
      "buildPresets": [
        {
          "name": "release",
          "displayName": "Ninja Release",
          "configurePreset": "ninja-nmc",
          "configuration": "Release"
        },
        {
          "name": "debug",
          "displayName": "Ninja Debug",
          "configurePreset": "ninja-nmc",
          "configuration": "Debug"
        }
      ]
    }
    \endcode

    This example assumes that the CMake executable path is set in
    \uicontrol Edit > \uicontrol Preferences > \uicontrol CMake >
    \uicontrol Tools.

    \section2 Using Conditions

    The following configure presets are used if they match \c condition. That is,
    if the \c hostSystemName equals \c Linux, the \c linux presets are used and
    if it equals \c Windows, the \c windows presets are used.

    \badcode
    {
      "version": 3,
      "configurePresets": [
        {
          "name": "linux",
          "displayName": "Linux GCC",
          "binaryDir": "${sourceDir}/build",
          "cacheVariables": {
            "CMAKE_PREFIX_PATH": "$env{HOME}/Qt/6.4.0/gcc_64"
          },
          "condition": {
            "type": "equals",
            "lhs": "${hostSystemName}",
            "rhs": "Linux"
          }
        },
        {
          "name": "windows",
          "displayName": "Windows MSVC",
          "binaryDir": "${sourceDir}/build",
          "cacheVariables": {
            "CMAKE_PREFIX_PATH": "$env{SYSTEMDRIVE}/Qt/6.4.0/msvc2019_64"
          },
          "condition": {
            "type": "equals",
            "lhs": "${hostSystemName}",
            "rhs": "Windows"
          }
        }
      ]
    }
    \endcode

    \section1 Multi-Config Support

    \QC supports
    \l{https://cmake.org/cmake/help/latest/prop_gbl/GENERATOR_IS_MULTI_CONFIG.html}
    {Multi-config generators}, such as Xcode, Visual Studio, and Ninja
    Multi-Config. This means that you need to configure CMake only once, have
    only one build directory, and can switch between build types faster.

    However, this means that \QC can no longer simply parse the first CMake
    file-api JSON export. Therefore, the value of the \uicontrol {Build type}
    field must match that of the \c CMAKE_BUILD_TYPE variable for the single
    configuration generators (Ninja, Makefile) to determine, which generator
    to use.

    When developing with Qt 6 for iOS, only the Xcode generator is supported.

    \section1 Modifying Variable Values

    You can view and edit the actual values of the variables that
    are passed to CMake in \uicontrol {Initial Configuration} or
    \uicontrol {Current Configuration}.

    \image qtcreator-cmake-build-settings.png "CMake variables"

    To view all variables, select the \uicontrol Advanced check box.

    To add variables, select \uicontrol Add, and then select the type of
    the variable that you are adding: \uicontrol Boolean, \uicontrol String,
    \uicontrol Directory, or \uicontrol File.

    To change the type of the selected variable, right-click the
    variable name in the \uicontrol Key column, and then select
    \uicontrol {Force to bool}, \uicontrol {Force to file},
    \uicontrol {Force to directory}, or \uicontrol {Force to string}
    in the context menu.

    To copy the name or value of the selected variable to the clipboard,
    select \uicontrol Copy in the context menu.

    To modify the value of a variable, double-click it, or select it,
    and then select \uicontrol Edit. If the initial, current, and kit
    configuration get out of sync, select \uicontrol {Apply Kit Value} or
    \uicontrol {Apply Initial Configuration Value} in the context menu in
    \uicontrol {Initial Configuration} or \uicontrol {Current Configuration}.

    You can apply actions to multiple variables at a time. To clear
    the selection, select \uicontrol {Clear Selection}.

    To remove the selected variables, select \uicontrol Unset. To undo
    the removal, select \uicontrol Set.

    To reset all the changes that you made, select \uicontrol Reset.

    To modify the environment variable values for the CMake build environment,
    select \uicontrol {Batch Edit}. For more information, see \l{Batch Editing}.

    To build using the current configuration, select \uicontrol {Run CMake}.
    While building, the button text changes to \uicontrol {Stop CMake}. Select
    the button to cancel the current build.

    The variable values that you change are passed via \c -D<option>=<value>
    to CMake, which stores the options in the CMakeCache.txt file. This means
    that if you remove the build directory, all the custom variables that are
    not part of the initial CMake configuration are also removed.

    To reconfigure a project using the modified variable values,
    select \uicontrol Build > \uicontrol {Clear CMake Configuration}, which
    removes the CMakeCache.txt file. This enables you to do a full rebuild.

    \section1 Re-configuring with Initial Variables

    To reset CMake variables to the initial ones, select
    \uicontrol {Re-configure with Initial Variables} in
    \uicontrol {Initial Configuration}. \QC deletes the current CMake
    configuration and runs CMake. The initial configuration values are
    stored in the CMakeLists.txt.user file, so deleting a build directory
    does not delete the initial configuration.

    To be asked before \QC resets the changes, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol CMake > \uicontrol General >
    \uicontrol {Ask before re-configuring with initial parameters}.

    \image qtcreator-preferences-cmake-general.png "General tab in CMake Preferences"

    \section1 Viewing CMake Output

    Output from CMake is displayed next to the \uicontrol {Build Settings} and
    \uicontrol {Run Settings} panes in the \uicontrol Projects mode.

    \image qtcreator-build-cmake-output.png "CMake output in Projects mode"

    To clear the search results, select the \inlineimage icons/clean_pane_small.png
    (\uicontrol Clear) button.

    You can enter a string in the \uicontrol Filter field to filter output.
    To specify filtering options, select the
    \inlineimage icons/magnifier.png "Filtering options menu"
    button. You can filter output by using regular expressions or
    case-sensitivity. Select \uicontrol {Show Non-matching Lines} to
    hide the lines that match the filter.

    Press \key {Ctrl+F} to \l{Finding and Replacing}{search} for a string from
    the output.

    To increase or decrease the output text size, select \inlineimage icons/plus.png
    (\uicontrol {Zoom In}) or \inlineimage icons/minus.png
    (\uicontrol {Zoom Out}), or press \key Ctrl++ or \key Ctrl+-.

    To hide the output, select the \inlineimage icons/rightsidebaricon.png
    (\uicontrol {Hide/Show Right Sidebar}) button or press \key {Alt+Shift+0}.

    \section1 CMake Build Steps

    \QC builds CMake projects by running \c {cmake . --build}, which then runs
    the CMake generator specified in the project configuration: \c make,
    \c mingw32-make, \c nmake, or \c ninja, for example. The CMake generator
    produces project files for \QC. Multi-config generators are also supported.

    You can add arguments to pass to CMake and the generator and targets for
    the build command in \uicontrol {Build Steps}.

    \image qtcreator-cmake-build-steps.png "CMake build steps"

    \note While the other CMake generators are installed together with Qt,
    you usually need to install Ninja yourself.

    \section1 Using Ninja as a CMake Generator

    To use \l {https://ninja-build.org/}{Ninja} with CMake, you must install it
    and select it as the CMake generator in the build and run kit:

    \list 1
        \li Install Ninja.
        \li Add the path to the Ninja executable to the value of the PATH system
            variable.
        \li In \uicontrol Projects > \uicontrol {Build & Run} > \uicontrol Build
            > \uicontrol {Build Settings}, select \uicontrol {Kit Configuration}.
            \image qtcreator-cmake-kit-configuration.png "Kit CMake Configuration dialog"
        \li Select \uicontrol Change next to the \uicontrol {CMake generator}
            field to open the \uicontrol {CMake Generator} dialog.
            \image qtcreator-cmake-generator.png "CMake Generator dialog"
        \li In \uicontrol Generator, select \uicontrol Ninja.
        \li Select \uicontrol OK to save your changes and close the dialog.
        \li Select \uicontrol Close to close the
            \uicontrol {Kit CMake Configuration} dialog
            and return to \uicontrol {Build Settings}.
    \endlist

    \note To make sure that old build artifacts don't get in the way
    the first time you build the project after the change, select
    \uicontrol Build > \uicontrol {Rebuild Project}. This cleans up the
    build directory and performs a new build.

    \section1 Using CMake with Conan

    \QC can automatically set up the \l {Setting Up Conan}
    {Conan package manager} for use with CMake.

    Select \uicontrol Edit > \uicontrol Preferences > \uicontrol CMake
    \uicontrol General > \uicontrol {Package manager auto setup} to set the
    value of the \c CMAKE_PROJECT_INCLUDE_BEFORE variable to the path to a
    CMake script that installs dependencies from a \c conanfile.txt,
    \c conanfile.py, or \c vcpkg.json file in the project source directory.

    \section1 CMake Clean Steps

    When building with CMake, you can add arguments to pass to CMake and the
    generator and targets for the clean command in \uicontrol {Clean Steps}.

    \image qtcreator-cmake-clean-steps.png

    The build errors and warnings are parsed and displayed in \l Issues.
*/
