// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-positioning.html
    \previouspage qtquick-properties.html
    \if defined(qtdesignstudio)
    \nextpage qtquick-annotations.html
    \else
    \nextpage qtquick-fonts.html
    \endif

    \title Scalable Layouts

    The position of a \l{glossary-component}{component} in a UI can be either
    absolute or relative to other components. The visual components exist at a
    particular location in the screen coordinate system at any instant in time.
    The x and y coordinates of a visual component are relative to those of its
    visual parent, with the top-left corner having the coordinate (0, 0).

    If you are designing a static UI,
    \l{Important Concepts In Qt Quick - Positioning#manual-positioning}
    {manual positioning} provides the most efficient form of positioning
    components. For a dynamic UI, you can employ the following positioning
    methods:

    \list
        \li \l{Setting Bindings}
        \li \l{Setting Anchors and Margins}
        \li \l{Aligning and Distributing Components}
        \li \l{Using Positioners}
        \li \l{Using Layouts}
        \li \l{Organizing Components}
    \endlist

    \section2 Setting Bindings

    \l{Positioning with Bindings}{Property binding} is a declarative way of
    specifying the value of a property. Binding allows a property value to be
    expressed as a JavaScript expression that defines the value relative to
    other property values or data accessible in the application. The property
    value is automatically kept up to date if the other properties or data
    values change.

    Property bindings are created implicitly whenever a property is assigned a
    JavaScript expression. To set JavaScript expressions as values of properties
    in the \l Properties view, select the \inlineimage icons/action-icon.png
    (\uicontrol Actions) menu next to a property, and then select
    \uicontrol {Set Binding}.

    \image qmldesigner-set-expression.png "Actions menu"

    In \uicontrol {Binding Editor}, select a component and a property from
    lists of available components and their properties.

    \image qmldesigner-binding-editor.png "Binding Editor"

    Alternatively, start typing a
    string and press \key Ctrl+Space to display a list of properties, IDs, and
    code snippets. When you enter a period (.) after a property name, a list of
    available values is displayed. Press \key Enter to accept the first
    suggestion in the list and to complete the code. For more information, see
    \l{Completing Code}.

    When a binding is set, the \uicontrol Actions menu icon changes to
    \inlineimage icons/action-icon-binding.png
    . To remove bindings, select \uicontrol Actions > \uicontrol Reset.

    You can set bindings also on the \uicontrol Bindings tab in the
    \l Connections view.
    For more information, see \l {Adding Bindings Between Properties}.

    For more information on the JavaScript environment provided, see
    \l{Integrating QML and JavaScript}.

    Bindings are a black box for \QDS and using them might have a
    negative impact on performance, so consider setting anchors and margins for
    components, instead. For example, instead of setting \c {parent.width} for a
    component, you could anchor the component to its sibling components on the
    left and the right.

    \section2 Setting Anchors and Margins

    In an \l{Important Concepts In Qt Quick - Positioning#anchors}
    {anchor-based} layout, each component can be thought of as having a set of
    invisible \e anchor lines: top, bottom, left, right, fill, horizontal
    center, vertical center, and baseline.

    In \l Properties > \uicontrol Layout, you can set anchors and margins for
    components. To set the anchors of a component, click the anchor buttons.
    You can combine the top/bottom, left/right, and horizontal/vertical anchors
    to anchor components in the corners of the parent component or center them
    horizontally or vertically within the parent component.

    \image qmldesigner-anchor-buttons.png "Anchor buttons"

    For convenience, you can click the \inlineimage icons/anchor-fill.png
    (\uicontrol {Fill to Parent}) toolbar button to apply fill anchors to a
    component and the \inlineimage icons/qtcreator-anchors-reset-icon.png
    (\uicontrol {Reset Anchors}) button to reset the anchors to their saved
    state.

    You can specify the baseline anchor in the \l{Code} view.

    For performance reasons, you can only anchor a component to its siblings
    and direct parent. By default, a component is anchored to its parent when
    you use the anchor buttons. Select a sibling of the component in the
    \uicontrol Target field to anchor to it instead.

    Arbitrary anchoring is not supported. For example, you cannot specify:
    \c {anchor.left: parent.right}. You have to specify:
    \c {anchor.left: parent.left}. When you use the anchor buttons, anchors to
    the parent component are always specified to the same side. However, anchors
    to sibling components are specified to the opposite side:
    \c {anchor.left: sibling.right}. This allows you to keep sibling components
    together.

    In the following image, the left edge of \e rectangle1 is anchored to the
    right edge of the sibling on its left side, \e rectangle, while its top
    edge is anchored to the top of its parent.

    \image qmldesigner-anchors.png "Anchoring sibling components"

    The anchors are specified as follows in code:

    \qml
    Rectangle {
        id: rectangle1
        anchors.left: rectangle.right
        anchors.top: parent.top
        anchors.leftMargin: 5
        anchors.topMargin: 30
    }
    \endqml

    Margins specify the amount of empty space to leave to the outside of a
    component. Margins only have meaning for anchors. They do not take any
    effect when using layouts or absolute positioning.

    \section2 Aligning and Distributing Components

    When you're working with a group of components, you can select them to align
    and distribute them evenly. As the positions of the components are fixed,
    you cannot apply these functions to anchored components. For scalability,
    you can anchor the aligned and distributed components when your design is
    ready.

    \image qmldesigner-alignment.png "Aligning sibling components"

    Select the buttons in the \uicontrol Alignment field to align the top/bottom
    or left/right edges of a group of components to the one farthest away
    from the center of the group. For example, when left-aligning, the
    components are aligned to the leftmost component. You can also align the
    horizontal/vertical centers of components, or both.

    In the \uicontrol {Align to} field, select whether to align the components
    in respect to the selection, the root component, or a \e {key component}
    that you select in the \uicontrol {Key object} field. The key component must
    be a part of the selection.

    You can distribute either \e components or the \e spacing between them.
    If the components or spacing cannot be distributed to equal pixel values
    without ending up with half pixels, you receive a notification. You can
    either allow \QDS to distribute components or spacing using the closest
    values possible or tweak your design so that the components and spacing
    can be distributed perfectly.

    When distributing components, select the buttons in the
    \uicontrol {Distribute objects} field to determine whether the
    distance between components is calculated from their top/bottom
    or left/right edges or their horizontal/vertical center.

    When distributing spacing, select the buttons in the
    \uicontrol {Distribute spacing} field to determine whether
    it is distributed evenly within a target area or at
    specified distances, calculated from a starting point.
    You can select the orientation in which the components are distributed
    evenly within the target area: horizontally along the x axis or vertically
    along the y axis.

    Alternatively, you can distribute spacing in pixels by selecting one of the
    starting point buttons: top/left or bottom/right edge of the target area or
    item, or its center. The edge to use depends on whether the items are
    distributed horizontally or vertically:

    \list
        \li Select \inlineimage icons/distribute-origin-top-left.png
            and \inlineimage icons/distribute-spacing-horizontal.png
            to use the left edge of the target area or item as the starting
            point.
        \li Select \inlineimage icons/distribute-origin-top-left.png
            and \inlineimage icons/distribute-spacing-vertical.png
            to use the top edge.
        \li Select \inlineimage icons/distribute-origin-bottom-right.png
            and \inlineimage icons/distribute-spacing-horizontal.png
            to use the right edge.
        \li Select \inlineimage icons/distribute-origin-bottom-right.png
            and \inlineimage icons/distribute-spacing-vertical.png
            to use the bottom edge.
    \endlist

    \note Some components might end up outside the target area.

    In the \uicontrol {Pixel spacing} field, you can set the space between
    components in pixels. You can disable the distribution of spacing in
    pixels by clicking the \inlineimage icons/distribute-origin-none.png
    button.

    \section3 Summary of Aligment Buttons

    The following table summarizes the buttons available in the
    \uicontrol Alignment section.

    \table
    \header
        \li Icon
        \li Purpose
    \row
        \li \inlineimage icons/align-left.png
        \li Aligns the left edges of the selected components to the one
            farthest away from the center of the group.
    \row
        \li \inlineimage icons/align-center-horizontal.png
        \li Aligns the horizontal centers of the selected components.
    \row
        \li \inlineimage icons/align-right.png
        \li Aligns the right edges of the selected components.
    \row
        \li \inlineimage icons/align-top.png
        \li Aligns the top edges of the selected components.
    \row
        \li \inlineimage icons/align-center-vertical.png
        \li Aligns the verical centers of the selected components.
    \row
        \li \inlineimage icons/align-bottom.png
        \li Aligns the bottom edges of the selected components.
    \row
        \li \inlineimage icons/distribute-left.png
        \li Distributes the selected components and calculates the distance
            between them from their left edges.
    \row
        \li \inlineimage icons/distribute-center-horizontal.png
        \li Distributes the selected components and calculates the distance
            between them from their horizontal centers.
    \row
        \li \inlineimage icons/distribute-right.png
        \li Distributes the selected components and calculates the distance
            between them from their right edges.
    \row
        \li \inlineimage icons/distribute-top.png
        \li Distributes the selected components and calculates the distance
            between them from their top edges.
    \row
        \li \inlineimage icons/distribute-center-vertical.png
        \li Distributes the selected components and calculates the distance
            between them from their vertical centers.
    \row
        \li \inlineimage icons/distribute-bottom.png
        \li Distributes the selected components and calculates the distance
            between them from their bottom edges.
    \row
        \li \inlineimage icons/distribute-spacing-horizontal.png
        \li Distributes spacing between the selected components horizontally.
    \row
        \li \inlineimage icons/distribute-spacing-vertical.png
        \li Distributes spacing between the selected components vertically.
    \row
        \li \inlineimage icons/distribute-origin-none.png
        \li Disables the distribution of spacing in pixels.
    \row
        \li \inlineimage icons/distribute-origin-top-left.png
        \li Sets the top or left edge of the target area or item as the
            starting point for distributing spacing in pixels depending on
            the distribution orientation.
    \row
        \li \inlineimage icons/distribute-origin-center.png
        \li Sets the center of the target area as the starting point
            for distributing spacing in pixels.
    \row
        \li \inlineimage icons/distribute-origin-bottom-right.png
        \li Sets the bottom or right edge of the target area or item as the
            starting point for distributing spacing in pixels, depending on
            the distribution orientation.
    \endtable

    \section2 Using Positioners

    Positioner components are containers that manage the positions of their
    child components. For many use cases, the best positioner to use is a simple
    column, row, flow, or grid. You can use the components available in
    \uicontrol Components > \uicontrol {Default Components} >
    \uicontrol Positioner to position the children of a component in these
    formations in the most efficient manner possible.

    To position several components in a \uicontrol Column, \uicontrol Row,
    \uicontrol Flow, or \uicontrol Grid, select the components in
    the \l {2D} view, and then select \uicontrol Position in
    the context menu.

    \section3 Column Positioner

    A \uicontrol Column positions its child components along a single column.
    It can be used as a convenient way to vertically position a series of
    components without using anchors.

    \image qtquick-positioner-column-properties.png "Column properties"

    For all positioners, you can specify the spacing between the child
    components that they contain in the \uicontrol Spacing field.

    In addition, you can specify the vertical and horizontal padding between
    content and the left, right, top, and bottom edges of components as values
    of the fields in the \l Padding section.

    \section3 Row and Flow Positioners

    A \uicontrol Row positions its child components along a single row. It can
    be used as a convenient way to horizontally position a series of components
    without using anchors.

    The \uicontrol Flow component positions its child components like words on a
    page, wrapping them to create rows or columns of components.

    \image qtquick-positioner-flow-properties.png "Flow properties"

    For flow and row positioners, you can also set the direction of a flow to
    either left-to-right or top-to-bottom in the \uicontrol Flow field.
    Components are positioned next to to each other according to the value you
    set in the \uicontrol {Layout direction} field until the width or height of
    the \uicontrol Flow component is exceeded, then wrapped to the next row or
    column.

    You can set the layout direction to either \uicontrol LeftToRight or
    \uicontrol RightToLeft in the \uicontrol {Layout direction} field. If
    the width of the row is explicitly set, the left anchor remains to the
    left of the row and the right anchor remains to the right of it.

    \section3 Grid Positioner

    A \uicontrol Grid creates a grid of cells that is large enough to hold all
    of its child components, and places these components in the cells from left
    to right and top to bottom. Each component is positioned at the top-left
    corner of its cell with position (0, 0).

    \QDS generates the grid based on the positions of the child components in
    the \l {2D} view. You can modify the number of rows and columns in the
    \uicontrol Rows and \uicontrol Columns fields.

    \image qtquick-positioner-grid-properties.png "Grid properties"

    In addition to the flow and layout direction, you can set the horizontal
    and vertical alignment of grid components. By default, grid components are
    vertically aligned to the top. Horizontal alignment follows the value of the
    \uicontrol {Layout direction} field. For example, when layout direction is
    set to \uicontrol LeftToRight, the components are aligned on the left.

    To mirror the layout, set the layout direction to \uicontrol RightToLeft.
    To also mirror the horizontal alignment of components, select
    \uicontrol AlignRight in the \uicontrol {Alignment H} field.

    \section3 Summary of Positioners

    The following table lists the positioners that you can use to arrange
    components in UIs. They are available in \uicontrol Components
    > \uicontrol {Default Components} > \uicontrol Positioner.

    \table
    \header
        \li Icon
        \li Name
        \li Purpose
    \row
        \li \inlineimage column-positioner-icon-16px.png
        \li \l[QtQuick] {Column}
        \li Arranges its child components vertically.
    \row
        \li \inlineimage row-positioner-icon-16px.png
        \li \l[QtQuick] {Row}
        \li Arranges its child components horizontally.
    \row
        \li \inlineimage grid-positioner-icon-16px.png
        \li \l[QtQuick] {Grid}
        \li Arranges its child components so that they are aligned in a grid and
            are not overlapping.
    \row
        \li \inlineimage flow-positioner-icon-16px.png
        \li \l[QtQuick] {Flow}
        \li Arranges its child components side by side, wrapping as necessary.
    \endtable

    \section2 Using Layouts

    \if defined(qtcreator)
    Since Qt 5.1, you can use QML types in the \l{qtquicklayouts-index.html}
    {Qt Quick Layouts} module to arrange components in UIs.
    \else
    You can use the components available in \uicontrol Components
    > \uicontrol {Qt Quick Layouts} to arrange components in UIs.
    \endif
    Unlike positioners, layouts manage both the positions and sizes of their
    child components, and are therefore well suited for dynamic and resizable
    UIs. However, this means that you should not specify fixed positions and
    sizes for the child components in the \l{2D Geometry}{Geometry - 2D} section
    in \l Properties, unless their implicit sizes are not satisfactory.

    You can use anchors or the width and height properties of the layout itself
    to specify its size in respect to its non-layout parent component. However,
    do not anchor the child components within layouts.

    To arrange several components in a column, row, grid, or
    \uicontrol {Stack Layout}, select the components in the \l {2D} view,
    and then select \uicontrol Layout in the context menu.

    You can also click the \inlineimage column.png
    (\uicontrol {Column Layout}), \inlineimage row.png
    (\uicontrol {Row Layout}), and \inlineimage grid.png
    (\uicontrol {Grid Layout}) toolbar buttons to apply
    layouts to the selected components.

    To make a component within a layout as wide as possible while respecting the
    given constraints, select the component in the \uicontrol {2D} view, and
    then select \uicontrol Layout > \uicontrol {Fill Width} in the context menu.
    To make the component as high as possible, select \uicontrol {Fill Height}.

    \section3 Layout Properties

    A \uicontrol {Grid Layout} component provides a way of dynamically
    arranging components in a grid. If the grid layout is resized, all
    its child components are rearranged. If you want a layout with just
    one row or one column, use the \uicontrol {Row Layout} or
    \uicontrol {Column Layout} component.

    The child components of row and column layout components are automatically
    positioned either horizontally from left to right as rows or vertically from
    top to bottom as columns. The number of the child components determines the
    width of the row or the height of the column. You can specify the spacing
    between the child components in the \uicontrol Spacing field.

    The child components of grid layout components are arranged according to the
    \uicontrol Flow property. When the direction of a flow is set to
    \uicontrol LeftToRight, child components are positioned next to to each
    other until the the number of columns specified in the
    \uicontrol {Columns & Rows} field is reached. Then,
    the auto-positioning wraps back to the beginning of the next row.

    \image qtquick-layout-grid-properties.png "Grid Layout properties"

    If you set the direction of the flow to \uicontrol TopToBottom, child
    components are auto-positioned vertically using the number of rows set
    in the \uicontrol {Columns & Rows} field to determine the maximum number
    of rows.

    You can set the layout direction to either \uicontrol LeftToRight or
    \uicontrol RightToLeft in the \uicontrol {Layout direction} field.
    When you select \uicontrol RightToLeft, the alignment of the components
    will be mirrored.

    You can specify the spacing between rows and columns in the
    \uicontrol Spacing field.

    \section3 Stack Layout

    \image qtquick-designer-stacked-view.png

    To add components to a \uicontrol {Stack Layout}, select the
    \inlineimage icons/plus.png
    button next to the component name in the \l {2D} view.
    To move between components, select the \inlineimage icons/prev.png
    (\uicontrol Previous) and \inlineimage icons/next.png
    (\uicontrol Next) buttons.

    To add a tab bar to a stack layout, right-click on the
    \uicontrol {Stack Layout} in \l Navigator to access the context menu, and
    select \uicontrol {Stacked Container} > \uicontrol {Add Tab Bar}.

    To raise or lower the stacking order of a component, select
    \uicontrol {Stacked Container} > \uicontrol {Increase Index} or
    \uicontrol {Decrease Index}.

    \section3 Summary of Layouts

    The following table lists the layout components that you can use to arrange
    components in UIs. They are available in \uicontrol Components
    > \uicontrol {Qt Quick Layouts}.

    \table
    \header
        \li Icon
        \li Name
        \li Purpose
    \row
        \li \inlineimage column-layouts-icon-16px.png
        \li \l{ColumnLayout}{Column Layout}
        \li Provides a grid layout with only one column.
    \row
        \li\inlineimage row-layouts-icon-16px.png
        \li \l{RowLayout}{Row Layout}
        \li Provides a grid layout with only one row.
    \row
        \li \inlineimage grid-layouts-icon-16px.png
        \li \l{GridLayout}{Grid Layout}
        \li Provides a way of dynamically arranging components in a grid.
    \row
        \li \inlineimage stack-layouts-icon-16px.png
        \li \l{StackLayout}{Stack Layout}
        \li Provides a stack of components where only one component is
            visible at a time.
    \endtable


    \section2 Organizing Components

    You can use the \uicontrol Frame and \uicontrol {Group Box} controls to
    draw frames around groups of controls.
    \if defined(qtdesignstudio)
    If you don't want a frame, use the \uicontrol Group component instead.
    \endif


    The following table lists the UI controls that you can use to organize
    components in UIs (since Qt 5.7). The \e Location column indicates the
    location of the component in \uicontrol Components.

    \table
    \header
        \li Icon
        \li Name
        \li Location
        \li Purpose
    \row
        \li \inlineimage icons/frame-icon16.png
        \li \l [QtQuickControls]{Frame}
        \li Qt Quick Controls
        \li A visual frame around a group of controls.
    \if defined(qtdesignstudio)
    \row
        \li \inlineimage icons/group-16px.png
        \li Group
        \li Qt Quick Studio Components
        \li Enables handling the selected components as a group.
    \endif
    \row
        \li \inlineimage icons/groupbox-icon16.png
        \li \l [QtQuickControls]{GroupBox}{Group Box}
        \li Qt Quick Controls
        \li A titled visual frame around a group of controls.
    \row
        \li \inlineimage icons/page-icon16.png
        \li \l [QtQuickControls]{Page}
        \li Qt Quick Controls
        \li A styled page control with support for a header and footer.
    \row
        \li \inlineimage icons/pane-icon16.png
        \li \l [QtQuickControls]{Pane}
        \li Qt Quick Controls
        \li A background that matches the application style and theme.
    \endtable
*/
