// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage external-tool-spec.html
    \page qtcreator-ui-text.html
    \nextpage coding-style.html

    \title User Interface Text Guidelines

    Follow the guidelines in this section to make sure that your extensions
    are consistent with the Qt Creator UI and that they can be easily
    localized into different languages.

    When you write UI text, make sure that it is:

    \list

        \li  Consistent with existing Qt Creator UI terms

        \li  Short and concise

        \li  Neutral, descriptive, and factually correct

        \li  Unambigious

        \li  Translatable into different languages

    \endlist

    \section1 Grammar and Style

    All UI text must be grammatically correct English and use the standard form
    of written language. Do not use dialect or slang words. Use idiomatic
    language, that is, expressions that are characteristic for English.
    If possible, ask a native English speaker for a review.

    UI text should be concise and economically formulated. Avoid unnecessary
    content words and phrases. However, it is more important that the text is
    useful and easy to understand.

    Avoid addressing the user in the second person. Use a neutral tone or
    passive voice but use a formal address when necessary. Avoid using the
    word \e Please when addressing the user. Exceptions to this are some
    copyright text and short imperative sentences that might otherwise sound
    abrupt. For example, \e {Please wait.}

    Avoid abbreviations in the menu names and items. If there is no room for
    the full spelling or hyphenation of a word, abbreviate the text according
    to the English abbreviation rules.

    Avoid contractions. For example, write \e cannot instead of \e can't.

    \section1 Punctuation

    Avoid using punctuation marks or special characters in menu names and
    items.

    Use punctuation marks as follows:

    \list

        \li  Use full stops in messages.

        \li  Never use full stops (.) at the end of menu item names.

        \li  Place three full stops (...) at the end of menu item names that
             open a dialog requiring user action.

        \li  Use exclamation marks (!) only in text that demands extra attention
             from the user or carries special weight.

        \li  Use quotation marks ("") around variable values. For example,
             \uicontrol {Close Project "qtcreator"}.
             For consistency, use double quotes to emphasize or set apart file
             names, directory names, URLs, and so on, in user visible strings.

        \li  Do not use leading, trailing, or multiple spaces to align text in
             messages, as translation tools might not handle them correctly.

    \endlist

    \section2 Writing Tooltips

    Tooltips contain useful information about icons, menu items, or other UI
    elements. They appear when users place the mouse pointer over an UI
    element. You can also add descriptive text to the UI that is always
    visible.

    For an icon, you can use the command name as a tool tip. In that
    case, use book style capitalization and do not add a period after the tool
    tip.

    \image qtcreator-tooltip.png "Tooltip"

    Tooltips can also contain full sentences. Try to make them as short and
    concise as possible, while still making them grammatically correct. Use
    sentence style capitalization and punctuation as you would for any
    sentence.

    \image qtcreator-tooltip-long.png "Sentence as a tooltip"

    \section3 Writing Tooltips in Design Mode

    In Qt Designer, use plain text for tooltips. For extra formatting, write
    short, canonical HTML in the source tab of the rich text editor:
    \c {<html><head/><body><b>Note:</b> text.}

    Qt Designer has a feature that simplifies the rich text (on by default),
    but still, you should verify by looking at the \uicontrol Source tab.

    \section2 Writing Messages

    Check that messages are concise and economically formulated. However, it
    is more important that the messages are useful and easy to understand.

    Keep the use of many new and different sentence structures to a minimum.
    Reuse sentence structures that have been used in similar situations. For
    example:

    \list

        \li  Cannot send log as selected message type. Text is too long.

        \li  Cannot receive image.

        \li  Cannot insert picture. Maximum text length is 120 characters.

        \li  Image name already in use.

        \li  Folder name already in use.

    \endlist

    \section1 UI Text Capitalization

    Two styles are used, book title and sentence style:

    \list

        \li  Example of Book Title Capitalization

        \li  Example of sentence style capitalization

    \endlist

    \section2 Using Book Style Capitalization

    When using book style capitalization, capitalize all words, except
    prepositions that are shorter than five letters (for example, 'with' but
    'Without'), conjunctions (for example, and, or, but), and articles (a, an,
    the).
    However, always capitalize the first and last word.

    Use book style capitalization for:

    \list

        \li  Titles (window, dialog, group box, tab, list view columns, and so on)

        \li  Functions (menu items, buttons)

        \li  Selectable items (combobox items, listbox items, tree list items, and so on)

    \endlist

    \section3 Checking Book Style Capitalization

    You can use the to-title-case.js script in the \c{\doc\titlecase}
    folder to check book style capitalization of UI text or headings in
    documentation:

    \list 1

        \li  Open to-title-case.html in a browser.

        \li  Enter the UI text in the field.

        \li  Click \uicontrol Convert.

    \endlist

    The UI text with suggested book style capitalization is displayed in the
    field to the right.

    \note The script is based on word lists; it does not perform grammatical
    analysis. Therefore, it might get the capitalization wrong if you use a rare
    meaning of a word. For example, should you mean feathers and not direction
    when you write \e down. However, you should be able to trust it in most
    cases in the context of writing UI text and technical documentation.

    \section2 Using Sentence Style Capitalization

    When using sentence style capitalization, capitalize only the first letter,
    except proper names.

    Use sentence style capitalization for:

    \list

        \li  Labels

        \li  Tool tips

        \li  Descriptive text

        \li  Other non-heading or title text

    \endlist

    \section1 Preparing for Localization

    Qt Creator is localized into several languages. Consistency and conciseness
    make UI text easier to translate.

    \section2 Marking UI Text for Translation

    Make sure the text strings presented to the user are easy to translate.
    The user interface text strings are enclosed in \c tr() calls and
    extracted from the source code during the translation process. Therefore,
    the translator might not know the source code context of the messages.

    You can add comments that are visible in Qt Linguist ( //:) to clarify the
    context. For example:

    \code
    //: Contact book "Add person" button label
    return tr("Add");
    \endcode

    If the class is not Q_OBJECT, use \c {QCoreApplication::translate("class
    context", "message")} or consider using Q_DECLARE_TR_FUNCTIONS. Do not use
    \c {QObject::tr()}, which is confusing because the messages appear grouped
    by class context in Qt Linguist and messages tied to QObject do not have a
    class context.

    Use \c{QDir::toNativeSeparators()} for file and directory names that you
    pass to \c{tr().arg()}.

    Do not use markup that spans the whole string because that can be confusing
    for translators. For example, instead of:

    \code
    tr("<html><head/><body><span>UI Text</span></body></html>")
    \endcode

    use

    \code
    QLatin1String("<html><head/><body><span>") + tr("UI Text") + QLatin1String("/span></body></html>")
    \endcode

    \section2 Features of Languages or Writing Systems

    To allow for localization of your extensions, consider the impact that
    languages and writing systems have on the implementation.

    \table
        \header
            \li Features of Languages or Writing Systems

            \li Impact on Implementation
        \row
            \li Word order

            \li Different languages have different word order rules.

                 Do not use run-time concatenation. Use complete phrases
                 and "%1" formatting instead. For example, use:

                \c{tr("Foo failed: %1").arg(message)}

                 instead of

                 \c {tr("Foo failed: ") + message}
        \row
            \li Singular vs. plural vs. dual forms

            \li Some languages do not have plural form (for example, Chinese
                and Japanese), whereas some have a different form for dual.

                Allow room for text expansion in the layout design. Some
                languages need more space to indicate plurality or duality to
                convey the needed information.

                For example, use

                \c {tr("%n files found", 0, number)}

                instead of

                \c {tr("%1 files found").arg(number)}
        \row
            \li Gender

            \li Some languages have gender (feminine, masculine, neutral),
                whereas some do not (for example, Finnish) or do not use it
                extensively (for example, English).

                 Do not reuse text strings. The same term may not work in
                 another context due to the gender of the base word.

                Articles have a grammatical gender in some languages and
                sentences cannot be as easily constructed as in English. Avoid
                following types of constructs:

                \c {tr("%1 failed").arg(someCondition ? "the operation" : "opening a file")}
    \endtable

    \section1 Common Qt Creator Terms

    This section summarizes the terminology used for common Qt Creator UI
    components. It also describes the conventions for naming different types of
    UI components.

    Always check that the term you plan to use is not used to mean something
    else in the UI. If a suitable term already exists, use it. For example, use
    \e Find for searching and \e New for wizards that create new objects.

    For more information on how to add UI components, see
    \l{Common Extension Tasks}.

    \table
        \header
            \li UI Text

            \li Usage

            \li Conventions

        \row
            \li Context menu

            \li Opens when users right-click on the screen. Contents depend on
                the context.
                \image qtcreator-context-menu.png "Context menu"

            \li You can add menu items that are relevant in a particular
                context. Follow the conventions for naming menu items.

        \row
            \li  Dialog
            \li  User interface element that usually contains a number of
                 choices or allows the user to give input to the application.
                 Opens when users select a menu item or button.
                 \image qtcreator-dialog.png "Dialog"
            \li  Use the menu item or button name as the dialog name. You can
                 also combine the menu item or button name and the name of the
                 object that is managed in the dialog. For example, the \uicontrol Add
                 button in the \uicontrol Documentation options opens the
                 \uicontrol {Add Documentation} dialog.
        \row
            \li Locator

            \li Allows you to browse not only files, but any items defined by
                locator filters.
                \image qtcreator-locator.png "Locator"

            \li You can add locator filters. Check that the filter is not
                already in use and give the filter a descriptive name.

        \row
            \li Menu

            \li Contains menu items that represent commands or options and that
                are logically grouped and displayed. A menu can also contain
                submenus.
                \image qtcreator-menu.png "Menu"

            \li You can create new menus. Use short, but descriptive names that
                are consistent with existing menu names. Use unambigious names.

        \row
            \li  Menu item
            \li  Represents a command or an option for users to choose.
                 \image qtcreator-menu-item.png "Menu item"
            \li  You can add new items to menus. Use short, but descriptive
                 names that are consistent with existing menu names. Use
                 unambigious names.
        \row
            \li  Message box
            \li  Dialog that provides feedback to users, in the form of status
                 information, a warning, or an error message.
                 \image qtcreator-error-message.png "Message box"
                 Output from Qt Creator should be displayed in output views,
                 instead.
            \li  Use the event as the title and provide a solution in the
                 message box.
        \row
            \li  Mode
            \li  Modes correspond to complete screens of controls, specialized
                 for a task.
                 \image qtcreator-mode-selector.png "Mode selector"
            \li  You can add a mode for a new type of editor, for example.
                 Use descriptive, but short mode names. They have to fit in the
                 \uicontrol {Mode selector}.
        \row
            \li  Output
            \li  Views to display output from Qt Creator.
                 \image qtcreator-output-pane.png "Output"
            \li  Use descriptive names for output views.
        \row
            \li  Sidebar
            \li  A view available in the \uicontrol Edit and \uicontrol Debug modes that
                 you can use to browse projects, files, and bookmarks, and to
                 view the class hierarchy.
                 \image qtcreator-sidebar-menu.png "Sidebar"
            \li  You can add views to the sidebar menu. Use descriptive names
                 for them.
        \row
            \li  View
            \li  An area of the screen that displays information for users and
                 provides them with functions for managing the information.
                 Available in \uicontrol Debug mode, for interaction with the program
                 that is running under the control of the debugger.
                 \image qtcreator-debugger-views.png "Views"
            \li  Use descriptive names for views.
    \endtable

*/
