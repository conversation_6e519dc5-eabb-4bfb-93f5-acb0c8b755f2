// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!

//! [debugger-breakpoints]

    \section1 Setting Breakpoints



    You can associate breakpoints with:

    \list

        \li Source code files and lines

        \li Functions

        \li Addresses

        \li Throwing and catching exceptions

        \li Executing and forking processes

        \li Executing some system calls

        \li Changes in a block of memory at a particular address when a
            program is running

        \li Emitting QML signals

        \li Throwing JavaScript exceptions

    \endlist

    The interruption of a program by a breakpoint can be restricted with certain
    conditions.

    Breakpoints come in two varieties: \c unclaimed and \c claimed.
    An unclaimed breakpoint represents a task to interrupt the debugged
    program and passes the control to you later. It has two states:
    \c pending and \c implanted.

    Unclaimed breakpoints are stored as a part of a session and exist
    independently of whether a program is being debugged or not. They
    are listed in the \uicontrol {Breakpoint Preset} view and in the
    editor using the \inlineimage icons/qtcreator-unclaimed-breakpoint-icon.png
    (\uicontrol {Unclaimed Breakpoint}) icon, when they refer to a position
    in code.

    \image qtcreator-debugger-breakpoint-preset.png "Breakpoint Preset" view

    When a debugger starts, the debugging backend identifies breakpoints
    from the set of unclaimed breakpoints that might be handled by the
    debugged program and claims them for its own exclusive use. Claimed
    breakpoints are listed in the \uicontrol {Breakpoints} view of the
    running debugger. This view only exists while the debugger is running.

    When a debugger claims a breakpoint, the unclaimed breakpoint disappears
    from the \uicontrol {Breakpoint Preset} view, to appear as a pending
    breakpoint in the \uicontrol Breakpoints view.

    At various times, attempts are made to implant pending breakpoints into
    the debugged process. Successful implantation might create one or more
    implanted breakpoints, each associated with an actual address in
    the debugged breakpoint. The implantation might also move a breakpoint
    marker in the editor from an empty line to the next line for which the
    actual code was generated, for example. Implanted breakpoint icons don't
    have the hourglass overlay.

    When the debugger ends, its claimed breakpoints, both pending and
    implanted, will return to the unclaimed state and re-appear in the
    \uicontrol{Breakpoint Preset} view.

    When an implanted breakpoint is hit during the execution of the
    debugged program, control is passed back to you.
    You can then examine the state of the interrupted program, or
    continue execution either line-by-line or continuously.

    \image qtcreator-debug-breakpoints.png "Breakpoints view"

    \section2 Adding Breakpoints

    To add breakpoints:

    \list 1

        \li Add a new breakpoint in one of the following ways:

            \list

                \li In the code editor, click the left margin or press \key F9
                    (\key F8 on \macos) on a particular line you want the
                    program to stop.

                \li In the \uicontrol {Breakpoint Preset} view or the
                    \uicontrol Breakpoints view:

                    \list
                        \li Double-click the empty part of the view.
                        \li Right-click the view, and select
                            \uicontrol {Add Breakpoint} in the context menu.
                    \endlist
            \endlist

        \li In the \uicontrol {Breakpoint type} field, select the location in the
            program code where you want the program to stop. The other options
            to specify depend on the selected location.

            \image qtcreator-add-breakpoint.png "Add Breakpoints" dialog

        \li In the \uicontrol Condition field, set the condition to be evaluated
            before stopping at the breakpoint if the condition evaluates as
            true.

        \li In the \uicontrol Ignore field, specify the number of times that the
            breakpoint is ignored before the program stops.

        \li In the \uicontrol Commands field, specify the commands to execute
            when the program stops; one command on a line. GDB executes the
            commands in the order in which they are specified.

    \endlist

    \if defined(qtcreator)
    \section2 Specifying Breakpoint Settings

    You can specify settings for breakpoints in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Debugger. For more information,
    see \l{Specifying Debugger Settings}.

    To use a full absolute path in breakpoints, select the
    \uicontrol {Set breakpoints using a full absolute path} check box.

    GDB and CDB enable setting breakpoints on source lines for which no code
    was generated. In such situations, the breakpoint is shifted to the next
    source code line for which the code was actually generated. To reflect
    such temporary changes by moving the breakpoint markers in the source code
    editor, select \uicontrol GDB > \uicontrol {Adjust breakpoint locations}
    or \uicontrol CDB > \uicontrol {Correct breakpoint location}.

    When using GDB as backend, you can extend the ordinary GDB
    breakpoint class by using Python. Select \uicontrol GDB >
    \uicontrol {Use pseudo message tracepoints}.

    When using CDB as backend, you can specify that the debugger should break on
    specific events, such as C++ exceptions, thread creation or exit, loading or
    unloading \l{Viewing Modules}{application modules}, or particular output.
    Select the appropriate check boxes in the \uicontrol CDB >
    \uicontrol {Break on} group. To disable first-chance break on access
    violation exceptions, select the \uicontrol {Ignore first chance access
    violations} check box. The second occurrence of an access violation will
    break into the debugger.

    You can automatically add breakpoints on some functions to catch error
    and warning messages. For more information, see \l{Specifying CDB Settings}
    and \l{Specifying Extended GDB Settings}.

    For more information on breakpoints, see
    \l{http://sourceware.org/gdb/onlinedocs/gdb/Breakpoints.html#Breakpoints}
    {Breakpoints, Watchpoints, and Catchpoints} in GDB documentation.
    \endif

    \section2 Moving Breakpoints

    To move a breakpoint:

    \list
        \li Drag and drop a breakpoint marker to another line
            in the text editor.
        \li In the \uicontrol {Breakpoint Preset} view or the
             \uicontrol Breakpoints view, select
             \uicontrol {Edit Selected Breakpoints}, and set the
             line number in the \uicontrol {Line number} field.
    \endlist

    \section2 Deleting Breakpoints

    To delete breakpoints:

    \list

        \li Click the breakpoint marker in the text editor.

        \li In the \uicontrol {Breakpoint Preset} view or the
            \uicontrol Breakpoints view:
            \list
                \li Select the breakpoint and press \key Delete.
                \li Select \uicontrol {Delete Selected Breakpoints},
                    \uicontrol {Delete Selected Breakpoints}, or
                    \uicontrol {Delete Breakpoints of File} in the
                    context menu.
            \endlist

    \endlist

    \section2 Enabling and Disabling Breakpoints

    To temporarily disable a breakpoint without deleting it and losing associated
    data like conditions and commands:

    \list

        \li Right-click the breakpoint marker in the text editor and select
            \uicontrol{Disable Breakpoint}.

        \li Select a line that contains a breakpoint and press \key Ctrl+F9
            (\key Ctrl+F8 on \macos).

        \li In the \uicontrol {Breakpoint Preset} view or the
            \uicontrol Breakpoints view:
            \list
                \li Select the breakpoint and press \key Space.
                \li Select \uicontrol {Disable Breakpoint} in the context menu.
            \endlist
    \endlist

    A hollow breakpoint icon in the text editor and the views indicates a
    disabled breakpoint. To re-enable a breakpoint, use any of the above
    methods.

    With the notable exception of data breakpoints, breakpoints retain their
    enabled or disabled state when the debugged program is restarted.

    \section2 Setting Data Breakpoints

    A \e {data breakpoint} stops the program when data is read or written at the
    specified address.

    To set a data breakpoint at an address:

    \list 1

        \li In the \uicontrol {Breakpoint Preset} or \uicontrol Breakpoints
            view, select \uicontrol {Add Breakpoint} in the context menu.

        \li In the \uicontrol {Breakpoint type} field, select
            \uicontrol {Break on data access at fixed address}.

        \li In the \uicontrol Address field, specify the address of the memory
            block.

        \li Select \uicontrol OK.

    \endlist

    If the address is displayed in the \uicontrol {Locals} or
    \uicontrol {Expressions} view, you can select
    \uicontrol {Add Data Breakpoint at Object's Address} in the
    context menu to set the data breakpoint.

    Data breakpoints will be disabled when the debugged program exits, as it
    is unlikely that the used addresses will stay the same at the next program
    launch. If you really want a data breakpoint to be active again, re-enable
    it manually.

//! [debugger-breakpoints]

//! [debugger-call-stack-trace]

    \section1 Viewing Call Stack Trace

    When the program being debugged is interrupted, \QC displays the nested
    function calls leading to the current position as a call stack trace. This
    stack trace is built up from call stack frames, each representing a
    particular function. For each function, \QC tries to retrieve the file name
    and line number of the corresponding source file. This data is shown in the
    \uicontrol Stack view.

    \image qtcreator-debug-stack.png

    Since the call stack leading to the current position may originate or go
    through code for which no debug information is available, not all stack
    frames have corresponding source locations. Stack frames without
    corresponding source locations are grayed out in the \uicontrol Stack view.

    If you click a frame with a known source location, the text editor jumps to
    the corresponding location and updates the \uicontrol {Locals} and
    \uicontrol {Expressions} views, making it seem like the program
    was interrupted before entering the function.

    To find out which QML file is causing a Qt Quick 2 application to crash,
    select \uicontrol {Load QML Stack} in the context menu in the
    \uicontrol Stack view. The debugger tries to retrieve the JavaScript stack
    from the stopped executable and prepends the frames to the C++ frames,
    should it find any. You can click a frame in the QML stack to open the QML
    file in the editor.

//! [debugger-call-stack-trace]

//! [debugger-locals]

    \section1 Local Variables and Function Parameters

    The Locals view consists of the \uicontrol Locals pane and the
    \uicontrol {Return Value} pane (hidden when empty).

    \image qtcreator-locals.png "Locals view"

    Whenever a program stops under the control of the debugger, it retrieves
    information about the topmost stack frame and displays it in the
    \uicontrol {Locals} view. The \uicontrol Locals pane shows
    information about parameters of the function in that frame as well as the
    local variables. If the last operation in the debugger was returning from a
    function after pressing \key {Shift+F11}, the \uicontrol {Return Value}
    pane displays the value returned by the function.

    When using GDB, you can specify whether the dynamic or the static type of
    objects will be displayed. Select \uicontrol {Use dynamic object type for
    display} in the context menu. Keep in mind that choosing the dynamic type
    might be slower.

//! [debugger-locals]

//! [debugger-expressions]

    \section1 Evaluating Expressions

    To compute values of arithmetic expressions or function calls, use
    expression evaluators in the \uicontrol Expressions view. To insert a new
    expression evaluator, either double-click on an empty part of the
    \uicontrol {Expressions} or \uicontrol {Locals} view, or select
    \uicontrol {Add New Expression Evaluator} from the context menu, or drag and
    drop an expression from the code editor.

    \image qtcreator-debugger-expressions.png

    \note Expression evaluators are powerful, but slow down debugger operation
    significantly. It is advisable to not use them excessively, and to remove
    unneeded expression evaluators as soon as possible.

    Expression evaluators are re-evaluated whenever the current frame changes.
    Note that functions used in the expressions are called each time, even if
    they have side-effects.

    The QML debugger can evaluate JavaScript expressions.

//! [debugger-expressions]

//! [debugger-expressions-cpp]

    GDB, LLDB and CDB support the evaluation of simple C and C++ expressions.
    Functions can be called
    only if they are actually compiled into the debugged executable or a library
    used by the executable. Most notably, inlined functions such as most
    \c{operator[]} implementations of standard containers are typically \e{not}
    available.

    When using GDB or LLDB as backend, a special ranged syntax can be used to
    display multiple values with one expression. A sub-expression of form
    \c{foo[a..b]} is split into a sequence of individually evaluated expressions
    \c{foo[a], ..., foo[b]}.

    Compound variables of struct or class type are displayed as expandable in
    the view. Expand entries to show all members. Together with the display of
    value and type, you can examine and traverse the low-level layout of object
    data.

    \table
        \row
            \li \b{Note:}

        \row
            \li GDB and LLDB, and therefore \QC's debugger, also work for optimized
                builds on Linux and \macos. Optimization can lead to re-ordering
                of instructions or removal of some local variables, causing the
                \uicontrol {Locals} and \uicontrol {Expressions} view to show
                unexpected data.
        \row
            \li The debug information provided by GCC does not include enough
                information about the time when a variable is initialized.
                Therefore, \QC can not tell whether the contents of a local
                variable contains "real data", or "initial noise". If a QObject
                appears uninitialized, its value is reported as
                \uicontrol {not in scope}. Not all uninitialized objects,
                however, can be recognized as such.
    \endtable

    \note The set of evaluated expressions is saved in your session.

//! [debugger-expressions-cpp]

//! [debugger-qt-basic-objects]

    \section1 Inspecting Basic Qt Objects

    The \uicontrol {Locals} and \uicontrol {Expressions} views also provide access
    to the most powerful feature of the debugger: comprehensive display of data
    belonging to Qt's basic objects. For example, in case of QObject, instead of
    displaying a pointer to some private data structure, you see a list of
    children, signals, and slots.

    Similarly, instead of displaying many pointers and integers, \QC's debugger
    displays the contents of a QHash or QMap in an orderly manner. Also, the
    debugger displays access data for QFileInfo and provides access to the
    \e real contents of QVariant.

    Right-click in the \uicontrol {Locals} or the \uicontrol {Expressions} view
    to open a context menu that provides additional options for viewing data. The
    available options depend on the type of the current items, and are provided
    by the \l{Using Debugging Helpers}{Debugging Helpers}. Typically,
    string-like data, such as \c{QByteArray} and \c{std::string}, offer a
    selection of encodings, as well as the possibility to use a full editor
    window. Map-like data, such as \c{QMap}, \c{QHash}, and \c{std::map}, offer
    a compact option using the \c{name} column for keys, resulting in a concise
    display of containers with short keys, such as numbers or short strings. For
    example, to expand all the values of QMap, select
    \uicontrol {Change Value Display Format} > \uicontrol Compact.

    You can use the \uicontrol {Locals} and \uicontrol {Expressions} view to change
    the contents of variables of simple data types, for example, \c int, \c float,
    \c QString and \c std::string when the program is interrupted. To do so,
    click the \uicontrol Value column, modify the value with the inplace editor,
    and press \key Enter (or \key Return).

    To change the complete contents of QVector or \c std::vector values, type
    all values separated by commas into the \uicontrol Value column of the main
    entry.

    You can enable tooltips in the main editor displaying this information.
    For more information, see \l{See the value of variables in tooltips while debugging}.

//! [debugger-qt-basic-objects]
*/
