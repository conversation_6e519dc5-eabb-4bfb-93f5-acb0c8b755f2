// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-commercial-overview.html

    \title Commercial Features

    \commercial

    You can use the following \QC features if you have the appropriate
    \l{http://qt.io/licensing/}{Qt license}:

    \list
        \li \l{https://doc.qt.io/Boot2Qt/index.html}{Boot2Qt}
        \li \l{https://doc.qt.io/qtcreator/creator-overview-qtasam.html}
            {Qt Application Manager} integration
    \endlist
*/
