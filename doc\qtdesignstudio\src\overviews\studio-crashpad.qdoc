// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-crashpad.html
    \previouspage studio-user-feedback.html
    \nextpage studio-packaging.html

    \title Reporting Crashes

    You can enable \QDS to report crashes automatically. \QDS uses Google
    Crashpad to collect crashes and report them to the Sentry backend storage
    for processing. The purpose of Crashpad is to capture application state in
    sufficient detail to allow developers to diagnose and, where possible, fix
    the issue causing the crash. Crashpad may capture arbitrary contents from
    the memory of a crashed process, including user sensitive information, URLs,
    and other content provided by the users. The collected reports are used for
    the sole purpose of fixing bugs. For more information on Crashpad, see the
    \l {https://chromium.googlesource.com/crashpad/crashpad/+/master/doc/overview_design.md}
    {documentation} by Google. For more information on processing and storing
    of the collected data, see \l {https://sentry.io/security/}
    {Security & Compliance} by Sentry.

    To enable sending crash reports, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Environment > \uicontrol System
    (\uicontrol {Qt Design Studio} > \uicontrol Preferences > \uicontrol
    Environment > \uicontrol System on \macos), and then select
    \uicontrol {Enable crash reporting}.

    Since crash reports take up disk space, you may wish to remove them when
    they are no longer needed. Select \uicontrol {Clear local crash reports} to
    remove the crash report data.

    \image studio-crashpad-checkbox.png "Checkbox for enabling crash reporting"

    \note Crashpad is currently only supported on Windows and \macos.
*/
