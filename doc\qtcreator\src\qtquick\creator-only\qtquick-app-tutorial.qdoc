// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-tutorials.html
    \example transitions
    \nextpage creator-writing-program.html

    \title Creating a Qt Quick Application

    This tutorial illustrates basic concepts of \l{Qt Quick}. For more
    information about the UI choices you have, see \l{User Interfaces}.

    This tutorial describes how to use \QC to implement \l{Qt Quick States}
    {states} and \l{Animation and Transitions in Qt Quick}{transitions}
    when using Qt 6 as the minimum Qt version and CMake as the build system.

    We use the \l{Working in Edit Mode}{Edit mode} to create an application
    that moves a Qt logo between three rectangles when you click them.

    \image qtquick-tutorial.gif "Transitions example"

    For more examples, see \l{Qt Quick Examples and Tutorials}.

    You can develop Qt Quick applications also in \QDS. For more information,
    see \l{Qt Design Studio Manual}.

    \include qtquick-tutorial-create-empty-project.qdocinc qtquick empty application

    \QC generates a component file, \e main.qml, and opens it in the
    \uicontrol Edit mode.

    \section1 Deploying Applications

    The main view of the application displays a Qt logo inside a rectangle in
    the top left corner of the view and two empty rectangles.

    We use the \e qt-logo.png image in this tutorial, but you can also use
    any other image or a component, instead.

    For the image to appear when you run the application, you must specify it
    as a resource in the \c RESOURCES section of \e CMakeLists.txt file that
    the wizard created for you:

    \quotefromfile transitions/CMakeLists.txt
    \skipto qt_add_qml_module
    \printuntil )

    \section1 Creating Custom QML Types

    Because the \l Window QML type requires that you add states into child
    components, we use the wizard to create a custom QML type called
    \e Page that we will refer to from \e main.qml.

    To create a custom QML type:

    \list 1
        \li Select \uicontrol File > \uicontrol {New File} >
            \uicontrol Qt >
            \uicontrol {QML File (Qt Quick 2)}.
        \li Select \uicontrol Choose to open the \uicontrol Location dialog.
        \li In the \uicontrol {File name} field, enter a name for the custom
            QML type. In this example, we call the type \e Page.
        \li Select \uicontrol Next to open the \uicontrol {Project Management}
            dialog.
        \li Select \uicontrol Finish to create \e Page.qml.
    \endlist

    \QC opens \e Page.qml in the \uicontrol Edit mode. It contains a root item
    of the type \l Item that we replace with a \l Rectangle type. We give the
    type the ID \e page, anchor it to the parent item on all sides, and set
    its color to white:

    \quotefromfile transitions/Page.qml
    \skipto import
    \printuntil color

    Because we develop with Qt 6, where version numbers are not used with
    modules, we remove the version number from the import statement.

    When you start typing the QML type name, \QC suggests available types
    and properties to \l{Completing Code}{complete the code}.

    Select the light bulb icon \inlineimage icons/refactormarker.png
    next to the type name to open the \l{Editing Rectangles}
    {Qt Quick Toolbar for rectangles}. You can use it to specify
    rectangle properties, such as color, transparency, and gradients.

    \image qml-toolbar-rectangle.png "Qt Quick Toolbar for rectangles"

    Next, we add an \l Image type with \e qt-logo.png as the source. We
    position the image in the top-left corner of the rectangle:

    \printuntil }

    You can use the \l{Previewing Images}{Qt Quick Toolbar for images} to
    specify image properties, such as source file and fill mode.

    \image qml-toolbar-image.png "Logo visible in Qt Quick Toolbar for images"

    We now create the rectangles that the image will move between. Their size
    should match the image size and they should be transparent, so that the
    image is visible. We set the border color to light gray to make the
    rectangles visible:

    \printuntil border.color

    We anchor the rectangles to their parent to position them in its
    top-left and and bottom-left corners, as well as the vertical center
    of its right edge. The following code snippet anchors a rectangle to
    the top-left corner of its parent:

    \printuntil anchors.topMargin

    We add a \l MouseArea type to make the rectangle clickable by users:

    \printuntil anchors.fill

    To check your code, you can compare it with the \e {Page.qml} example file.

    Next, we will make the image move between the rectangles when users click
    them, by adding states and by connecting mouse clicks to state changes.

    \section1 Connecting Mouse Clicks to State Changes

    To make the image move between the rectangles when users click them, we add
    states to the Page component, where we change the values of the \c x and
    \c y properties of \e icon to match those of the middle right and top left
    rectangles. To make sure that the image stays within the rectangle
    when the view is scaled on different sizes of screens, we \e bind the values
    of the \c x and \c y properties of \e icon to those of the rectangles:

    \dots
    \skipto states:
    \printuntil ]

    Then, we connect the \c onClicked signals of the mouse areas to the state
    changes:

    \quotefromfile transitions/Page.qml
    \skipto Connections {
    \printuntil }

    Because we develop with Qt 6, we must specify the connections as functions.

    \section1 Adding Page to the Main View

    We now open \e main.qml for editing and add an instance of the Page custom
    component to it:

    \quotefromfile transitions/main.qml
    \skipto import
    \printuntil /^\ {0}\}/

    Press \key {Ctrl+R} to run the application, and click the rectangles to
    move the Qt logo from one rectangle to another.

    \section1 Animating Transitions

    We will now create transitions to apply animation to the image. For example,
    the image bounces back when it moves to \e middleRightRect and eases into
    \e bottomLeftRect.

    We specify transitions for switching from each state to the other two
    states:

    \quotefromfile transitions/Page.qml
    \skipto transitions:
    \printuntil },

    We change the easing curve type for transitions to \e State2 from linear to
    \c Easing.OutBounce to create the bounce effect:

    \printuntil },

    \note You can use the \l{Previewing Animation}
    {Qt Quick Toolbar for animation} to specify the
    easing curve type and animation duration.

    \image qml-toolbar-animation.png "Qt Quick Toolbar for animation"

    Then, we change the easing curve type for transitions to \e State2 from
    linear to \c Easing.InOutQuad to create the easing effect:

    \printuntil /^\ {0}\}/

    Press \key {Ctrl+R} to run the application, and click the rectangles to
    view the animated transitions.
*/
