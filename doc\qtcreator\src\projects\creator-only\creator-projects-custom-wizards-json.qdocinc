// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [json wizards]
    \section1 Integrating Wizards into Builds

    To integrate the wizard into \QC and to deliver it as part of the \QC build,
    place the wizard files in the \QC sources. Then select \uicontrol Build >
    \uicontrol {Run CMake} or \uicontrol {Run qmake}, depending on the build
    system you use. This ensures that the new files you added for your wizard are
    actually copied from the \QC source directory into the \QC build directory
    as part of the next \QC build.

    If you do not run CMake or qmake, your new wizard will not show up because
    it does not exist in the build directory you run your newly built \QC from.
    It never got copied there because <PERSON><PERSON><PERSON> or q<PERSON><PERSON> did not inform the
    build tool, such as make or ninja, about the new files in the source tree.

    Basically, CMake and qmake generate a fixed list of files to copy from the
    source directory to the subdirectory of the build directory that is checked
    for wizards at runtime. Therefore, you need to run CMake or qmake or execute
    the \uicontrol {Factory.Reset} function each time the names or locations of
    the files change.

    \section1 Using Variables in Wizards

    You can use variables (\c {%\{<variableName>\}}) in strings in the JSON configuration
    file and in template source files.
    A set of variables is predefined by the wizards and their pages.
    You can introduce new variables as shortcuts for later use by
    defining the variable key names and values in the \c options section in the
    \c {wizard.json} file.

    There is a special variable \c {%\{JS:<JavaScript expression>\}} which evaluates the given
    JavaScript expression and converts the resulting JavaScript value to a string.
    In the JavaScript expression you can refer to variables defined by the wizard with
    \c {value('<variableName>')}. The returned JavaScript object has the type that the value
    of the variable has, which can be a string, list, dictionary, or boolean.

    In places where a boolean value is expected and a string is given,
    an empty string as well as the string \c {"false"} is treated as
    \c false and anything else as \c true.

    \section1 Localizing Wizards

    If a setting name starts with the \c tr prefix, the value is visible to
    users and should be translated. If the new wizard is included in the \QC
    sources, the translatable strings appear in the \QC translation files and
    can be translated as a part of \QC. Alternatively, you can place the
    translations in the .json file using the following syntax:

    \code
    "trDisplayName": { "C": "default", "en_US": "english", "de_DE": "deutsch" }
    \endcode

    For example:

    \code
    "trDisplayName": { "C": "Project Location", "en_US": "Project Location", "de_DE": "Projekt Verzeichnis" }
    \endcode

    \section1 Creating Wizards

    \QC contains wizards for adding classes, files, and projects. You can
    use them as basis for adding your own wizards. We use the C++ wizard
    to explain the process and the sections and settings in the .json file.

    In this example, we create the wizard directory in the shared directory
    and integrate it in the \QC build system, so that it can deployed along with
    the \QC binaries as part of the build.

    \image qtcreator-cpp-class-wizard.png

    For more information about the pages and widgets that you can add and their
    supported properties, see \l {Available Pages} and \l{Available Widgets}.

    To create a JSON-based C++ class wizard:

    \list 1

        \li Start \QC with the \c {-customwizard-verbose} argument to receive
            feedback during wizard development. For more information, see
            \l {Verbose Output}.

        \li Set keyboard shortcuts for the \uicontrol Inspect and
            \uicontrol {Factory.Reset} actions, as described in
            \l {Tips for Wizard Development}.

        \li Make a copy of \c {share/qtcreator/templates/wizards/classes/cpp}
            and rename it. For example,
            \c {share/qtcreator/templates/wizards/classes/mycpp}

        \li Use the \uicontrol {Factory.Reset} action to make the wizard appear
            in \uicontrol File > \uicontrol {New File} without
            restarting \QC.

        \li Open the wizard configuration file, \c {wizard.json} for editing:

            \list

                \li The following settings determine the type of the wizard and
                    its place in the \uicontrol {New File} dialog:

                    \code
                    "version": 1,
                    "supportedProjectTypes": [ ],
                    "id": "A.Class",
                    "category": "O.C++",
                    \endcode

                \list

                    \li \c version is the version of the file contents. Do not
                        modify this value.

                    \li \c supportedProjectTypes is an optional setting that
                        can be used to filter wizards when adding a new build
                        target to an existing project. For example, only wizards
                        that produce qmake projects should be provided when
                        adding a new target to an existing qmake project.

                        Possible values are the build systems supported by \QC
                        or \c UNKNOWN_PROJECT if the build system is not
                        specified: \c AutotoolsProjectManager.AutotoolsProject,
                        \c CMakeProjectManager.CMakeProject,
                        \c GenericProjectManager.GenericProject,
                        \c PythonProject, \c Qbs.QbsProject,
                        \c Qt4ProjectManager.Qt4Project (qmake project),
                        \c QmlProjectManager.QmlProject

                    \li \c id is the unique identifier for your wizard. Wizards
                        are sorted by the ID in alphabetic order within the
                        \c category. You can use a leading letter to specify the
                        position of the wizard. You must always change
                        this value. For example, \c B.MyClass.

                        This information is available in the wizard as
                        \c {%\{id\}}.

                    \li \c category is the category in which to place the wizard
                        in the list. You can use a leading letter to specify the
                        position of the category in the list in the
                        \uicontrol {New File} dialog.

                        This information is available in the wizard as
                        \c {%\{category\}}.
                \endlist

            \li The following settings specify the icon and text that appear in
                the \uicontrol {New File} dialog:

                \code
                "trDescription": "Creates a C++ header and a source file for a new class that you can add to a C++ project.",
                "trDisplayName": "C++ Class",
                "trDisplayCategory": "C++",
                "iconText": "h/cpp",
                "enabled": "%{JS: value('Plugins').indexOf('CppEditor') >= 0}",
                \endcode

            \list

                \li \c trDescription appears in the right-most panel when
                    \c trDisplayCategory is selected.

                    This information is available in the wizard as
                    \c {%\{trDescription\}}.

                \li \c trDisplayName appears in the middle panel when
                    \c trDisplayCategory is selected.

                    This information is available in the wizard as
                    \c {%\{trDisplayName\}}.

                \li \c trDisplayCategory appears in the \uicontrol {New File}
                    dialog, under \uicontrol {Files and Classes}.

                    This information is available in the wizard as
                    \c {%\{trDisplayCategory\}}.

                \li \c icon appears next to the \c trDisplayName in the middle
                    panel when \c trDisplayCategory is selected. We recommend
                    that you specify the path relative to the wizard.json file,
                    but you can also use an absolute path. Omit this value to
                    use the default icon for the wizard type.

                \li \c iconText determines the text overlay for the default
                    file icon.

               \li \c iconKind determines whether the icon is themed.

                \li \c image specifies a path to an image (for example a
                    screenshot) that appears below the \c trDescription.

                \li \c featuresRequired specifies the \QC features that the
                    wizard depends on. If a required feature is missing, the
                    wizard is hidden. For example, if no kit has a Qt version
                    set, then the qmake-based wizards are hidden.

                    Use \c enabled if you need to express more complex logic to
                    decide whether or not your wizard will be available.

                    This information is available in the wizard as
                    \c {%\{RequiredFeatures\}}.

                \li \c featuresPreferred specifies the build and run kits to
                    preselect.

                    This information is available in the wizard as
                    \c {%\{PreferredFeatures\}}.

                \li \c platformIndependent is set to \c true if the wizard is
                    supported by all target platforms. By default, it is set to
                    \c{false}.

                \li \c enabled is evaluated to determine whether a wizard is
                    listed in \uicontrol File > \uicontrol {New Project} or
                    \uicontrol {New File}, after \c featuresRequired has been
                    checked.

                    The default value is \c true.

            \endlist

            \li The \c options section contains an array of objects with \e key
                and \e value attributes. You can define your own variables to
                use in the configuration and template source files, in addition
                to the predefined variables. For example, the C++ class creation
                wizard uses the following variables:

                \code
                "options":
                [
                    { "key": "TargetPath", "value": "%{Path}" },
                    { "key": "HdrPath", "value": "%{Path}/%{HdrFileName}" },
                    { "key": "SrcPath", "value": "%{Path}/%{SrcFileName}" },
                    { "key": "CN", "value": "%{JS: Cpp.className(value('Class'))}" },
                    { "key": "Base", "value": "%{JS: value('BaseCB') === '' ? value('BaseEdit') : value('BaseCB')}" },
                    { "key": "isQObject", "value": "%{JS: (value('Base') === 'QObject' || value('Base') === 'QWidget' || value('Base') === 'QMainWindow' || value('Base') === 'QDeclarativeItem' || value('Base') === 'QQuickItem' ) ? 'true' : 'false'}" },
                    { "key": "GUARD", "value": "%{JS: Cpp.classToHeaderGuard(value('Class'), Util.suffix(value('HdrFileName'))}" },
                    { "key": "SharedDataInit", "value": "%{JS: value('IncludeQSharedData') ? 'data(new %{CN}Data)' : '' }" }
                ],
                \endcode

                This section is optional. For more examples of variables, see
                the \c {wizard.json} files for other wizards.

            \li The \c pages section specifies the wizard pages. The pages
                used depend on the wizard type. You can add standard pages to
                wizards or create new pages using the available widgets. The
                following settings specify the display name, title, and type of
                the page:

                \code
                "pages":
                [
                    {
                        "trDisplayName": "Define Class",
                        "trShortTitle": "Details",
                        "typeId": "Fields",
                        "data" :
                        [
                            {
                                "name": "Class",
                                "trDisplayName": "Class name:",
                                "mandatory": true,
                                "type": "LineEdit",
                                "data": {
                                    "trPlaceholder": "Fully qualified name, including namespaces",
                                    "validator": "(?:(?:[a-zA-Z_][a-zA-Z_0-9]*::)*[a-zA-Z_][a-zA-Z_0-9]*|)",
                                    "completion": "namespaces"
                                    }
                    },
                            ...
                ]
                \endcode

                \list

                    \li \c typeId specifies the page to use: \c Fields, \c File,
                        \c Form, \c Kits, \c Project, \c VcsConfiguration,
                        \c VcsCommand or \c Summary.

                        Full page ID, as used in the code, consists of the
                        \c typeId prefixed with \c {"PE.Wizard.Page."}. For more
                        information, about the pages, see \l{Available Pages}.

                    \li \c trDisplayName specifies the title of the page. By
                        default, the page title is used.

                    \li \c trShortTitle specifies the title used in the sidebar
                        of the Wizard. By default, the page title is used.

                    \li \c trSubTitle specifies the subtitle of the page. By
                        default, the page title is used.

                    \li \c index is an integer value that specifies the page ID.
                        It is automatically assigned if you do not set it.

                    \li \c enabled is set to \c true to show the page and to
                        \c false to hide it.

                    \li \c data specifies the wizard pages. In the C++ wizard,
                        it specifies a \c Fields page and a \c Summary page. The
                        \c Fields page contains the \c CheckBox, \c ComboBox,
                        \c LineEdit, \c PathChooser, and \c Spacer widgets. For
                        more information about the widgets, see
                        \l{Available Widgets}.

                \endlist

            \li The \c generators section specifies the files to be added to the
                project:

                \code
                "generators":
                [
                    {
                        "typeId": "File",
                        "data":
                        [
                            {
                                "source": "file.h",
                                "target": "%{HdrPath}",
                                "openInEditor": true
                                "options": [
                                    { "key": "Cpp:License:FileName", "value": "%{HdrFileName}" },
                                    { "key": "Cpp:License:ClassName", "value": "%{CN}" }
                                ]
                            },
                            {
                                "source": "file.cpp",
                                "target": "%{SrcPath}",
                                "openInEditor": true
                                "options": [
                                    { "key": "Cpp:License:FileName", "value": "%{SrcFileName}" },
                                    { "key": "Cpp:License:ClassName", "value": "%{CN}" }
                                ]
                            }
                ]
                \endcode

                \list

                   \li \c typeId specifies the type of the generator. Currently,
                        only \c File or \c Scanner is supported.

                    \li \c data allows to configure the generator further.
                \endlist

          \endlist

    \endlist

    \section1 Values Available to the Wizard

    In addition to properties taken from the \c {wizard.json} file itself (see
    \l{Creating Wizards}), \QC makes some information available to all JSON
    based wizards:

    \list
        \li \c WizardDir is the absolute path to the \c {wizard.json} file.

        \li \c Features lists all features available via any of the kits
            configured in \QC.

        \li \c Plugins contains a list of all plugins running in the current
            instance of \QC.

        \li \c Platform contains the platform selected in the \uicontrol File >
            \uicontrol {New Project} or \uicontrol {New File} dialog. This value
            may be empty.
    \endlist

    The following information is only available when users trigger the wizard
    via the context menu of a node in the \uicontrol Projects view:

    \list
        \li \c InitialPath with the path to the selected node.

        \li \c ProjectExplorer.Profile.Ids contains a list of Kits configured
            for the project of the selected node.
    \endlist

    \section1 Available Pages

    You can add predefined pages to wizards by specifying them in the \c pages
    section of a wizard.json file.

    \section2 Field Page

    A Field page has the \c typeId value \c Field and contains widgets. For more
    information about widget definitions, see \l{Available Widgets}.

    \code
    "pages":
    [
        {
            "trDisplayName": "Define Class",
            "trShortTitle": "Details",
            "typeId": "Fields",
            "data" :
            [
                {
                    "name": "Class",
                    "trDisplayName": "Class name:",
                    "mandatory": true,
                    "type": "LineEdit",
                    "data": {
                        "trPlaceholder": "Fully qualified name, including namespaces",
                        "validator": "(?:(?:[a-zA-Z_][a-zA-Z_0-9]*::)*[a-zA-Z_][a-zA-Z_0-9]*|)",
                        "completion": "namespaces"
                        }
                },
                ...
    ],
    \endcode

    \section2 File Page

    A File page has the \c typeId value \c File. You can leave out the \c data
    key or assign an empty object to it.

    \code
    {
        "trDisplayName": "Location",
        "trShortTitle": "Location",
        "typeId": "File"
    },
    \endcode

    The page evaluates \c InitialFileName and \c InitialPath from the wizard to
    set the initial path and filename. The page sets \c TargetPath to the full
    path of the file to be created.

    \section2 Form Page

    A Form page has the \c typeId value \c Form. You can leave out the \c data
    key or assign an empty object to it.

    \code
    {
        "trDisplayName": "Choose a Form Template",
        "trShortTitle": "Form Template",
        "typeId": "Form"
    },
    \endcode

    The page sets \c FormContents to an array of strings with the form contents.

    \section2 Kits

    A Kits page has the \c typeId value \c Kits. The \c data section of a Kits
    page contains an object with the following settings:

    \list
        \li \c projectFilePath with the path to the project file.

        \li \c requiredFeatures with a list of strings or objects that describe
            the features that a kit must provide to be listed on the page.

            When a string is found, this feature must be set. When using an
            object instead, the following settings are checked:

            \list
                \li \c feature, which must be a string (that will have all
                    \c {%\{<VariableName\}} expanded).

                \li \c condition, which must evaluate to \c true or \c false and
                    can be used to discount the feature from the list.
            \endlist

        \li \c preferredFeatures with a list in the same format as
            \c requiredFeatures. Any kit matching all features listed in
            \c preferredFeatures (in addition to \c requiredFeatures) will be
            pre-selected on this page.
    \endlist

    \code
    {
        "trDisplayName": "Kit Selection",
        "trShortTitle": "Kits",
        "typeId": "Kits",
        "enabled": "%{IsTopLevelProject}",
        "data": { "projectFilePath": "%{ProFileName}" }
    },
    \endcode

    The page evaluates \c {%\{Platform\}} to set the platform selected in
    \uicontrol File > \uicontrol {New Project} or \uicontrol {New File}.



    \section2 Project

    A Project page has the \c typeId value \c Project. It contains no data or an
    object with the \c trDescription property which will be shown on the
    generated page. \c trDescription defaults to \c {%\{trDescription\}}, which
    is filled in with the information taken from the \c trDescription
    field of the \c {wizard.json} file.

    \code
    {
        "trDisplayName": "Project Location",
        "trShortTitle": "Location",
        "typeId": "Project",
        "data": { "trDescription": "A description of the wizard" }
    },
    \endcode

    The page evaluates \c InitialPath to set the initial project path. The page
    sets \c ProjectDirectory and \c TargetPath to the project directory.

    \section2 Summary

    A Summary page has the \c typeId value \c Summary. It contains no data or
    an empty object.

    \code
    {
        "trDisplayName": "Project Management",
        "trShortTitle": "Summary",
        "typeId": "Summary"
    }
    \endcode

    The page sets \c IsSubproject to an empty string if this is a top-level
    project and to \c yes otherwise. It sets \c VersionControl to the ID of the
    version control system in use.

    \section2 VcsCommand

    The VcsCommand page runs a set of version control operations and displays
    the results.

    The \c data section of this page takes an object with the following keys:

    \list
        \li \c vcsId with the id of the version control system to be used.

        \li \c trRunMessage with the message to be shown while the version
            control is running.

        \li \c extraArguments with a string or a list of strings defining
            extra arguments passed to the version control checkout command.

        \li \c repository with the URL to check out from the version control
            system.

        \li \c baseDirectory with the directory to run the checkout operation
            in.

        \li \c checkoutName with the subdirectory that will be created to hold
            the checked out data.

        \li \c extraJobs with a list of objects defining additional commands to
            run after the initial checkout. This can be used to customize the
            repository further by for example adding additional remote
            repositories or setting configuration variables of the version
            control system.

            Each \c extraJob is defined by an object with the following
            settings:

            \list
                \li \c skipIfEmpty silently removes empty arguments from the
                    command to run if you set it to \c true.
                    Defaults to \c true.

                \li \c directory with the working directory of the command to
                    run. This defaults to the value of \c baseDirectory.

                \li \c command with the command to be run.

                \li \c arguments with the arguments to pass to \c command.

                \li \c timeOutFactor can be used to provide for longer than
                    default timeouts for long-running commands.

                \li \c enabled which will be evaluated to decide whether or
                    not to actually execute this job.
            \endlist
    \endlist

    \section2 VcsConfiguration

    The VcsConfiguration page asks the user to configure a version control
    system and only enables the \uicontrol Next button once the configuration
    is successful.

    The \c data section of this page takes an object with the \c vcsId key.
    This setting defines the version control system that will be configured.

    \section1 Available Widgets

    You can add the following widgets on a Field page:

    \list
        \li Check Box
        \li Combo Box
        \li Label
        \li Line Edit
        \li Path Chooser
        \li Spacer
        \li Text Edit
    \endlist

    \note Wizards support only the the settings documented in the following
    sections.

    Specify the following settings for each widget:

    \list

        \li \c name specifies the widget name. This name is used as the variable
            name to access the value again.

        \li \c trDisplayName specifies the label text visible in the UI (if
            \c span is not \c true).

        \li \c type specifies the type of the widget: \c CheckBox, \c ComboBox,
            \c Label, \c LineEdit, \c PathChooser, \c Spacer, and \c TextEdit.

        \li \c trToolTip specifies a tooltip to show when hovering the field
            with the mouse.

        \li \c isComplete is evaluated for all fields to decide whether the
            \uicontrol Next button of the wizard is available or not. All fields
            must have their \c isComplete evaluate to \c true for this to
            happen. This setting defaults to \c true.

        \li \c trIncompleteMessage is shown when the field's \c isComplete
            evaluates to \c false.

        \li \c persistenceKey makes the user choice persistent. The value is
            taken to be a settings key. If the user changes the default
            value of the widget, the user-provided value is stored and will
            become the new default value the next time the wizard is run.

        \li \c visible is set to \c true if the widget is visible, otherwise
            it is set to \c false. By default, it is set to \c true.

        \li \c enabled is set to \c true if the widget is enabled, otherwise
            it is set to \c false. By default, it is set to \c true.

        \li \c mandatory is set to \c true if this widget must have a value
            for the \uicontrol Next button to become enabled. By default, it
            is set to \c true.

        \li \c span is set to hide the label and to span the form. By
            default, it is set to \c false. For more information, see
            \l{Using Variables in Wizards}.

        \li \c data specifies additional settings for the particular widget type, as described
            in the following sections.

    \endlist

    \section2 Check Box

    \code
    {
        "name": "IncludeQObject",
        "trDisplayName": "Include QObject",
        "type": "CheckBox",
        "data":
        {
            "checkedValue": "QObject",
            "uncheckedValue": "",
            "checked": "%{JS: value('BaseCB') === 'QObject' ? 'true' : 'false'}"
        }
    },
    \endcode

    \list

        \li \c checkedValue specifies the value to set when the check box is
            enabled. By default, set to \c true.

        \li \c uncheckedValue specifies the value to set when the check box is
            disabled. By default, set to \c false.

         \li \c checked is set to \c true if the check box is enabled, otherwise
             \c{false}.

    \endlist

    \section2 List

    \note The Combo Box and Icon List types are both variations of the List type,
          and therefore they can have the same properties.

    \code
    {
        "name": "BaseCB",
        "trDisplayName": "Base class:",
        "type": "ComboBox",
        "data":
        {
            "items": [ { "trKey": "<Custom>", "value": "" },
                       "QObject", "QWidget", "QMainWindow", "QDeclarativeItem", "QQuickItem" ]
        }
    },
    \endcode
    or
    \code
    {
        "name": "ChosenBuildSystem",
        "trDisplayName": "Choose your build system:",
        "type": "IconList",
        "data":
        {
            "items": [
                { "trKey": "CMake", "value": "cmake", "icon": "cmake_icon.png", "trToolTip": "Building with CMake." },
                { "trKey": "Qbs", "value": "qbs", "icon": "qbs_icon.png", "trToolTip": "Building with Qbs." },
                { "trKey": "QMake", "value": "qmake", "icon": "qmake_icon.png", "trToolTip": "Building with QMake." }
            ]
        }
    },
    \endcode

    \list

        \li \c items specifies a list of items to put into the list type. The
            list can contain both JSON objects and plain strings.
            For JSON objects, define \c trKey and \c value pairs, where the
            \c trKey is the list item visible to users and \c value contains
            the data associated with the item.
            In addition, you can use \c icon to specify an icon for the list
            item and \c trToolTip to specify a tooltip for it.

        \li \c index specifies the index to select when the list type is
            enabled. By default, it is set to \c 0.

        \li \c disabledIndex specifies the index to show if the list type is
            disabled.

    \endlist

    \section2 Label

    \code
    {
        "name": "LabelQQC_2_0",
        "type": "Label",
        "span": true,
        "visible": "%{JS: value('CS') === 'QQC_2_0'}",
        "data":
        {
            "wordWrap": true,
            "trText": "Creates a deployable Qt Quick 2 application using Qt Quick Controls.",
        }
    },
    \endcode

    \list

        \li \c wordWrap is set to \c true to enable word wrap. By default, it is
             set to \c{false}.

        \li \c trText contains the label text to display.

    \endlist

    \section2 Line Edit

    \code
    {
        "name": "Class",
        "trDisplayName": "Class name:",
        "mandatory": true,
        "type": "LineEdit",
        "data": {
            "trPlaceholder": "Fully qualified name, including namespaces",
            "validator": "(?:(?:[a-zA-Z_][a-zA-Z_0-9]*::)*[a-zA-Z_][a-zA-Z_0-9]*|)",
            "completion": "namespaces"
        }
    },
    {
        "name": "BaseEdit",
        "type": "LineEdit",
        "enabled": "%{JS: value('BaseCB') === '' ? 'true' : 'false'}",
        "mandatory": false,
        "data":
        {
            "trText": "%{BaseCB}",
            "trDisabledText": "%{BaseCB}",
            "completion": "classes"
        }
    },
    \endcode

    \list

        \li \c trText specifies the default text to display.

        \li \c trDisabledText specifies the text to display in a disabled field.

        \li \c completion lists existing \c namespaces for the class name line
            edit and existing \c classes for the base class line edit. This
            value replaces the history completer that is usually available for
            such fields.

        \li \c trPlaceholder specifies the placeholder text.

        \li \c validator specifies a QRegularExpression to validate the line
            edit against.

        \li \c fixup specifies a variable that is used to fix up the string.
            For example, to turn the first character in the line edit to upper
            case.

        \li \c isPassword is a boolean value that specifies that the line edit
            contains a password, which will be masked.

        \li \c historyId is a key that specifies the name for a list of items
            for the history completer. This value and \c completion are
            mutually exclusive, so do not set both of them at the same time.

        \li \c restoreLastHistoryItem is a boolean that specifies that the
            last history item is automatically set as the default text in
            the line edit. This key can only be set to true if \c historyId
            is also set.

    \endlist

    \section2 Path Chooser

    \code
    {
        "name": "Path",
        "type": "PathChooser",
        "trDisplayName": "Path:",
        "mandatory": true,
        "data":
        {
            "kind": "existingDirectory",
            "basePath": "%{InitialPath}",
            "path": "%{InitialPath}"
        }
    },
    \endcode

    \list

        \li \c path specifies the selected path.

        \li \c basePath specifies a base path that lookups are relative to.

        \li \c kind defines what to look for: \c existingDirectory,
            \c directory, \c file, \c saveFile, \c existingCommand, \c command,
            or \c any.

    \endlist

    \section2 Spacer

    \code
    {
        "name": "Sp1",
        "type": "Spacer",
        "data":
        {
            "factor": 2
        }
    },
    \endcode

    The \c factor setting specifies the factor (an integer) to multiply the
    layout spacing for this spacer.

    \section2 Text Edit

    \code
    {
        "name": "TextField",
        "type": "TextEdit",
        "data" :
        {
            "trText": "This is some text",
            "richText": true
        }
    }
    \endcode

    \list

        \li \c trText specifies the text to display.

        \li \c trDisabledText specifies the text to display when the text edit
            is disabled.

        \li \c richText is set to \c true for rich text, otherwise \c{false}.

    \endlist

    \section1 Available Generators

    \QC supports two different generators for JSON wizards.

    \section2 File Generator

    A \c File generator expects a list of objects in its \c data section. Each
    object defines one file to be processed and copied into the
    \c {%\{TargetPath\}} (or any other location).

    Each file object can take the following settings:

    \list
        \li \c source specifies the path and filename of the template file
            relative to the directory containing the \c {wizard.json} file.

            If \c source is unset, it is assumed that the file with the name
            given in \c target is generated by some other means. This is useful
            to for example specify the correct file to open as a project after
            checking out data from a version control system.

        \li \c target specifies the location of the generated file, either
            absolute or relative to \c %{TargetPath}, which is usually set by
            one of the wizard pages.

        \li \c openInEditor opens the file in the appropriate editor if it is
            set to \c true. This setting defaults to \c false.

        \li \c openAsProject opens the project file in \QC if it is set to
            \c true. This setting defaults to \c false.

        \li \c isBinary treats the file as a binary and prevents replacements
            from being done in the file if set to \c true. This setting
            defaults to \c false.

        \li \c condition generates the file if the condition
            returns \c true. This setting defaults to \c true. For more
            information, see \l{Using Variables in Wizards}.

    \endlist

    \section2 Scanner Generator

    A \c Scanner generator scans the \c {%\{TargetPath\}} and produces a list
    of all files found there.

    The \c Scanner generator takes one object in its \c data section with the
    following settings:

    \list

        \li \c binaryPattern is a regular expression that will be matched
            against all file names found. Any match will be marked as a binary
            file and template substitution will be skipped for this file. This
            setting defaults to an empty pattern, so no files will be marked as
            binary.

        \li \c subdirectoryPatterns is a list of regular expression patterns.
            Any directory matching one of these patterns will be scanned as well
            as the top level directory. This setting defaults to an empty list
            and no subdirectories will be scanned.

    \endlist
//! [json wizards]
*/
