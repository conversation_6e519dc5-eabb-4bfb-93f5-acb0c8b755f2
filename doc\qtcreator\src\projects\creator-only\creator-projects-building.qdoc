// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage qt-design-viewer.html
    \page creator-building-targets.html
    \nextpage creator-running-targets.html

    \title Building for Multiple Platforms

    You can build applications for multiple target platforms, or using different
    compilers, debuggers or Qt versions. \l{glossary-buildandrun-kit}{Kits}
    define the tools, \l{glossary-device}{device} type and other settings to use.

    By default, when you run the application, you
    automatically build and deploy it first.
    However, you can also perform each operation separately.

    To check that the application code can be compiled and linked for a device,
    you can build the project. The build errors and warnings are displayed in
    the \l Issues. More detailed information is displayed in \l {Compile Output}.

    To build an application:

    \list 1

        \li  Click the \uicontrol {Build and Run Kit Selector} icon (1) or select
             \uicontrol Build > \uicontrol {Open Build and Run Kit Selector} to select the
             build and run \l{glossary-buildandrun-kit}{kit} or an
             \l{Managing Android Virtual Devices (AVD)}{Android device}.

        \image qtcreator-kit-selector.png "Kit selector"

        \li  Choose \uicontrol Build > \uicontrol {Build Project} or press
             \key {Ctrl+B}.

             You can also select the \uicontrol Run button (2) to also deploy and run
             the application after building it.

    \endlist

    While the application is being built, the \uicontrol Build button (3)
    changes to a \uicontrol {Cancel Build} button. To cancel the build, select
    the button, press \key {Alt+Backspace}, or select \uicontrol Build >
    \uicontrol {Cancel Build}. If you selected a build command and
    decide you would also like to run the application, you can select the
    \uicontrol Run button to schedule running the project after building is done.

    For more information on the options you have, see
    \l{Specifying Build Settings}.

    \section1 Additional Build Commands

    The \uicontrol Build menu contains additional commands for building,
    rebuilding, and cleaning projects.

    To build the current project in all its configurations, that
    is, for all build configurations in all enabled kits, select
    \uicontrol {Build Project for All Configurations}.

    To build all open projects, select \uicontrol {Build All Projects}.
    If building one application fails, \QC displays an error message and
    continues building the other applications.

    To build all open projects in all their configurations, select
    \uicontrol {Build All Projects for All Configurations}.

    To quickly check the compile output for changes that you made in one file or
    subproject, you can use the \uicontrol Build menu commands to build a file or
    subproject. The available build menu commands depend on the build system
    you selected for the project: CMake, qmake, or Qbs.

    Select \uicontrol {Build for Run Configuration} to
    build the executable that corresponds to the selected run configuration.
    You can also use the \c cm filter in the \l {Searching with the Locator}
    {locator}.

    To remove all build artifacts, select one of \uicontrol {Clean} menu commands.

    To clean the build directory and then build the project, select
    one of \uicontrol {Rebuild} menu commands.

    To build and clean projects without dependencies, select the
    \uicontrol {Build Without Dependencies},
    \uicontrol {Rebuild Without Dependencies}, and
    \uicontrol {Clean Without Dependencies} options in the context menu in the
    \uicontrol Projects view.

    To run qmake or CMake to regenerate build system files, select
    \uicontrol Build > \uicontrol {Run qmake} or \uicontrol {Run CMake}.

    \section1 Building with CMake

    \QC automatically runs CMake when you make changes to \c {CMakeLists.txt}
    files. To disable this feature, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol CMake > \uicontrol Tools. Select the
    CMake executable to edit, and then deselect the \uicontrol {Autorun CMake}
    check box.

    \image qtcreator-preferences-cmake-tools.png "Tools tab in CMake Preferences"

    For more information, see \l {Setting Up CMake}.

    \section1 Building with qmake

    To prevent failures on incremental builds, it might make sense
    to always run qmake before building, even though it means that
    building will take more time. To enable this option, select \uicontrol Edit
    > \uicontrol Preferences > \uicontrol {Build & Run} > \uicontrol Qmake >
    \uicontrol {Run qmake on every build}.

    \image qtcreator-preferences-build-run-qmake.png "qmake tab in Build & Run Preferences"

    For more information, see \l {qmake Build Configuration}.
*/
