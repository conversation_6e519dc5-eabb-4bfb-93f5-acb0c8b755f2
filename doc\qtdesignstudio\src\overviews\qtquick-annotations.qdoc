// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-annotations.html
    \if defined(qtdesignstudio)
    \previouspage qtquick-positioning.html
    \nextpage qtquick-prototyping.html
    \else
    \previouspage qtquick-fonts.html
    \nextpage creator-quick-ui-forms.html
    \endif

    \title Annotating Designs

    You can submit your designs to review or further development as QML files.
    You can annotate your designs to provide reviewers or developers with
    additional information about them. You can add \e {global annotations}
    that apply to the whole file or annotate individual components.

    An annotation consist of an annotation name and one or several comments.
    The comments have a title, author, and comment text.

    To add or edit global annotations, select \inlineimage icons/annotation.png
    on the top menu bar in the Design mode.

    Global annotations have an additional status property, which enables you
    to indicate whether you are still working on the design, you have submitted
    it to review, or it can be considered done. To set the status, select
    \uicontrol {Add Status}.

    \image qtquick-annotations.png "Annotation Editor"

    Annotations are saved in the end of QML files when you save the file. They
    do not affect the QML performance in any way.

    \section1 Annotating Components

    To add annotations to components:

    \list 1
        \li Select the component to annotate in \uicontrol Navigator or in
            the \uicontrol {2D} view.
        \li In \uicontrol Properties, select \uicontrol {Add Annotation} to
            open \uicontrol {Annotation Editor}.
            \image qtquick-annotation-editor.png "Annotation Editor"
        \li The \uicontrol {Selected Item} field displays the ID of the
            component.
        \li In the the \uicontrol Name field, enter a free-form text that
            describes the component.
        \li In the \uicontrol Title field, enter the text to display in
            the tab for this comment.
        \li In the \uicontrol Author field, enter the author's name.
        \li In the \uicontrol Text field, enter the comment text.
        \li Select \uicontrol OK.
    \endlist

    To add more comments about the component, select the \inlineimage icons/plus.png
    (\uicontrol {Add Comment}) button.

    To remove the active comment, select the \inlineimage icons/minus.png
    (\uicontrol {Remove Comment}) button. To remove the annotation, right-click
    the annotation icon, and then select \uicontrol {Remove Annotation}.

    To view the annotations in table format, select \uicontrol {Table view}.

    To edit annotations, select \uicontrol {Edit Annotation} in the context
    menu of the component.
*/
