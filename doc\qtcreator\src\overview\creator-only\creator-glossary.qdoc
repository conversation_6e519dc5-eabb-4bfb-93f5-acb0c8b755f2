// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-known-issues.html
    \page creator-glossary.html
    \nextpage technical-support.html

    \title Glossary

    \table
        \header
            \li Term
            \li Meaning

        \row
            \li Build configuration
                \target glossary-build-config
            \li Contains all the information you need to compile the sources
                into binaries. Different build configurations allow you to
                quickly build the project for different purposes.
                By default, \QC creates \e {debug build} and \e {release build}
                configurations for each \e {kit} defined for your project.
                A debug build contains
                additional debug symbols that you need for debugging the
                application but that you can leave out from the release version.
                Generally, you use the debug configuration for testing and the
                release configuration for creating the final installation file.

        \row
            \li Build step
                \target glossary-build-step
            \li A command that \QC executes to accomplish a basic step in a
                complex task such as cleaning, building, or deploying a project.
                \QC executes build steps in sequence to complete the task.
                The number and type of necessary build steps depends on the
                project type and the build system you use. For example, projects
                that are based on \c qmake specify build steps to use \c qmake
                and \c make.

        \row
            \li Component
                \target glossary-component
            \li An instantiable QML definition, typically
                contained in a .qml file. For instance, a Button component may
                be defined in Button.qml. The QML runtime may instantiate this
                Button component to create Button objects. Alternatively, a
                component may be defined inside a \l{Component} QML type.

        \row
            \li Deploy configuration
                \target glossary-deploy-config
            \li Handles the packaging and copying of the necessary files to a
                location you want to run the executable at. The files can be
                copied to a location in the file system of the development PC or
                a \e {device}.

        \row
            \li Device
                \target glossary-device
            \li An embedded device or a mobile device. For more information, see
                \l{Embedded Platforms} and \l{Mobile Platforms}.

        \row
            \li Kit
                \target glossary-buildandrun-kit
            \li \QC groups build and run specific settings as kits to make
                cross-platform development easier. Each kit consists of a set
                of values that define one environment, such as a \e {device},
                tool chain, Qt version, and debugger command to use. Configure kits at
                \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits.

        \row
            \li Mode
                \target glossary-mode
            \li Adapts the \QC user interface to the different application
                development tasks at hand. Each mode has its own view that shows
                only the information required for performing a particular task,
                and provides only the most relevant features and functions
                related to it. As a result, the majority of the \QC window area
                is always dedicated to actual application development tasks.

        \row
            \li Project
                \target glossary-project
            \li Groups together a set of source files, forms, and resource files
                that you can build for, as well as deploy and run on different
                desktop and \e {device} platforms, as
                specified by a \e {build configuration}, \e {deploy
                configuration}, and \e {run configuration}.

        \row
            \li Qt version
                \target glossary-project-qt
            \li \QC allows you to have multiple versions of Qt installed on your
                development PC and use different versions to build your projects
                for different purposes.

        \row
            \li Run configuration
                \target glossary-run-config
            \li Starts the application in the location where it was copied by
                the \e {deploy configuration}. By default, when you run a
                project, \QC builds it, deploys it to the \e {device} specified
                in the selected \e {kit},
                and runs it there. However, if you have not made any changes to
                the project since you last built and deployed it, \QC simply
                runs it again.

        \row
            \li Shadow build
                \target glossary-shadow-build
            \li Shadow building means building a project in a separate
                directory, the \e{build directory}. The build directory is
                different from the \e {source directory}. One of the benefits of
                shadow building is that it keeps your source directory clean,
                which makes it faster to switch between \e {build
                configurations}. Therefore, shadow building is the best practice
                if you need many build configurations for a single set of
                source files.

                Shadow builds are also widely known as \e {out-of-source builds}
                to separate them from \e {in-source builds} or
                \e {in-tree builds}.

    \endtable

*/
