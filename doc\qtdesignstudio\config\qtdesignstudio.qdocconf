project     = $IDE_ID
description = "$IDE_DISPLAY_NAME Manual"
url         = https://doc.qt.io/qtdesignstudio
version     = $QTC_VERSION

#Words to ignore for auto-linking
ignorewords += \
    macOS \
    WebChannel \
    WebSocket \
    WebSockets \
    OpenGL \
    MinGW

headerdirs =
sourcedirs = ../src \
             ../examples/doc \
             ../../qtcreator/src
imagedirs = ../images \
            ../examples/doc/images \
            ../../qtcreator/images \
            ../../../share/qtcreator/qml/qmlpuppet/mockfiles/images \
            ../../../share/qtcreator/qmldesigner/propertyEditorQmlSources/imports/HelperWidgets/images \
            ../../../src/libs/qmleditorwidgets/images \
            ../../../src/plugins/qmldesigner/components/componentcore/images \
            ../../../src/plugins/qmldesigner/components/edit3d/images \
            ../../../src/plugins/qmldesigner/componentsplugin/images \
            ../../../src/plugins/qmldesigner/qtquickplugin/images \

include(../../qtcreator/images/extraimages/qtdesignstudio-extraimages.qdocconf)

excludefiles += ../../qtcreator/src/qtcreator.qdoc \
                ../../qtcreator/src/qtcreator-toc.qdoc

excludedirs  += ../../qtcreator/examples/accelbubble \
                ../../qtcreator/examples/textfinder \
                ../../qtcreator/examples/transitions \
                ../../qtcreator/src/analyze \
                ../../qtcreator/src/android \
                ../../qtcreator/src/baremetal \
                ../../qtcreator/src/cmake \
                ../../qtcreator/src/conan \
                ../../qtcreator/src/debugger/creator-only \
                ../../qtcreator/src/docker \
                ../../qtcreator/src/editors/creator-only \
                ../../qtcreator/src/howto/creator-only \
                ../../qtcreator/src/incredibuild \
                ../../qtcreator/src/ios \
                ../../qtcreator/src/linux-mobile \
                ../../qtcreator/src/mcu \
                ../../qtcreator/src/meson \
                ../../qtcreator/src/overview/creator-only \
                ../../qtcreator/src/projects \
                ../../qtcreator/src/qnx \
                ../../qtcreator/src/qtquick/creator-only \
                ../../qtcreator/src/vcs/creator-only \
                ../../qtcreator/src/widgets \
                ../../qtcreator/src/webassembly

exampledirs = ../examples/ \
              ../../qtcreator/examples
examples.fileextensions += *.qml *.ts *.qm
examples.imageextensions = "*.svg *.png *.jpg *.gif"

depends +=    qtwidgets \
              qtcore \
              qtqml \
              qtqmlmodels \
              qtquick \
              qmake \
              qtdoc \
              qtgraphicaleffects \
              qtgui \
              qthelp \
              qtquick3d \
              qtquickcontrols \
              qtquickextras \
              qtquicktimeline \
              qtlinguist \
              qtscxml \
              qtsensors \
              qttestlib \
              qtuitools \
              qtvirtualkeyboard \
              qtxml

include(../../config/macros.qdocconf)
include(../../config/qt-cpp-ignore.qdocconf)
include(../../config/qt-defines.qdocconf)

defines += qtdesignstudio

sources.fileextensions = "*.qdoc" "*.qdocinc"

#depends += qtcreator \
#           qmake

qhp.projects = qtdesignstudio
qhp.qtdesignstudio.file = qtdesignstudio.qhp
qhp.qtdesignstudio.namespace = org.qt-project.$IDE_ID.$QTC_VERSION_TAG
qhp.qtdesignstudio.virtualFolder = doc
qhp.qtdesignstudio.indexTitle       = $IDE_DISPLAY_NAME Manual $QTC_VERSION
qhp.qtdesignstudio.filterAttributes = $IDE_ID $QTC_VERSION
qhp.qtdesignstudio.customFilters.qtdesignstudio.name = $IDE_DISPLAY_NAME $QTC_VERSION
qhp.qtdesignstudio.customFilters.qtdesignstudio.filterAttributes = $IDE_ID $QTC_VERSION
qhp.qtdesignstudio.indexRoot =

qhp.qtdesignstudio.subprojects = manual
qhp.qtdesignstudio.subprojects.manual.title = $IDE_DISPLAY_NAME Manual
qhp.qtdesignstudio.subprojects.manual.indexTitle = All Topics
qhp.qtdesignstudio.subprojects.manual.type = manual


# Doxygen compatibility commands
macro.see                       = "\\sa"
macro.function                  = "\\fn"
macro.QMLD                      = "Qt Design Studio"

navigation.landingpage = "$IDE_DISPLAY_NAME Manual"
buildversion = "$IDE_DISPLAY_NAME Manual $QTC_VERSION"
