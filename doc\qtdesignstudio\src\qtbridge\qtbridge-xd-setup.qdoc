// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage xdqtbridge.html
    \page qtbridge-xd-setup.html
    \nextpage qtbridge-xd-using.html

    \title Setting Up \QBXD

    \QBXD is included in the
    \l{https://www.qt.io/pricing}{Qt Design Studio Enterprise license}.
    You can use the Qt Installer to have the \QBXD plugin package copied to the
    following path in your Qt installation folder:

    \list
        \li On Windows:
            \c {Tools\QtDesignStudio\xd_bridge\qtbridge.xdx}
        \li On \macos:
            \c {QtDesignStudio/xd_bridge/qtbridge.xdx}.
    \endlist

    \note Install Adobe XD before installing the plugin.

    To install the \QBXD plugin to Adobe XD, double-click \c qtbridge.xdx in the
    \c xd_bridge folder in the installation directory of \QDS. Adobe XD will
    automatically install \QBXD.

    \note Since the plugin is not distributed through Adobe's marketplace, during
    the installation Adobe XD might warn about the third-party developer.

    \image qt-bridge-xd-warn.png

    You can launch the plugin from \uicontrol Plugins > \uicontrol {\QB}.
*/
