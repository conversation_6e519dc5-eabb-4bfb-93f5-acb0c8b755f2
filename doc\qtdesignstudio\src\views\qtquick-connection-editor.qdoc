// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qmldesigner-connections.html
    \previouspage qtquick-adding-dynamics.html
    \nextpage quick-signals.html

    \title Working with Connections

    \list
        \li \l{Connecting Components to Signals}

            A signal and handler mechanism enables components to respond to
            application events, which are represented by \e signals. When a
            signal is emitted, the corresponding \e {signal handler} is
            invoked to respond to the event by applying an action, for
            example.

        \li \l{Adding Bindings Between Properties}

            A component's property can be assigned a static value that stays
            constant until it is explicitly changed. To make the UI more
            dynamic, you can use \e {property bindings}. This means that you
            can specify relationships between component properties so that when
            the value of a property changes, the values of any properties that
            are bound to it are automatically updated accordingly.

        \li \l{Specifying Custom Properties}

            Each preset component has a set of preset properties that you
            can specify values for. You can add custom properties that would
            not otherwise exist for a particular \l{Component Types}
            {component type} or your custom components.

        \if defined(qtcreator)
        \li \l{Managing C++ Backend Objects}

            Application developers can access QObject objects implemented in C++
            from QML files.
        \endif
    \endlist

    For an example of using properties, bindings, and connections to create a
    scalable push button, see \l{Creating Scalable Buttons and Borders}.
*/
