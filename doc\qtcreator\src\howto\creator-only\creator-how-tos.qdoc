// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-faq.html
    \page creator-how-tos.html
    \nextpage creator-known-issues.html

    \title How-tos

    How do I:

    \list
    \li \l {Switch between modes}
    \li \l {Move between open files}
    \li \l {Switch to Edit mode}
    \li \l {Find a specific setting}
    \li \l {View output}
    \li \l {Find keyboard shortcuts}
    \li \l {Run \QC from the command line}
    \li \l {Show and hide sidebars}
    \li \l {Move to symbols}
    \li \l {Inspect signal-slot connections while debugging}
    \li \l {Display low-level data in the debugger}
    \li \l {See the value of variables in tooltips while debugging}
    \li \l {Quickly locate files using the keyboard}
    \li \l {Perform calculations}
    \li \l {Jump to a function in QML code}
    \li \l {Add a license header template for C++ code}
    \li \l {Paste text from my clipboard history}
    \li \l {Sort lines alphabetically}
    \li \l {Enclose selected code in curly braces, parentheses, or double quotes}
    \li \l {Select the enclosing block in C++}
    \li \l {Add my own code snippets to the auto-complete menu}
    \li \l {Quickly write down notes somewhere}
    \li \l {Configure the amount of recent files shown}
    \li \l {Search and replace across files using a regular expression}
    \endlist

    \section1 Switch between modes

    \QC uses different modes for different purposes. You can quickly
    switch between these modes with the following keyboard shortcuts:

    \list

        \li \uicontrol Welcome mode \key Ctrl+1
        \li \uicontrol Edit mode \key Ctrl+2
        \li \uicontrol Design mode \key Ctrl+3
        \li \uicontrol Debug mode \key Ctrl+4
        \li \uicontrol Projects mode \key Ctrl+5
        \li \uicontrol Help mode \key Ctrl+6

    \endlist

    For more information about \QC modes, see \l {Selecting Modes}.

    \section1 Move between open files

    To quickly move between currently open files, press
    \key Ctrl+Tab.

    To move forward in the location history, press \key {Alt+Right}
    (\key {Cmd+Opt+Right} on \macos). To move backward, press \key {Alt+Left}
    (\key {Cmd+Opt+Left} on \macos). For example, if you use the \uicontrol Locator
    to jump to a symbol in the same file, you can jump back to your original
    location in that file by pressing \key {Alt+Left}.

    \section1 Switch to Edit mode

    To move to the \uicontrol Edit mode and currently active file, press
    \key Esc.

    If you already are in the \uicontrol Edit mode:

    \list

        \li The first press moves focus to the editor

        \li The second press closes secondary windows

    \endlist

    \section1 Find a specific setting

    To find specific settings in \uicontrol Edit > \uicontrol Preferences,
    use the filter located at the top left of the \uicontrol Preferences dialog.

    \section1 View output

    The \l{Viewing Output}{taskbar} provides different views to output from
    several sources, such as a list of errors and warnings encountered during
    a build, detailed output from the compiler, status of a program when it is
    executed, debug output, or search results.

    \image qtcreator-output-panes-taskbar.png "Output on the taskbar"

    To view different types of output, use the following shortcuts:

    \list

        \li \uicontrol{Issues} - \key Alt+1 (\key Cmd+1 on \macos)

        \li \uicontrol{Search Results} - \key Alt+2 (\key Cmd+2 on \macos)

        \li \uicontrol{Application Output} - \key Alt+3 (\key Cmd+3 on \macos)

        \li \uicontrol{Compile Output} - \key Alt+4 (\key Cmd+4 on \macos)

    \endlist

    For additional ways to view other types of output, see \l{Viewing Output}.

    \section1 Find keyboard shortcuts

    \QC provides \l{Keyboard Shortcuts}{many useful keyboard shortcuts}.
    You can see the keyboard shortcut for a menu command in the menu
    or the tooltip for a button.

    To customize, import, or export keyboard shortcuts, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Environment > \uicontrol Keyboard.

    \image qtcreator-keyboard-shortcuts.png "Keyboard preferences"

    \section1 Run \QC from the command line

    You can launch \QC from the command line using the name of an
    existing \l{Managing Sessions}{session} or project file by entering
    the name as the command argument.

    For example, running \c {qtcreator somesession}, launches \QC and
    loads the session called \e somesession.

    For more information, see \l{Using Command Line Options}.

    \section1 Show and hide sidebars

    You can toggle the left and right sidebar in some \QC modes.

    To toggle the left sidebar, click \inlineimage icons/leftsidebaricon.png
    (\uicontrol {Hide Left Sidebar/Show Left Sidebar}) or press \key Alt+0
    (\key Cmd+0 on \macos).

    To toggle the right sidebar, click \inlineimage icons/rightsidebaricon.png
    (\uicontrol {Hide Right Sidebar/Show Right Sidebar}) or press
    \key Alt+Shift+0 (\key Cmd+Shift+0 on \macos).

    For more information on using the sidebars, see
    \l {Browsing Project Contents}.

    \section1 Move to symbols

    To move straight to a symbol used in a project, select the symbol in the
    \uicontrol Editor toolbar drop-down menu. For more information on the editor
    toolbar, see \l {Using the Editor Toolbar}.

    To jump to a symbol in the current file, press \key {Ctrl+K} to open the
    \uicontrol Locator, enter a period (.), and start typing the symbol name. Then
    select the symbol in the list. For more information on using the locator,
    see \l{Searching with the Locator}.

    Press \key Ctrl (\key Cmd on \macos) and click a symbol to move directly to
    the definition or the declaration of the symbol. You can also move the
    cursor on the symbol and press \key {F2}. For more information, see
    \l{Moving to Symbol Definition or Declaration}.

    \section1 Inspect signal-slot connections while debugging

    If an instance of a class is derived from QObject, and you would like to
    find all other objects connected to one of your object's slots using
    Qt's signals and slots mechanism, select \uicontrol Edit > \uicontrol Preferences
    > \uicontrol {Debugger} > \uicontrol {Locals & Expressions} >
    \uicontrol {Use Debugging Helpers}.

    In the \uicontrol{Locals} view, expand the object's entry and open
    the slot in the \e slots subitem. The objects connected to this slot are
    shown as children of the slot. This method works with signals too.

    For more information about the \uicontrol{Locals} view, see
    \l{Local Variables and Function Parameters}.

    \section1 Display low-level data in the debugger

    If special debugging of Qt objects fails due to data corruption within the
    debugged objects, you can switch off the debugging helpers. When debugging
    helpers are switched off, low-level structures become visible.

    To switch off the debugging helpers:
    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Debugger >
            \uicontrol {Locals & Expressions}.
            \image qtcreator-debugging-helper-options.png "Locals & Expressions preferences"
        \li Deselect \uicontrol {Use Debugging Helpers}.

    \endlist

    \section1 See the value of variables in tooltips while debugging

    To inspect the value of variables from the editor, you can turn
    on tooltips. Tooltips are hidden by default for performance reasons.

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol Debugger > \uicontrol General.
            \image qtcreator-debugger-general-options.png "Debugger General preferences"
        \li Select \uicontrol {Use tooltips in main editor when debugging}.

    \endlist

    When you hover over a variable in the code editor in \uicontrol Debug mode, a
    tooltip is displayed. To keep the tooltip visible, click the pin button.
    You can expand pinned tooltips to view their full content.

    \image qtcreator-pin-tooltip.png

    Pinned tooltips are stored in the session. To close all pinned tooltips,
    select \uicontrol {Close Editor Tooltips} in the context menu in the
    \uicontrol {Locals} view.

    \section1 Quickly locate files using the keyboard

    The \uicontrol Locator provides one of the easiest ways in \QC to browse
    through projects, files, classes, functions, documentation, and file systems.
    To quickly access files not directly mentioned in your project, you can
    create your own locator filters. That way you can locate files in a
    directory structure you have defined.

    \image qtcreator-locator.png "List of locator filters"

    To create locator filters, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Environment > \uicontrol Locator > \uicontrol Add.

    \image qtcreator-locator-customize.png "Locator preferences"

    For more information, see \l{Creating Locator Filters}.

    \section1 Perform calculations

    Open the \uicontrol Locator with \key {Ctrl+K} and type =, followed by a space.
    You can now do basic calculations, with options to copy the results to the clipboard
    by navigating through the entries and pressing \key {Enter}.

    For more information, see \l{Executing JavaScript}.

    \section1 Jump to a function in QML code

    Open the \uicontrol Locator with \key {Ctrl+K} and type m, followed by a space.
    You can now go directly to any QML method in the file.

    \section1 Add a license header template for C++ code

    Specify a file containing a license header for C++ in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol C++ > \uicontrol {File Naming} >
    \uicontrol {License template}.

    \image qtcreator-options-cpp-files.png "File Naming preferences"

    The license file may contain special placeholders enclosed
    in \c{%%} that are replaced when generating a new file:

    \list 1

        \li \c %YEAR%: Year
        \li \c %MONTH%: Month
        \li \c %DAY%: Day of the month
        \li \c %DATE%: Date
        \li \c %USER%: Username
        \li \c %FILENAME%: File name
        \li \c %CLASS%: Class name (if applicable)
        \li \c %$VARIABLE%: Contents of environment variable \c{VARIABLE}.

    \endlist

    \section1 Paste text from my clipboard history

    \QC stores copied text in clipboard history. To retrieve clips from the
    history, press \key {Ctrl+Shift+V} until the clip appears.
    The number of clips in the history is fixed to 10.

    \section1 Sort lines alphabetically

    To sort selected lines alphabetically, select \uicontrol Edit >
    \uicontrol Advanced > \uicontrol {Sort Selected Lines} or press
    \key {Alt+Shift+S} (or \key Ctrl+Shift+S on \macos).

    \section1 Enclose selected code in curly braces, parentheses, or double quotes

    When you have selected code and enter one of the following opening
    characters, the matching closing character is added automatically
    at the end of the selection:

    \list
        \li {
        \li (
        \li "
    \endlist

    To specify whether to automatically insert matching characters,
    select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Text Editor} > \uicontrol Completion.

    \image qtcreator-options-texteditor-completion.png "Completion preferences"

    \section1 Select the enclosing block in C++

    Press \key {Ctrl+U}.

    \section1 Add my own code snippets to the auto-complete menu

    You can add, modify, and remove snippets in the snippet editor.
    To open the editor, select \uicontrol Edit > \uicontrol Preferences
    > \uicontrol {Text Editor} > \uicontrol Snippets.

    \image qtcreator-snippet-modifiers.png "Snippets preferences"

    For more information, see \l {Adding and Editing Snippets}.

    \section1 Quickly write down notes somewhere

    Select \uicontrol File > \uicontrol {New File} >
    \uicontrol General > \uicontrol {Scratch Buffer}.
    Alternatively, \key {Ctrl+N} can be used to open this dialog, which is
    fully navigable via keyboard by using the up and down arrow keys and the
    tab key.

    This creates a new empty text file and saves it to the temporary directory
    on your machine. You can use it to write down notes without having to worry
    about deleting the file afterwards. The operating system will eventually
    remove the file automatically. If you want to keep the file, you can easily
    save it as a new file somewhere else. If you accidentally close the file,
    you can find it in the \uicontrol File > \uicontrol {Recent Files} menu.

    \section1 Configure the amount of recent files shown

    Set the value of \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Environment > \uicontrol System
    > \uicontrol {Maximum number of entries in "Recent Files"}.

    \image qtcreator-options-environment-system.png "System preferences"

    \section1 Search and replace across files using a regular expression

    As an example, say you want to replace equality checks (\c {foo == bar})
    with a function (\c {foo.equals(bar)}):

    \list 1
    \li Ensure that any work you have done is committed to version control,
        as the changes cannot be undone.
    \li Press \key {Ctrl+Shift+F} to bring up the \uicontrol {Advanced Find}
        form.
    \li Change the scope to whatever is appropriate for your search.
    \li Under the \uicontrol {Search for} text field, select
        the \uicontrol {Use regular expressions} check box.
    \li Enter the following text in the \uicontrol {Search for} text field:
        \badcode
        if \((.*) == (.*)\)
        \endcode
    \li Press \uicontrol {Search & Replace} to see a list of search results.
    \li In the \uicontrol {Replace with} text field, enter the following text:
        \badcode
        if (\1.strictlyEquals(\2))
        \endcode
    \li Press \uicontrol Replace to replace all instances of the text.
    \endlist

    For more information, see \l {Advanced Search}.
*/
