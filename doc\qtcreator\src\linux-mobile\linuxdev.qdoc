// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

    /*!
    \page creator-developing-generic-linux.html
    \previouspage creator-developing-qnx.html
    \nextpage creator-setup-webassembly.html

    \title Connecting Remote Linux Devices

    You can connect generic Linux devices to the development PC to run,
    debug, and analyze applications built for them from \QC.

    If you have a tool chain for building applications for embedded Linux
    devices installed on the development PC, you can add
    it to \QC. You can then select a \l{glossary-buildandrun-kit}{kit}
    with the device type \uicontrol {Remote Linux Device} to
    build applications for and run them on the devices.

    To be able to run and debug applications on remote Linux devices,
    you must add devices and select them in the \QC \l{glossary-buildandrun-kit}
    {kit}.

    You use a wizard to create the connections. You can edit the settings later
    in \uicontrol Edit > \uicontrol Preferences > \uicontrol Devices >
    \uicontrol Devices.

    \image qtcreator-preferences-devices-remote-linux.webp "Remote Linux Device in the Devices tab"

    You can protect the connections between \QC and a device by using an
    \l{https://www.openssh.com/}{OpenSSH} connection. OpenSSH is a
    connectivity tool for remote login using the SSH protocol. The OpenSSH
    suite is not delivered with \QC, so you must download it and install it
    on the development PC. Then, you must configure the paths to the tools in
    \QC. For more information, see \l {Configuring SSH Connections}.

    You need either a password or an SSH public and private key pair for
    authentication. If you do not have an SSH key, you can use the ssh-keygen
    tool to create it in \QC. For more information, see \l {Generating SSH Keys}.

    \note \QC does not store passwords, so if you use password authentication,
    you may need to enter the password on every connection to the device,
    or, if caching is enabled, at every \QC restart.

    To configure connections between \QC and a remote Linux device and to
    specify build and run settings for the device:

    \list 1

        \li Make sure that your device can be reached via an IP address.

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
            \uicontrol {Qt Versions} > \uicontrol Add to add the Qt version
            for embedded Linux.

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
            \uicontrol Compilers > \uicontrol Add to add the compiler for
            building the applications.

        \li To deploy applications and run them remotely on devices, specify
            parameters for accessing the devices:

            \list a

                \li Select \uicontrol Edit > \uicontrol Preferences >
                    \uicontrol Devices > \uicontrol Devices > \uicontrol Add >
                    \uicontrol {Remote Linux Device}
                    > \uicontrol {Start Wizard}.

                    \image qtcreator-preferences-devices-remote-linux-connection.webp "Connection Data wizard"

                \li In \uicontrol {The name to identify this configuration},
                    enter a name for the connection.

                \li In \uicontrol {The device's host name or IP address},
                    enter the host name or IP address of the device.
                    This value will be available in the variable \c %{Device:HostAddress}.

                \li In \uicontrol {The device's SSH port number}, enter the port
                    number to use for SSH connections. This value will be
                    available in the variable \c %{Device:SshPort}.
                \li In \uicontrol {The username to log into the device},
                    enter the username to log into the device and run the
                    application as.
                    This value will be available in the variable \c %{Device:UserName}.

                \li Select \uicontrol {Next} to open the
                    \uicontrol {Key Deployment} dialog.

                    \image qtcreator-preferences-devices-remote-linux-key-deployment.webp "Key Deployment dialog"

                \li In \uicontrol {Private key file}, select a private key file
                    to use for authentication. This value will be available in
                    the variable \c %{Device:PrivateKeyFile}.

                \li If you do not have a public-private key pair, select
                    \uicontrol {Create New Key Pair}. For more information,
                    see \l{Generating SSH Keys}.

                \li Select \uicontrol {Deploy Public Key} to copy the public
                    key to the device.

                \li  Select \uicontrol {Next} to create the connection.

            \endlist

            All of these parameters can be edited later, as well as additional ones that the
            wizard does not show because there are sensible default values.


        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
            \uicontrol Add to add a kit for building for the device. Select the
            Qt version, compiler, and device that you added above, and select
            \uicontrol {Remote Linux Device} in \uicontrol {Device type}.

            To build on the remote device, select \uicontrol {Remote Linux Device}
            also in \uicontrol {Build device}.

        \li To specify build settings:

        \list 1

            \li Open a project for an application you want to develop for the
                device.

            \li Select \uicontrol Projects > \uicontrol {Build & Run} to enable
                the kit that you specified above.

        \endlist

        \li Select \uicontrol Run to specify run settings. Usually, you can use
            the default settings.

    When you run the project, \QC deploys the application as specified by the
    deploy steps. By default, \QC copies the application files to the device.
    For more information, see \l{Deploying to Remote Linux}.

    \endlist

    \include linux-mobile/linuxdev-keys.qdocinc configuring ssh
    \include linux-mobile/linuxdev-keys.qdocinc generating ssh keys
    \include linux-mobile/linuxdev-processes.qdocinc managing device processes
*/
