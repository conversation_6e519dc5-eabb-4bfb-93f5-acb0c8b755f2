// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-use-cases.html
    \previouspage studio-projects.html
    \nextpage studio-terms.html

    \title Use Cases

    The following table summarizes the main use cases of \QDS with links to
    more information:

    \table
        \row
            \li \inlineimage front-ui.png
            \li \inlineimage studio-flow.png
            \li \inlineimage studio-3d-scenes.png
            \li \inlineimage front-projects.png
        \row
            \li \b {Creating UI wireframes}
            \li \b {Creating UI prototypes}
            \li \b {Combining 2D and 3D assets}
            \li \b {Cooperating with developers}
        \row
            \li Create a UI concept by using preset and custom 2D and 3D
                components and designing application flows. Create scalable
                layouts with all your screens and controls in place.
            \li Create an animated UI prototype with functioning UI controls
                and logic and validate it on the desktop or mobile or embedded
                devices.
            \li Create 2D and 3D assets and design UIs in imaging and design
                tools. Then bring your designs over to \QDS where you can
                combine them into one UI.
            \li Use visual editors that generate the code for you and deliver
                the code to developers for adding more code and preparing the
                application for production. You can use version control to
                pass changes back and forth when necessary.
        \row
            \li \l{Wireframing}
            \li \l{Prototyping}
            \li \l{Asset Creation with Other Tools}
            \li \l{Implementing Applications}
    \endtable
*/
