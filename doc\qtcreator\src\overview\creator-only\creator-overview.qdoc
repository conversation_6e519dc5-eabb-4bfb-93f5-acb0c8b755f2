// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-getting-started.html
    \page creator-overview.html
    \nextpage creator-quick-tour.html

    \title IDE Overview

    \QC is an integrated development environment (IDE) that provides you with
    tools to design and develop applications with the Qt application framework.
    With Qt you can develop applications and user interfaces once and deploy
    them to several desktop, embedded, and mobile operating systems or
    web browsers (experimental). \QC
    provides you with tools for accomplishing your tasks throughout the whole
    application development life-cycle, from creating a project to deploying the
    application to the target platforms.

    \table
        \row
            \li \inlineimage front-projects.png
            \li \inlineimage front-ui.png
            \li \inlineimage front-coding.png
        \row
            \li \b {\l{Managing Projects}}

                To be able to build and run applications, \QC needs the same
                information as a compiler would need. It stores the information
                in the project settings.

                \QC contains templates for creating new projects. They guide
                you step-by-step through the project creation process, create
                the necessary files, and specify settings depending on the
                choices you make. For more information, see
                \l{Managing Projects}.
            \li \b {\l{Designing User Interfaces}}

                To create intuitive, modern-looking, fluid user interfaces, you
                can use \l{Qt Quick} and \l{Qt Design Studio Manual}{\QDS}.
                If you need a traditional user interface that has a clear
                structure and enforces a platform look and feel, you can use
                the integrated \QD. For more information, see
                \l{Designing User Interfaces}.
            \li \b {\l{Coding}}

                As an IDE, \QC differs from a text editor in that it knows how
                to build and run applications. It understands the C++ and QML
                languages as code, not just as plain text. This enables it to
                provide you with useful features, such as semantic highlighting,
                checking code syntax, code completion, and refactoring actions.
                \QC supports some of these services also for other programming
                languages, such as Python, for which a \e {language server} is
                available that provides information about the code to IDEs.
                For more information, see \l{Coding}.
        \row
            \li \inlineimage front-preview.png
            \li \inlineimage front-testing.png
            \li \inlineimage front-publishing.png
        \row
            \li \b {\l{Building and Running}}

                \QC integrates cross-platform systems for build
                automation: qmake, Qbs, CMake, and Autotools. In addition, you
                can import
                projects as \e {generic projects} and fully control the steps
                and commands used to build the project.

                You can build applications for, deploy them to, and run them on
                the desktop environment or a \l{glossary-device}{device}.
                \l{glossary-buildandrun-kit}{Kits}, build, run, and deployment
                settings allow you to quickly switch between different setups and
                target platforms.

                For more information, see \l{Building and Running}.
            \li \b {\l{Testing}}

                \QC integrates several external native debuggers: GNU
                Symbolic Debugger (GDB), Microsoft Console Debugger (CDB), and
                internal JavaScript debugger. In the \uicontrol Debug mode, you
                can inspect the state of your application while debugging.

                Devices have limited memory and CPU power, so
                you should use them carefully. \QC integrates Valgrind code
                analysis tools for detecting memory leaks and profiling function
                execution. In addition, the QML Profiler enables you to profile
                Qt Quick applications.

                \QC integrates the \l{Qt Test}, Boost.Test, Catch 2 test,
                and Google C++ Testing frameworks for unit testing applications
                and libraries. You can use \QC to create, build, and run
                autotests.

                For more information, see \l{Testing}.
            \li \b {Publishing}

                \QC enables you to create installation packages for mobile
                devices that you can publish to application stores
                and other channels. You must make sure that the package contents
                meet the requirements for publishing on the channel.

                For more information, see \l{Publishing to Google Play}.
\endtable

*/
