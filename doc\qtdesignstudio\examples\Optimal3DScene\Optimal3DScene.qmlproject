/* File generated by Qt Creator */

import QmlProject 1.1

Project {
    mainFile: "Optimal3DScene.qml"

    /* Include .qml, .js, and image files from current directory and subdirectories */
    QmlFiles {
        directory: "."
    }

    JavaScriptFiles {
        directory: "."
    }

    ImageFiles {
        directory: "."
    }

    Files {
        filter: "*.conf"
        files: ["qtquickcontrols2.conf"]
    }

    Files {
        filter: "qmldir"
        directory: "."
    }

    Files {
        filter: "*.ttf;*.otf"
    }

    Environment {
        QT_QUICK_CONTROLS_CONF: "qtquickcontrols2.conf"
        QT_AUTO_SCREEN_SCALE_FACTOR: "1"
        QMLSCENE_CORE_PROFILE: "true"
    }

    /* List of plugin directories passed to QML runtime */
    importPaths: [ "imports", "asset_imports" ]

    /* Required for deployment */
    targetDirectory: "/opt/Optimal3DScene"
}
