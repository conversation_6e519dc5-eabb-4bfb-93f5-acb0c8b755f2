// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-components-view.html
    \previouspage studio-material-editor.html
    \nextpage quick-assets.html

    \title Components

    The \uicontrol Component view lists the available components.

    \image qtquick-components-tab.png "Components view"

    \uicontrol Components displays modules that have been
    added to your project. In many modules the components have been further
    organized into different categories. The modules contain visual components,
    such as basic shapes, UI controls, and 3D components, and add functionality
    to the project. All components have a type.

    The UI controls can be styled to have the look and feel of a particular
    operating system, such as \macOS, Windows, Android, or iOS.

    Some modules have no visible components. For example, the
    \uicontrol {Qt.Multimedia} module adds support for audio and video
    files to your UI.

    \section2 Component Types

    You can add the following types of components to your project:

    \list
        \li \l Shapes
        \li \l Text
        \li \l Images
        \li \l {UI Controls}
        \li \l {User Interaction Methods}
        \li \l {Lists and Other Data Models}
        \if defined(qtdesignstudio)
        \li \l {2D Effects}
        \li \l {Logic Helpers}
        \endif
        \li \l Animations
        \li \l{3D Views}
        \li \l{Node}
        \li \l{Group}
        \li \l{Instanced Rendering}
        \li \l{Skeletal Animation}
        \li \l{3D Models}
        \li \l{Materials and Shaders}
        \li \l{Textures}
        \li \l{3D Materials}
        \li \l{3D Effects}
        \li \l{Custom Shaders}
        \li \l{Lights}
        \li \l{Cameras}
        \li \l{Scene Environment}
        \li \l{Morph Target}
        \li \l{Repeater3D}
        \li \l{Particles}
    \endlist

    For more information about creating your own components, see
    \l{Creating Custom Components}.

    \section2 Adding and Removing Modules

    When you \l{Creating Projects}{create projects}, modules typically needed in
    them are added by default. For example, an empty application project
    contains basic components and UI controls, while a 3D application project
    contains additional 3D components.

    To view the list of available modules, select \inlineimage icons/plus.png
    . Most commonly used modules are placed at the top of the list in
    alphabetical order. You can search for components and modules by entering
    search criteria in the \uicontrol Search field.

    Select the module to add it to \uicontrol Components.

    \image qtquick-components-tab-add.png "Select Modules to Add"

    This adds an \e {import statement} to the component code that you
    can see in the \l {Code} view. For example, if you add the
    \uicontrol QtQuick.Controls module, the following import statement
    is added to the code: \c {import QtQuick.Controls}.

    Since the added components and modules are packaged with your UI into the
    final application package, it is recommended that you select
    \uicontrol {Remove Module} to remove the ones you don't use in the project.

    \section1 Context Menu Commands

    \image qtquick-components-context-menu.png "Context menu commands in Components"
    \image qtquick-components-context-menu-hide.png "Context menu command Hide Category"

    To use the context menu commands in \uicontrol Components, right-click the
    name of a module or category and select one of the following commands:

    \list
        \li \uicontrol {Remove Module}: removes the module and all of its
            components from \uicontrol Components.
        \li \uicontrol {Expand All}: expands all the modules.
        \li \uicontrol {Collapse All}: collapses all the modules.
        \li \uicontrol {Hide Category}: hides the category from the module.
        \li \uicontrol {Show Module Hidden Categories}: shows the hidden
            categories of the module.
        \li \uicontrol {Show All Hidden Categories}: shows the hidden
            categories in all of the modules.
    \endlist

    \note The context menu commands for the \uicontrol Components categories do
    not function if you have entered something into the \uicontrol Search field.
    Clear the \uicontrol Search field to resume using the context menu commands.
*/
