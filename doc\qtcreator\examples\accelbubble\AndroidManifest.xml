<?xml version="1.0"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.qtproject.example"
    android:installLocation="auto"
    android:versionCode="-- %%INSERT_VERSION_CODE%% --"
    android:versionName="-- %%INSERT_VERSION_NAME%% --">
    <!-- %%INSERT_PERMISSIONS -->
    <!-- %%INSERT_FEATURES -->
    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true" />
    <application
        android:name="org.qtproject.qt.android.bindings.QtApplication"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:label="-- %%INSERT_APP_NAME%% --"
        android:requestLegacyExternalStorage="true">
        <activity
            android:name="org.qtproject.qt.android.bindings.QtActivity"
            android:configChanges="orientation|uiMode|screenLayout|screenSize|smallestScreenSize|layoutDirection|locale|fontScale|keyboard|keyboardHidden|navigation|mcc|mnc|density"
            android:label="-- %%INSERT_APP_NAME%% --"
            android:launchMode="singleTop"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

          <meta-data
                android:name="android.app.lib_name"
                android:value="-- %%INSERT_APP_LIB_NAME%% --" />

          <meta-data
                android:name="android.app.arguments"
                android:value="-- %%INSERT_APP_ARGUMENTS%% --" />

            <meta-data
                android:name="android.app.extract_android_style"
                android:value="minimal" />
        </activity>
    </application>
</manifest>
