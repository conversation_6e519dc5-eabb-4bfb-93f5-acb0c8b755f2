// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [qtcreator variables]
    \section1 Using Qt Creator Variables

    You can use \QC variables in arguments, executable paths, and working
    directories. The variables take care of quoting their expansions, so you do
    not need to put them in quotes.

    Select the \inlineimage icons/replace.png
    (\uicontrol {Variables}) button in a field to select from a list of
    variables that are available in a particular context.
    For more information about each variable, move the cursor over it in the
    list.

    \image qtcreator-variables.png "Qt Creator Variables dialog displaying a tooltip"

    The following syntax enables you to use environment variables as \QC
    variables: %{Env:VARNAME}.

    \QC uses pattern substitution when expanding variable names. To replace the
    first match of \e pattern within \e variable with \e replacement, use:

    \badcode
    %{variable/pattern/replacement}
    \endcode

    To replace all matches of \e pattern within \e variable with \e replacement,
    use:

    \badcode
    %{variable//pattern/replacement}
    \endcode

    The pattern can be a regular expression and the replacement can contain
    backreferences. For example, if \c %{variable} is \c my123var, then
    \c %{variable/(..)(\d+)/\2\1} is expanded to \c {123myvar}.

    Instead of the forward slash, you can also use the pound sign (\c #) as
    the substitution character. This can be helpful if the value is supposed
    to be a file path, in which case forward slashes might get translated
    to backslashes on Windows hosts.

    To use the default value if the variable is not set, use:

    \badcode
    %{variable:-default}
    \endcode
//! [qtcreator variables]
*/
