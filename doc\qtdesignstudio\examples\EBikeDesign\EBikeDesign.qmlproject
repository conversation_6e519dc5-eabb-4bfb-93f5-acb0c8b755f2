import QmlProject 1.1

Project {
    mainFile: "EBikeDesign.qml"

    /* Include .qml, .js, and image files from current directory and subdirectories */
    QmlFiles {
        directory: "."
    }

    JavaScriptFiles {
        directory: "."
    }

    ImageFiles {
        directory: "."
    }

    Files {
        filter: "*.conf"
        files: ["qtquickcontrols2.conf"]
    }

    Files {
        filter: "qmldir"
        directory: "."
    }

    Files {
        filter: "*.ttf"
        directory: "."
    }

    Environment {
        QT_QUICK_CONTROLS_CONF: "qtquickcontrols2.conf"
        QT_AUTO_SCREEN_SCALE_FACTOR: "1"
    }

    /* List of plugin directories passed to QML runtime */
    importPaths: [ "imports", "backend"]

    /* Required for deployment */
    targetDirectory: "/opt/ebikeDesign"
}
