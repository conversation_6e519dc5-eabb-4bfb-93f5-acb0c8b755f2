// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [using compilation databases]

    \section1 Using Compilation Databases

    The \l{https://clang.llvm.org/docs/JSONCompilationDatabase.html}
    {JSON compilation database format} specifies how to replay single builds
    independently of the build system.

    A \e {compilation database} is basically a list of files and the compiler
    flags that are used to compile the files. The database is used to feed the
    code model with the necessary information for correctly parsing the code
    when you open a file for editing.

    To generate a compilation database from the information that the code model
    has, select \uicontrol Build > \uicontrol {Generate Compilation Database}.

    You can add files, such as non-C files, to the project in
    \e {compile_database.json.files}.

    You can use the experimental Compilation Database Project Manager to open
    the files in a compilation database with access to all the editing features
    provided by the Clang code model.

    To switch between header and source files, select \uicontrol Tools >
    \uicontrol C++ > \uicontrol {Switch Header/Source}.

    You can specify custom build steps and run settings for compilation
    database projects in the \uicontrol Projects mode. For more information,
    see \l{Adding Custom Build Steps} and \l {Specifying Run Settings}.

    To enable the plugin, select \uicontrol Help > \uicontrol {About Plugins} >
    \uicontrol {Build Systems} > \uicontrol {Compilation Database Project Manager}.
    Then select \uicontrol {Restart Now} to restart \QC and load the plugin.

//! [using compilation databases]
*/
