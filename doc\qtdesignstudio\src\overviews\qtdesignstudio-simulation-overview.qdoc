// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-simulation-overview.html
    \previouspage qtquick-creating-ui-logic.html
    \nextpage qtquick-placeholder-data.html

    \title Simulating Complex Experiences

    \QDS enables you to connect UIs to different forms of data from various
    sources, such as QML-based data models, JavaScript files, and backend
    services. You can also connect your UI to Simulink to load live data from a
    Simulink simulation.


    \list
        \li \l{Loading Placeholder Data}

            You can create QML files that contain placeholder data, so that
            you can test grid, list, or path views, even though you don't
            have access to real data.

        \li \l{Simulating Application Logic}

        You can use JavaScript to generate mock data for your UI.

        \li \l{Simulating Dynamic Systems}

        Use the Simulink connector to connect a Simulink Simulation Model to
        your UI. Simulink is a MATLAB-based graphical programming environment
        for modeling, simulating, and analyzing multi-domain dynamic systems.

        \li \l{Using QML Modules with Plugins}

        You can load C++ plugins for QML to simulate data.
    \endlist
*/
