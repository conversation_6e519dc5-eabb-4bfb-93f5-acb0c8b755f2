// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-data-models.html
    \previouspage quick-controls.html
    \if defined(qtdesignstudio)
    \nextpage quick-2d-effects.html
    \else
    \nextpage quick-animations.html
    \endif

    \title Lists and Other Data Models

    Applications typically need to handle and display data that is organized
    into list or grid views. Models, views, and delegates are used for
    this purpose. They modularize the visualization of data in order to give
    you control over the different aspects of the data. For example, you can
    swap a list view with a grid view with little changes to the data.
    Similarly, encapsulating an instance of the data in a delegate allows
    developers to dictate how to present or handle the data.

    A \e model contains the data and its structure. There are several
    components for creating different types of models. A \e view is a container
    that displays the data in a list or a grid, or along a path. A \e delegate
    dictates how the data should appear in the view. The delegate takes each
    piece of data in the model and encapsulates it. The data is then accessible
    through the delegate.

    To visualize data, the view's model property is bound to a model and the
    delegate property to a component.

    For more information, see \l{Models and Views in Qt Quick}.

    \section1 List and Grid Views

    Create instances of \uicontrol {List View} and \uicontrol {Grid View}
    components to organize other component instances in list or grid format.
    They are available in \uicontrol Components >
    \uicontrol {Default Components} > \uicontrol Views.

    A \uicontrol {List View} organizes other components as a list, whereas a
    \uicontrol {Grid View} organizes them as a grid. By default, components
    in list and grid views flow vertically from left to right. They are laid
    out from left to right horizontally, and from top to bottom vertically.

    You can change the list view orientation in the \uicontrol Orientation
    field and the grid view flow in the \uicontrol Flow field. You can change
    the layout direction in the \uicontrol {Layout direction} field. By setting
    values for these properties, you can produce a variety of layouts.

    \image qtquick-designer-listview-properties.png "List View properties"

    For a list view, you can specify space between list items in the
    \uicontrol Spacing field. For a grid view, you can specify the width
    and height of each cell in the \uicontrol W and \uicontrol H fields.

    \image qtquick-designer-gridview-properties.png "Grid View properties"

    Select the \uicontrol {Navigation wraps} check box to specify that key
    navigation wraps around and moves the selection to the next line or cell
    in the other end of the view when it reaches the end of the view.

    Both list and grid views are inherently \l {Flickable}{flickable}.

    The value of the \uicontrol {Snap mode} field determines how the view
    scrolling will settle following a drag or flick. By default, the view
    stops anywhere within the visible area. If you select \uicontrol SnapToRow,
    the view settles with a row (or column for a grid view top-to-bottom flow)
    aligned with the start of the view. If you select \uicontrol SnapOneRow,
    the view will settle no more than one row or column away from the first
    visible row at the time the mouse button is released. This option is
    particularly useful for moving one page at a time.

    \section1 Delegate Cache

    The value of the \uicontrol Cache field determines whether delegates are
    retained outside the visible area of the view.

    If this value is greater than zero, the view may keep as many delegates
    instantiated as will fit within the cache specified. For example, if in
    a vertical view the delegate is 20 pixels high, there are three columns
    and the cache is set to 40, then up to six delegates above and below the
    visible area may be created or retained. The cached delegates are created
    asynchronously, allowing creation to occur across multiple frames and
    reducing the likelihood of skipping frames. To improve painting performance,
    delegates outside the visible area are not painted.

    The default value of this property is platform dependent, but will usually
    be a value greater than zero. Negative values are ignored.

    The cache is not a pixel buffer. It only maintains additional instantiated
    delegates.

    \note Setting the \uicontrol Cache property is not a replacement for
    creating efficient delegates. It can improve the smoothness of scrolling
    behavior at the expense of additional memory usage. The fewer items and
    bindings in a delegate, the faster a view can be scrolled. It is important
    to realize that setting a cache will only postpone issues caused by
    slow-loading delegates, it is not a solution to this problem.

    \section1 View Highlight

    In the \uicontrol {List View Highlight} and \uicontrol {Grid View Highlight}
    sections, you can specify properties for highlighting items in views.

    \image qtquick-designer-listview-highlight-properties.png "List View Highlight properties"

    The current item in a list or grid view is higlighted if you set
    the value of the \uicontrol Range field to \uicontrol ApplyRange
    or \uicontrol StrictlyEnforceRange. When you select to apply the
    range, the view attempts to maintain the highlight within the range.
    However, the highlight can move outside of the range at the ends of
    the view or due to mouse interaction. When you select to enforce the
    range, the highlight never moves outside of the range. The current
    item changes if a keyboard or mouse action would cause the highlight
    to move outside of the range.

    The values of the \uicontrol {Preferred begin} and \uicontrol {Preferred end}
    fields affect the position of the current item when the view is scrolled.
    For example, if the currently selected item should stay in the middle of the
    view when it is scrolled, set the begin and end values to the top and bottom
    coordinates of where the middle item would be. If the current item is
    changed programmatically, the view will automatically scroll so that the
    current item is in the middle of the view. The begin value must be less than
    the end value.

    Select the \uicontrol {Follows current} check box to enable the view to
    manage the highlight. The highlight is moved smoothly to follow the current
    item. Otherwise, the highlight is not moved by the view, and any movement
    must be implemented by the highlight.

    The values of the \uicontrol {Move duration}, \uicontrol {Move velocity},
    \uicontrol {Resize duration}, and \uicontrol {Resize velocity} fields
    control the speed of the move and resize animations for the highlight.

    \section1 Editing List Models

    When you add a \l{GridView}{Grid View}, \l{ListView}{List View}, or
    \l{PathView}{Path View}, the ListModel and the delegate component that
    creates an instance for each item in the model are added automatically.
    For grid and list views, you can edit the list model in \QDS.

    \image qtquick-designer-listview-preview.png "Preview of a list view"

    To edit list models:

    \list 1
        \li Drag-and-drop a \uicontrol {Grid View} or \uicontrol {List View}
            from \uicontrol Components >
            \uicontrol {Default Components} > \uicontrol Views to the
            \uicontrol Navigator or \uicontrol {2D} view.
        \li Right-click the view in \uicontrol Navigator, and select
            \uicontrol {Edit List Model} in the context-menu to open
            the list model editor:
            \image qtquick-designer-edit-list-model.png "List view in model editor"
        \li Double-click the column headings and cells to change their values.
        \li Use the toolbar buttons to add, remove, or move rows and columns.
            In a list, each column represents a property and each row adds a
            list item.
    \endlist

    You can replace the default model and delegate with other,
    more complex models and delegates in the \l {Code} view.
    \l{ItemDelegate}{Item Delegate} and \l{SwipeDelegate}{Swipe Delegate}
    components are also available in \uicontrol Components
    > \uicontrol {Qt Quick Controls}.

    \include qtquick-pathview-editor.qdocinc pathview
    \if defined(qtdesignstudio)
    \include qtquick-pathview-editor.qdocinc svgpath
    \endif

    \section1 Summary of Model Components

    The following table lists the components that you can use to add data models
    to UIs. The \e Location column indicates the location of the component in
    \uicontrol Components. The \e MCU column indicates which
    components are supported on MCUs.

    \table
    \header
        \li Icon
        \li Name
        \li Location
        \li MCU
        \li Purpose
    \row
        \li \inlineimage gridview-icon16.png
        \li \l{GridView}{Grid View}
        \li Default Components - Views
        \li
        \li A grid vizualization of a model.
    \row
        \li \inlineimage icons/itemdelegate-icon16.png
        \li \l{ItemDelegate}{Item Delegate}
        \li Qt Quick Controls
        \li
        \li A standard view item that can be used as a delegate in various
            views and controls, such as \l ListView and \l ComboBox.
    \row
        \li \inlineimage listview-icon16.png
        \li \l{ListView}{List View}
        \li Default Components - Views
        \li \inlineimage ok.png
        \li A list vizualization of a model.
    \row
        \li \inlineimage pathview-icon16.png
        \li \l{Path View}
        \li Default Components - Views
        \li
        \li Vizualizes the contents of a model along a path.
    \row
        \li \inlineimage icons/scrollview-icon16.png
        \li \l [QtQuickControls] {ScrollView}{Scroll View}
        \li Qt Quick Controls
        \li
        \li Provides scrolling for user-defined content. It can be used instead
            of a \l Flickable component.
    \row
        \li \inlineimage icons/stackview-icon16.png
        \li \l [QtQuickControls] {StackView}{Stack View}
        \li Qt Quick Controls
        \li
        \li A stack-based navigation model.
    \if defined(qtdesignstudio)
    \row
        \li \inlineimage icons/item-svg-16px.png
        \li \l{SVG Path Item}
        \li Qt Quick Studio Components
        \li
        \li An SVG path data string that is used to draw a path as a line.
    \endif
    \row
        \li \inlineimage icons/itemdelegate-icon16.png
        \li \l{SwipeDelegate}{Swipe Delegate}
        \li Qt Quick Controls
        \li
        \li A view item that can be swiped left or right to expose more
            options or information. It is used as a delegate in views such
            as \l ListView.
    \row
        \li \inlineimage icons/swipeview-icon16.png
        \li \l[QtQuickControls] {SwipeView}{Swipe View}
        \li Qt Quick Controls
        \li \inlineimage ok.png
        \li Enables users to navigate pages by swiping sideways.
    \endtable
*/
