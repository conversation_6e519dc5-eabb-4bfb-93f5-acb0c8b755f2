// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [python project wizards]

    \section2 Creating Widget-Based Qt for Python Applications

    \l {https://doc.qt.io/qtforpython/index.html}{Qt for Python} enables you
    to use Qt 6 API in Python applications. You can use the PySide6 modules
    to gain access to individual Qt modules, such as \l {Qt Core}, \l {Qt GUI},
    and \l {Qt Widgets}.

    If you have not installed PySide6, \QC prompts you to install it after
    you create the project. Further, it prompts you to install the
    \l {Python Language Server}{Python language server} that provides services
    such as code completion and annotations. Select \uicontrol Install to install
    PySide6 and the language server.

    To view and manage the available Python interpreters, select \uicontrol Edit
    > \uicontrol Preferences > \uicontrol Python > \uicontrol Interpreters.

    \image qtcreator-python-interpreters.png "Python Interpreters in Preferences"

    You can add and remove interpreters and clean up references to interpreters
    that you uninstalled, but that still appear in the list. In addition, you
    can set the interpreter to use by default.

    The Qt for Python Application wizards generate a \c {.pyproject} file that
    lists the files in the Python project and a \c {.py} file that contains
    some boilerplate code. In addition, the widget-based UI wizard creates a
    \c {.ui} file that contains a \QD form, and the Qt Quick Application wizard
    creates a \c {.qml} file that contains Qt Quick controls.

    The \c{.pyproject} files are JSON-based configuration files that replace
    the previously used \c {.pyqtc} configuration files. You can still open and
    use \c {.pyqtc} files, but we recommend that you choose \c{.pyproject} files
    for new projects.

    The \uicontrol {Window UI} wizard enables you to
    create a Python project that contains the source file for a class. Specify
    the PySide version, class name, base class, and and source file for the
    class.

    \image qtcreator-python-wizard-app-window.png "Qt for Python wizard for creating a widget-based UI"

    The wizard adds the imports to the source file to provide
    access to the QApplication, the base class you selected in the Qt
    Widgets module, and Qt UI tools:

    \badcode
    import sys

    from PySide6.QtWidgets import QApplication, QWidget
    \endcode

    \note It is important that you first create the Python code
    from your UI form. In PySide6, you can do this by executing
    \c{pyside6-uic form.ui -o ui_form.py} on a terminal. This
    enables you to import the class that represents your UI
    from that Python file.

    Once you generate the Python code from the UI file,
    you can import the class:

    \badcode
    from ui_form import Ui_Widget
    \endcode

    The wizard also adds a main class with the specified name that
    inherits from the specified base class:

    \badcode
    class Widget(QWidget):
        def __init__(self, parent=None):
            super().__init__(parent)
    \endcode

    The following lines in the main class instantiate the generated Python class from
    your UI file, and set up the interface for the current class.

    \badcode
            self.ui = Ui_Widget()
            self.ui.setupUi(self)
    \endcode

    \note You can access the UI elements of the new class as member variables.
    For example, if you have a button called \e{button1}, you
    can interact with it using \c{self.ui.button1}.

    Next, the wizard adds a main function, where it creates a
    QApplication instance. As Qt can receive arguments from the command line,
    you can pass any arguments to the QApplication object. Usually, you do not
    need to pass any arguments, and you can use the following approach:

    \badcode
    if __name__ == "__main__":
        app = QApplication(sys.argv)
    \endcode

    Next, the wizard instantiates the \c MainWindow class and shows it:

    \badcode
        widget = Widget()
        widget.show()
        ...
    \endcode

    Finally, the wizard calls the \c app.exec() method to enter the Qt
    main loop and start executing the Qt code:

    \badcode
        sys.exit(app.exec())
    \endcode

    You can now modify the boilerplate code in the Edit mode to develop your
    Python application. Select \uicontrol REPL on the toolbar to start the
    \l{https://pythonprogramminglanguage.com/repl/}{Python interactive shell}.
    To start the shell and import the current file as a module, select
    select \uicontrol {REPL Import File}. To also import all functions from
    the file, select \uicontrol {REPL Import *}.

    Always regenerate the Python code after modifying a UI file.

    Open the .ui file in the \uicontrol Design mode to create a widget-based UI
    in \QD.

    The \uicontrol Window wizard adds similar code to the source file, without
    the UI bits.

    The \uicontrol Empty wizard adds similar code to the source file, but it
    does not add any classes, so you need to add and instantiate them yourself.

    For more information about the
    \uicontrol {Qt for Python - Qt Quick Application - Empty} wizard, see
    \l {Creating Qt Quick Based Python Applications}.

    For examples of creating Qt for Python applications, see
    \l {https://doc.qt.io/qtforpython/tutorials/index.html}
    {Qt for Python Examples and Tutorials}.

//! [python project wizards]


//! [python qml project wizards]

   \section1 Creating Qt Quick Based Python Applications

    The \uicontrol {Qt for Python - Qt Quick Application - Empty} wizard enables
    you to create a Python project that contains a main QML file. Specify the
    minimum PySide version to run the application.

    \image qtcreator-python-wizard-qml.png "Qt for Python wizard for creating an empty Qt Quick application"

    The wizard adds the following imports to the source file to provide access
    to QGuiApplication and QQmlApplicationEngine:

    \badcode
    import sys
    from pathlib import Path

    from PySide6.QtGui import QGuiApplication
    from PySide6.QtQml import QQmlApplicationEngine
    \endcode

    The wizard also adds a main function, where it creates a QGuiApplication
    instance and passes system arguments to the QGuiApplication object:

    \badcode
    if __name__ == "__main__":
        app = QGuiApplication(sys.argv)
    ...
    \endcode

    The following lines in the main class create a QQmlApplicationEngine
    instance and load the generated QML file to the engine object:

    \badcode
        engine = QQmlApplicationEngine()
        qml_file = Path(__file__).resolve().parent / "main.qml"
        engine.load(qml_file)
    \endcode

    Finally, the wizard adds code that checks whether the file was successfully
    loaded. If loading the file fails, the application exits with an error code.
    If loading succeeds, the wizard calls the \c app.exec() method to enter the
    Qt main loop and start executing the Qt code:

    \badcode
    if not engine.rootObjects():
        sys.exit(-1)
    sys.exit(app.exec())
    \endcode

    Open the .qml file in the \uicontrol Edit mode to design a Qt Quick UI, or
    use \l{Qt Design Studio Manual}{\QDS}.

//! [python qml project wizards]
*/
