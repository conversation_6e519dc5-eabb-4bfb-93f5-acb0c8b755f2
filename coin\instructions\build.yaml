type: Group
instructions:
  - type: MakeDirectory
    directory: "{{.AgentWorkingDir}}/build/qt_temp"

  - type: Group
    instructions:
      - type: ExecuteCommand
        command: "curl --fail -L --retry 5 --retry-delay 5 -o {{.AgentWorkingDir}}/build/qt_temp/elfutils-release_0.175qt-linux-x86_64.7z http://master.qt.io/development_releases/prebuilt/elfutils/elfutils-release_0.175qt-linux-x86_64.7z"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to download elfutils package, check logs."
      - type: ExecuteCommand
        command: "/usr/bin/7z x -y {{.AgentWorkingDir}}/build/qt_temp/elfutils-release_0.175qt-linux-x86_64.7z -o{{.AgentWorkingDir}}/build/qt_temp/elfutils"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to extract elfutils package, check logs."
      - type: ExecuteCommand
        command: "curl --fail -L --retry 5 --retry-delay 5 -o {{.AgentWorkingDir}}/build/qt_temp/libclang.7z {{.Env.LLVM_BASE_URL}}-linux-Rhel8.2-gcc9.2-x86_64.7z"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to download LLVM package, check logs."
      - type: ExecuteCommand
        command: "/usr/bin/7z x -y {{.AgentWorkingDir}}/build/qt_temp/libclang.7z -o{{.AgentWorkingDir}}/build/qt_temp/"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to extract LLVM package, check logs."
      - type: ExecuteCommand
        command: "python3 -u {{.AgentWorkingDir}}/qt-creator/qt-creator/scripts/build.py --build-type {{.Env.QTC_BUILD_TYPE}} --src {{.AgentWorkingDir}}/qt-creator/qt-creator --build {{.AgentWorkingDir}}/qt-creator/qt-creator_build --qt-path {{.AgentWorkingDir}}/build/qt_install_dir --elfutils-path {{.AgentWorkingDir}}/build/qt_temp/elfutils --llvm-path {{.AgentWorkingDir}}/build/qt_temp/libclang --with-tests --no-zip --add-config=-DCMAKE_C_COMPILER_LAUNCHER=sccache --add-config=-DCMAKE_CXX_COMPILER_LAUNCHER=sccache"
        maxTimeInSeconds: 36000
        maxTimeBetweenOutput: 3600
        userMessageOnFailure: "Failed to run build.py, check logs."
      - type: ChangeDirectory
        directory: "{{.AgentWorkingDir}}/build/qtsdk/packaging-tools"
      - type: ExecuteCommand
        command: "python3 -m pipenv run python -u bld_sdktool.py --qt-url {{.Env.QTC_SDKTOOL_QT_BASE_URL}}{{.Env.QTC_SDKTOOL_QT_EXT}} --qt-build {{.AgentWorkingDir}}/build/sdktool/qt --src {{.AgentWorkingDir}}/qt-creator/qt-creator/src/tools/sdktool --build {{.AgentWorkingDir}}/build/sdktool/build --install {{.AgentWorkingDir}}/build/sdktool/install --make-command make"
        maxTimeInSeconds: 36000
        maxTimeBetweenOutput: 3600
        userMessageOnFailure: "Failed to build sdktool, check logs."
    enable_if:
      condition: and
      conditions:
        - condition: property
          property: host.os
          equals_value: Linux
        - condition: property
          property: target.compiler
          in_values: [GCC]

  - type: Group
    instructions:
      - type: ExecuteCommand
        command: "curl --fail -L --retry 5 --retry-delay 5 -o {{.AgentWorkingDir}}/build/qt_temp/libclang.7z {{.Env.LLVM_BASE_URL}}-macos-universal.7z"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to download LLVM package, check logs."
      - type: ExecuteCommand
        command: "/usr/local/bin/7z x -y {{.AgentWorkingDir}}/build/qt_temp/libclang.7z -o{{.AgentWorkingDir}}/build/qt_temp/"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to extract LLVM package, check logs."
      - type: ExecuteCommand
        command: "python3 -u {{.AgentWorkingDir}}/qt-creator/qt-creator/scripts/build.py --build-type {{.Env.QTC_BUILD_TYPE}} --src {{.AgentWorkingDir}}/qt-creator/qt-creator --build {{.AgentWorkingDir}}/qt-creator/qt-creator_build --qt-path {{.AgentWorkingDir}}/build/qt_install_dir --llvm-path {{.AgentWorkingDir}}/build/qt_temp/libclang --keychain-unlock-script /Users/<USER>/unlock-keychain.sh --with-tests --no-zip --add-config=-DCMAKE_C_COMPILER_LAUNCHER=sccache --add-config=-DCMAKE_CXX_COMPILER_LAUNCHER=sccache"
        maxTimeInSeconds: 36000
        maxTimeBetweenOutput: 3600
        userMessageOnFailure: "Failed to run build.py, check logs."
      - type: ChangeDirectory
        directory: "{{.AgentWorkingDir}}/build/qtsdk/packaging-tools"
      - type: ExecuteCommand
        command: "python3 -m pipenv run python -u bld_sdktool.py --qt-url {{.Env.QTC_SDKTOOL_QT_BASE_URL}}{{.Env.QTC_SDKTOOL_QT_EXT}} --qt-build {{.AgentWorkingDir}}/build/sdktool/qt --src {{.AgentWorkingDir}}/qt-creator/qt-creator/src/tools/sdktool --build {{.AgentWorkingDir}}/build/sdktool/build --install {{.AgentWorkingDir}}/build/sdktool/install --make-command make"
        maxTimeInSeconds: 36000
        maxTimeBetweenOutput: 3600
        userMessageOnFailure: "Failed to build sdktool, check logs."
    enable_if:
      condition: property
      property: host.os
      equals_value: MacOS

  - type: Group
    instructions:
      - type: ExecuteCommand
        command: "curl --fail -L --retry 5 --retry-delay 5 -o {{.AgentWorkingDir}}\\build\\qt_temp\\elfutils-release_0.175qt-windows-x86_64.7z http://master.qt.io/development_releases/prebuilt/elfutils/elfutils-release_0.175qt-windows-x86_64.7z"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to download elfutils package, check logs."
      - type: ExecuteCommand
        command: "C:\\Utils\\sevenzip\\7z.exe x -y {{.AgentWorkingDir}}\\build\\qt_temp\\elfutils-release_0.175qt-windows-x86_64.7z -o{{.AgentWorkingDir}}\\build\\qt_temp\\elfutils"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to extract elfutils package, check logs."
      - type: ExecuteCommand
        command: "curl --fail -L --retry 5 --retry-delay 5 -o {{.AgentWorkingDir}}\\build\\qt_temp\\Python38-win-x64.7z http://master.qt.io/development_releases/prebuilt/python/Python38-win-x64.7z"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to download python package, check logs."
      - type: ExecuteCommand
        command: "C:\\Utils\\sevenzip\\7z.exe x -y {{.AgentWorkingDir}}\\build\\qt_temp\\Python38-win-x64.7z -o{{.AgentWorkingDir}}\\build\\qt_temp\\python"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to extract python package, check logs."
      - type: ExecuteCommand
        command: "curl --fail -L --retry 5 --retry-delay 5 -o {{.AgentWorkingDir}}\\build\\qt_temp\\libclang.7z {{.Env.LLVM_BASE_URL}}-windows-vs2019_64.7z"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to download LLVM package, check logs."
      - type: ExecuteCommand
        command: "C:\\Utils\\sevenzip\\7z.exe x -y {{.AgentWorkingDir}}\\build\\qt_temp\\libclang.7z -o{{.AgentWorkingDir}}\\build\\qt_temp\\"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to extract LLVM package, check logs."
      - type: ExecuteCommand
        command: "python -u {{.AgentWorkingDir}}\\qt-creator\\qt-creator\\scripts\\build.py --build-type {{.Env.QTC_BUILD_TYPE}} --src {{.AgentWorkingDir}}\\qt-creator\\qt-creator --build {{.AgentWorkingDir}}\\qt-creator\\qt-creator_build --qt-path {{.AgentWorkingDir}}/build/qt_install_dir --python-path {{.AgentWorkingDir}}\\build\\qt_temp\\python --elfutils-path {{.AgentWorkingDir}}\\buid\\qt_temp\\elfutils --llvm-path {{.AgentWorkingDir}}\\build\\qt_temp\\libclang --with-tests --no-zip --add-config=-DCMAKE_C_COMPILER_LAUNCHER=sccache --add-config=-DCMAKE_CXX_COMPILER_LAUNCHER=sccache --add-config=-DWITH_SCCACHE_SUPPORT=ON"
        maxTimeInSeconds: 36000
        maxTimeBetweenOutput: 3600
        userMessageOnFailure: "Failed to run build.py, check logs."
      - type: ChangeDirectory
        directory: "{{.AgentWorkingDir}}\\build\\qtsdk\\packaging-tools"
      - type: ExecuteCommand
        command: "python -m pipenv run python -u bld_sdktool.py --qt-url {{.Env.QTC_SDKTOOL_QT_BASE_URL}}{{.Env.QTC_SDKTOOL_QT_EXT}} --qt-build {{.AgentWorkingDir}}\\build\\sdktool\\qt --src {{.AgentWorkingDir}}\\qt-creator\\qt-creator\\src\\tools\\sdktool --build {{.AgentWorkingDir}}\\build\\sdktool\\build --install {{.AgentWorkingDir}}\\build\\sdktool\\install --make-command nmake"
        maxTimeInSeconds: 36000
        maxTimeBetweenOutput: 3600
        userMessageOnFailure: "Failed to build sdktool, check logs."
    enable_if:
      condition: and
      conditions:
        - condition: property
          property: target.compiler
          in_values: [MSVC2013, MSVC2015, MSVC2017, MSVC2019]
        - condition: property
          property: target.arch
          equals_value: X86_64

  - type: Group
    instructions:
      - type: MakeDirectory
        directory: "{{.AgentWorkingDir}}/build/qt_temp"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to create folder qt_temp, check logs."
      - type: ExecuteCommand
        command: "curl --fail -L --retry 5 --retry-delay 5 -o {{.AgentWorkingDir}}\\build\\qt_temp\\Python38-win-x86.7z http://master.qt.io/development_releases/prebuilt/python/Python38-win-x86.7z"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to download python package, check logs."
      - type: ExecuteCommand
        command: "C:\\Utils\\sevenzip\\7z.exe x -y {{.AgentWorkingDir}}\\build\\qt_temp\\Python38-win-x86.7z -o{{.AgentWorkingDir}}\\build\\qt_temp\\python"
        maxTimeInSeconds: 3600
        maxTimeBetweenOutput: 360
        userMessageOnFailure: "Failed to extract python package, check logs."
      - type: ExecuteCommand
        command: "python -u {{.AgentWorkingDir}}\\qt-creator\\qt-creator\\scripts\\build.py --build-type {{.Env.QTC_BUILD_TYPE}} --src {{.AgentWorkingDir}}\\qt-creator\\qt-creator --build {{.AgentWorkingDir}}\\qt-creator\\qt-creator_build --python-path {{.AgentWorkingDir}}\\buid\\qt_temp\\python --no-qtcreator --no-zip"
        maxTimeInSeconds: 36000
        maxTimeBetweenOutput: 3600
        userMessageOnFailure: "Failed to run build.py, check logs."
    enable_if:
      condition: and
      conditions:
        - condition: property
          property: target.compiler
          in_values: [MSVC2013, MSVC2015, MSVC2017, MSVC2019]
        - condition: property
          property: target.arch
          equals_value: X86

  - type: UploadArtifact
    archiveDirectory: "{{.AgentWorkingDir}}/qt-creator/qt-creator_build/build"
    transferType: UploadModuleBuildArtifact
    maxTimeInSeconds: 1800
    maxTimeBetweenOutput: 1800

enable_if:
  condition: property
  property: features
  not_contains_value: "Qt5"
