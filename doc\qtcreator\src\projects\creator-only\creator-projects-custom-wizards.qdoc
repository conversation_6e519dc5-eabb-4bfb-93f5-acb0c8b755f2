// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-qmake-libraries.html
    \page creator-project-wizards.html
    \nextpage creator-version-control.html

    \title Adding New Custom Wizards

    If you have a team working on a large application or several applications,
    you might want to standardize the way the team members create projects and
    files.

    You can create custom wizards in JSON format. They are stored in wizard
    template directories that contain a JSON configuration file called
    \c {wizard.json} and any template files needed. The configuration file
    contains sections that specify information about the wizard, variables
    that you can use, wizard pages, and generators for creating files.

    To create a customized wizard, copy a template directory to the shared
    directory or the local user's settings directory under a new name. Then
    change the wizard id in the \c {wizard.json} file.

    You can create a subdirectory for the templates in the settings directory.
    \QC organizes the standard wizards into subdirectories by type, but you can
    add your wizard directory to any directory you like. The folder hierarchy
    does not affect the order in which \QC displays wizards.

    To share the wizard with other users, you can create an archive of the
    wizard directory and instruct the recipients to extract it into one of the
    directories \QC searches wizards from.

    \QC displays the wizards that it finds in the \uicontrol {New Project}
    and \uicontrol {New File} dialogs. For each wizard, it shows an icon (1), a
    display name (2), and a description (3).

    \image qtcreator-custom-wizard.png

    \section1 Wizard Types

    In a project wizard, you can specify the files needed in a project.
    You can add wizard pages to allow developers to specify settings for the
    project.

    A file wizard is similar, but does not contain any project file.

    \section1 Locating Wizards

    \QC searches the following locations for wizards:

    \list

        \li Shared directory:
            \list
                \li On Windows: \c {share\qtcreator\templates\wizards}
                \li On Linux: \c {share/qtcreator/templates/wizards}
                \li On \macos:
                    \c{Qt Creator.app/Contents/Resources/templates/wizards}
            \endlist
        \li Local user's settings directory:
            \list
                \li On Windows:
                    \c {%APPDATA%\QtProject\qtcreator\templates\wizards}
                \li On Linux and \macos:
                    \c {$HOME/.config/QtProject/qtcreator/templates/wizards}
            \endlist
    \endlist

    \section1 Tips for Wizard Development

    Assign keyboard shortcuts to some helper actions and turn on verbose output.

    \section2 Mapping Actions to Keyboard Shortcuts

    \QC has some actions that can improve the wizard development process. They
    don't have keyboard shortcuts by default, so you cannot trigger them. To
    enable them, assign keyboard shortcuts in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Environment > \uicontrol Keyboard >
    \uicontrol Wizard.

    The following actions can help with wizard development:

    \table
        \header
            \li Action Id
            \li Description

        \row
            \li \uicontrol Inspect
            \li Triggering this action opens a window that lists all the
                defined fields and variables in the wizard at the time the
                action was triggered. Each activation of this action opens a
                new non-modal window, so you can compare state at different
                pages of the wizard for example.

        \row
            \li \uicontrol {Factory.Reset}
            \li Triggering this action makes \QC forget all wizard factories,
                causing it to reload all wizard definitions when for example
                opening \uicontrol File > \uicontrol {New Project}.
                This way you can avoid having to restart \QC for your changes to
                a wizard definition to become visible.
    \endtable

    \section2 Verbose Output

    For wizard development, we recommend that you start \QC with the
    \c {-customwizard-verbose} argument to receive confirmation that \QC
    finds and parses the \c {wizard.json} file. The verbose mode displays
    information about syntax errors, which are the most typical errors you might
    run into while editing wizards.

    In verbose mode, each correctly set up wizard produces output along the
    following lines:

    \code
    Checking "/home/<USER>/.config/QtProject/qtcreator/templates/wizards/mywizard"
    for wizard.json.
    * Configuration found and parsed.
    \endcode

    The output includes the name of the directory that \QC checks for a
    \c wizard.json file. If it does not find the file, it does not show
    the message.

    If the file contains errors, such as an invalid icon path, you receive the
    following types of messages:

    \code
    Checking "/home/<USER>/.config/QtProject/qtcreator/templates/wizards/mywizard"
    for wizard.json.
    * Configuration found and parsed.
    * Failed to create: Icon file
    "/home/<USER>/.config/QtProject/qtcreator/templates/wizards/mywizard/../..
    /global/genericfilewizard.png" not found.
    \endcode

    See \l{Using Command Line Options} for more information about command line
    arguments.

    \include creator-projects-custom-wizards-json.qdocinc json wizards
*/
