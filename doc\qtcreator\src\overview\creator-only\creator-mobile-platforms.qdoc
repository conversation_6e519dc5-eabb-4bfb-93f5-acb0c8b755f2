// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-mobile-platforms.html
    \previouspage creator-embedded-platforms.html
    \nextpage creator-project-other.html

    \title Mobile Platforms

    You can develop applications for the following mobile platforms:

    \list
        \li \l Android
        \li \l iOS
    \endlist

    You must install the tool chain for building applications for the targeted
    mobile platform on the development PC and use the Qt Maintenance Tool to
    install Qt libraries that are built for the platform. You can then add a
    \l{glossary-buildandrun-kit}{kit} with the tool chain and the Qt version
    for the device's architecture. When possible, the Maintenance Tool creates
    suitable kits for you.

    You can connect mobile devices to the development PC and select the
    appropriate kit to build, run, debug, and analyze applications from \QC.

    \section1 Android

    Starting from Qt 5.14.0, the Qt for Android package contains all the
    architectures (ABIs) installed as one. You can let \QC automatically
    create kits for installed Qt version and tool chains.

    The following topics contain more information about developing applications
    for Android devices:

    \list
        \li \l{Connecting Android Devices}
        \li \l{Deploying to Android}
        \li \l{Running on Multiple Platforms}
        \li \l{Creating a Mobile Application}
        \li \l{Debugging on Android Devices}
        \li \l{Qt for Android}
    \endlist

    \section1 iOS

    To be able to use \QC on \macos, you must install Xcode, and therefore
    you should already have the tool chain for building applications for
    iOS. \QC automatically detects the tool chain and creates the necessary
    kits to build applications for and run them on configured iOS devices.

    The following topics contain more information about developing applications
    for iOS devices:

    \list
        \li \l{Connecting iOS Devices}
        \li \l{Running on Multiple Platforms}
        \li \l{Creating a Mobile Application}
        \li \l{Qt for iOS}
    \endlist

*/
