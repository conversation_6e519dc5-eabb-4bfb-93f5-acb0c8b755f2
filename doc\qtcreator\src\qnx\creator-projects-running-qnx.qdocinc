// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [running on qnx]

    \section1 Running on QNX Devices

    \list 1

        \li Connect the device to the development PC or to the Wi-Fi network.

        \li Configure the device and specify a connection to it. For more
            information, see \l{Connecting QNX Devices}.

        \li Make sure that your kit has your QNX device set.

        \li Click the \uicontrol Run button.

    \endlist

    \QC uses the compiler specified in the QNX tool chain to build the
    application.

    \note Debugging is currently only fully supported on Linux and \macos.
    It is not possible to insert breakpoints during runtime on Windows.

    \section2 Troubleshooting Errors

    To support running, debugging, and stopping applications from \QC, the QNX
    Neutrino RTOS should provide a few additional command line tools and
    services, as described in \l {Qt for QNX}.

    \section3 Debug Output Cannot Be Shown

    For the command-line output to show up in the \uicontrol{Application Output},
    \QC needs to be able to establish an SSH connection to the device.
    This is only possible if QNX Momentics is not running, and the SSH key
    configured for the device is a 4096-bit key.

    If these conditions are not met, you will get an error message saying debug
    output cannot be shown.

    \section3 Cannot Run, Debug, or Stop Applications

    The board support package (BSP) for the QNX device might be missing some
    of the following applications that \QC needs to run, debug, and stop
    applications on QNX devices: \c awk, \c grep, \c kill, \c netstat, \c print,
    \c printf, \c ps, \c read, \c sed, \c sleep, \c uname, \c slog2info, and
    \c cat.

//! [running on qnx]
*/
