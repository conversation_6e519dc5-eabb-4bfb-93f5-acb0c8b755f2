// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-configuring-projects.html
    \page creator-targets.html
    \nextpage creator-project-qmake.html

    \title Adding Kits

    \QC groups settings used for building and running projects as kits
    to make cross-platform and cross-configuration development easier. Each kit
    consists of a set of values that define one environment, such as a
    \l{glossary-device}{device}, compiler, Qt version, and debugger command
    to use, and some metadata, such as an icon and a name for the kit. Once
    you have defined kits, you can select them to build and run projects.

    \QC supports development for the desktop and for the following types of
    devices:

    \list
        \li \l{Connecting Android Devices}{Android Device}
        \li \l{Connecting Bare Metal Devices}{Bare Metal Device}
        \li \l{https://doc.qt.io/Boot2Qt/b2qt-installation-guides.html}
            {Boot2Qt Device} (commercial only)
        \li \l{Emulator}{Boot2Qt Emulator Device} (commercial only)
        \li \l{Connecting Remote Linux Devices}{Remote Linux Device}
        \li \l{Connecting iOS Devices}{iOS Device}
        \li iOS Simulator
        \li \l{Connecting MCUs}{MCU Device} (commercial only)
        \li \l{Connecting QNX Devices}{QNX Device}
        \li \l{Building Applications for the Web}{WebAssembly Runtime}
    \endlist

    \section1 Filtering Kit Settings

    Typically, only a subset of the kit settings is relevant for a particular
    setup. Therefore, \QC plugins register sets of relevant settings that you
    can view and modify in \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Kits. For example, if you use CMake to build all your projects,
    you can hide Qbs and qmake settings by default.

    \image qtcreator-kits.png

    To hide and show settings in the \uicontrol Kits tab for the
    current kit, select \uicontrol {Settings Filter}. To view and
    modify the settings displayed when you add a new kit, select
    \uicontrol {Default Settings Filter}.

    \section1 Specifying Kit Settings

    To add kits:

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
            \uicontrol Add.

            To clone the selected kit, select \uicontrol Clone.

        \li Specify kit settings. The settings to specify depend on the build
            system and device type.

        \li Select \uicontrol OK to create the kit.

    \endlist

    \QC uses the \e {default kit} if it does not have enough information to
    choose the kit to use. To set the selected kit as the default kit,
    select \uicontrol {Make Default}.

    \section2 Kit Settings

    The following table summarizes the available kit settings.

    \table
    \header
        \li Setting
        \li Value
    \row
        \li \uicontrol Name
        \li Name of the kit. You can use variables to generate the kit name
            based on the values you set in the other fields.
    \row
        \li \inlineimage icons/qtcreator-desktopdevice-button.png
        \li Image to use as an icon for the kit.
    \row
        \li \uicontrol {File system name}
        \li Name for the kit to use as a part of directory names. This value is
            used for the \c CurrentKit:FileSystemName variable, which determines
            the name of the shadow build directory, for example.
    \row
        \li \uicontrol{Device type}
        \li Type of the device.

            Double-click the icon next to the field to select the image that is
            displayed in the kit selector for this kit. You can use any
            image in a supported file format (for example, PNG). The image is
            scaled to the size 64x64 pixels. For example, using the compiler
            logo as an icon allows you to easily see, which compiler is used to
            build the project for the selected kit.
    \row
        \li \uicontrol Device
        \li The device to run applications on.
    \row
        \li \uicontrol {Build device}
        \li The device to build applications on.

    \row
        \li \uicontrol Sysroot
        \li Directory where the device image is located. If you are not
            cross-compiling, leave this field empty.
    \row
        \li \uicontrol {Emulator skin}
        \li Skin to use for the \l {Emulator}{Boot2Qt Emulator Device}.
    \row
        \li \uicontrol {Compiler}
        \li C or C++ compiler that you use to build the project. You can add
            compilers to the list if they are installed on the development PC,
            but were not detected automatically. For more information, see
            \l{Adding Compilers}.

            This setting is used to tell the code model which compiler is used.
            If your project type and build tool support it, \QC also tells the
            build tool to use this compiler for building the project.
    \row
        \li \uicontrol Environment
        \li Select \uicontrol Change to modify environment variable values for
            build environments in the \uicontrol {Edit Environment Changes}
            dialog. For more information about how to add and remove variable
            values, see \l{Batch Editing}.
    \row
        \li \uicontrol {Force UTF-8 MSVC compiler output}
        \li Either switches the language of MSVC to English or keeps the
            language setting and just forces UTF-8 output, depending on the
            MSVC compiler used.
    \row
        \li \uicontrol Debugger
        \li Debugger to debug the project on the target platform. \QC
            automatically detects available debuggers and displays a
            suitable debugger in the field. You can add debuggers to the list.
            For more information, see \l{Adding Debuggers}.

            For Android kits, the \uicontrol {Android GDB server} field will
            display the path to GDB server executable.
    \row
        \li \uicontrol {Qt version}
        \li Qt version to use for building the project. You can add Qt versions
            that \QC did not detect automatically. For more information, see
            \l{Adding Qt Versions}.

            \QC checks the directories listed in the \c{PATH} environment
            variable for the qmake executable. It refers to the qmake executable
            it finds as \b{Qt in PATH} and selects it as the Qt version
            to use for the \uicontrol Desktop kit that is created by default.
    \row
        \li \uicontrol {Qt mkspec}
        \li Name of the mkspec configuration that qmake uses. If you leave this
            field empty, it uses the default mkspec of the selected Qt version.
    \row
        \li \uicontrol {Additional Qbs profile settings}
        \li Select \uicontrol Change to add settings to Qbs build profiles.
            For more information, see \l {Editing Qbs Profiles}.
    \row
        \li \uicontrol {CMake Tool}
        \li CMake executable to use for building the project. Select
            \uicontrol Manage to add installed CMake executables to
            the list. For more information, see \l{Adding CMake Tools}.
    \row
        \li \uicontrol {CMake generator}
        \li Select \uicontrol Change to edit the CMake Generator to use for
            producing project files. Only the generators
            with names beginning with the string \uicontrol CodeBlocks produce
            all the necessary data for the \QC code model. \QC displays a
            warning if you select a generator that is not supported.
            For more information, see \l{Using Ninja as a CMake Generator}.
    \row
        \li \uicontrol {CMake configuration}
        \li Select \uicontrol Change to edit the parameters of the CMake
            configuration for the kit.
    \row
        \li \uicontrol {Meson tool}
        \li Meson tool to use for building the project. Select \uicontrol Manage
            to add installed Meson tools to the list. For more information, see
            \l{Adding Meson Tools}.
    \row
        \li \uicontrol {Ninja tool}
        \li Ninja tool to use for building the project with Meson. Select
            \uicontrol Manage to add installed Ninja tools to the list.
    \endtable

    \section1 Editing Qbs Profiles

    To view the Qbs profile associated with the kit, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Qbs > \uicontrol Profiles.

    \image creator-qbs-profiles.png "Qbs Profiles tab"

    You can add keys and values to the profile or remove them from it, as well
    as modify existing values. For a list of available keys and values, see
    \l{http://doc.qt.io/qbs/list-of-modules.html}{List of Modules} in the Qbs
    Manual.

    To edit the Qbs profile associated with the kit:

    \list 1

        \li In \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits, select
            the kit, and then select \uicontrol Change next to the
            \uicontrol {Additional Qbs Profile Settings} field to open the
            \uicontrol {Custom Properties} dialog.

            \image qtcreator-qbs-profile-settings.png "Custom Properties dialog"

        \li Double-click an empty cell in the \uicontrol Key column to specify
            the key to add or modify as: \c <module_name>.<property_name>.

        \li Double-click the cell on the same row in the \uicontrol Value column
            to specify a value as a JSON literal.

        \li Select \uicontrol Add to add the key-value pair.

        \li Click \uicontrol OK.

    \endlist

    To modify an existing value, double-click it in the \uicontrol Value field.

    To remove the selected property, select \uicontrol Remove.

*/
