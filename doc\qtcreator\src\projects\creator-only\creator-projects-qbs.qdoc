// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-project-cmake.html
    \page creator-project-qbs.html
    \nextpage creator-projects-autotools.html

    \title Setting Up Qbs

    To use Qbs to build a project, you must create a .qbs file for the project.
    You can use \QC to create a C or C++ project that is built with Qbs.
    For more information about Qbs, see
    the \l{http://doc.qt.io/qbs/index.html}{Qbs Manual}.

    The application is built using the default Qbs profile that is associated
    with the build and run kit. \QC automatically creates a Qbs profile for each
    kit. You can edit the build profiles by adding new keys and values.

    To check which Qbs version is being used, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Qbs > \uicontrol General.

    \section1 Building Qbs

    If you build \QC yourself from the \QC Git repository, you also need to
    fetch the Qbs submodule to get Qbs support:

    \list 1

        \li Fetch the Qbs submodule in your \QC git checkout with
            \c {git submodule update --init}.

        \li Run qmake on \QC and build \QC again.

    \endlist

    \section1 Specifying Qbs Settings

    By default, Qbs profiles are stored in the \c qbs directory in the \QC
    settings directory to ensure that different \QC instances do not overwrite
    each other's profiles. If you only run one \QC instance, you can store the
    profiles in the Qbs settings directory instead.

    To specify settings for Qbs:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Qbs.
            \image qtcreator-options-qbs.png "Qbs preferences"
        \li Deselect the \uicontrol {Use \QC settings directory for Qbs} check
            box to store Qbs profiles in the Qbs settings directory.
        \li In the \uicontrol {Path to qbs executable} field, you can view
            and change the path to the Qbs executable.
            The \uicontrol {Qbs version} field displays the version number
            of the executable.
        \li In the \uicontrol {Default installation directory} field, you
            can view and change the Qbs installation directory.
        \li Select the \uicontrol Profiles tab to specify settings for Qbs
            profiles.
            \image creator-qbs-profiles.png "Qbs Profiles tab"
        \li In the \uicontrol Kit field, select a build and run kit to view
            the properties of the associated profile. To modify the properties,
            select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits.
            For more information, see \l{Editing Qbs Profiles}.
    \endlist

    \section1 Related Topics

    \list
        \li \l {Opening Projects}
        \li \l {Qbs Build Configuration}
        \li \l {Specifying Run Settings}
    \endlist
*/
