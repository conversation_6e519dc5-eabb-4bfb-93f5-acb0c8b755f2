// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-project-generic.html
    \page creator-project-nimble.html
    \nextpage creator-project-meson.html

    \title Setting Up Nimble

    \l {https://github.com/nim-lang/nimble#readme}{Nimble} is a package
    manager for the Nim programming language. It is delivered with
    \l{https://nim-lang.org/}{Nim} and uses the Nim compiler to generate
    executables that are supported on Windows, Linux, and \macos.

    To use \QC for Nim development, you need to enable the experimental
    Nim plugin. Select \uicontrol Help > \uicontrol {About Plugins} >
    \uicontrol {Other Languages} > \uicontrol Nim. Then select
    \uicontrol {Restart Now} to restart \QC and load the plugin.

    In addition, you have to download and install Nim and set up a Nim kit
    in \QC.

    You can use wizards to create Nim and Nimble projects.

    \section1 Setting Up the Development Environment

    To configure \QC to build Nim executables:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits
            \uicontrol Compilers > \uicontrol Add > \uicontrol Nim to specify
            the path to the the Nim compiler.
        \li Select \uicontrol Apply to add the compiler.
        \li Select \uicontrol Kits > \uicontrol Add to add a kit for building
            applications with Nimble:
            \image qtcreator-kit-nimble.png "Nimble kit"
            \list 1
                \li In the \uicontrol Name field, specify a name for the kit.
                \li In the \uicontrol Compiler group, \uicontrol Nim field,
                    select the Nim compiler you added above.
                \li Select \uicontrol Apply to add the kit.
            \endlist
    \endlist

    \section1 Creating Nimble Applications

    To use a wizard to create boilerplate files for a Nim package that is
    managed with Nimble:

    \list 1
        \li Select \uicontrol File > \uicontrol {New Project} >
            \uicontrol {Non-Qt Project} >
            \uicontrol {Nimble Application}.
        \li Specify the name and location of the application.
        \li Select \uicontrol Next.
        \li Specify information about your application package.
            \image qtcreator-project-nimble.png
        \li Select \uicontrol Next to create the project.
    \endlist
*/
