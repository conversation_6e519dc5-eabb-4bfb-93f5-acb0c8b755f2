// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-materials.html
    \previouspage studio-3d-model.html
    \nextpage studio-3d-texture.html

    \title Materials and Shaders

    \image studio-qtquick-3d-material.webp "Material attached to model in Design mode"

    Materials and shaders define how object surfaces are rendered in \QDS and
    live preview. As you change the properties of materials, new shaders are
    generated accordingly, and the property values are bound. The complexity of
    a shader depends on a combination of the properties that are set on it, and
    the context of the scene itself.

    It is recommended that you use the \l {Material Editor and Browser} when
    working with materials, but you can also add materials using the components
    library.

    The materials that you used in your imported scenes are imported to \QDS
    as \l{Qt Quick 3D} components. When you add a View3D component, it contains
    a DefaultMaterial component. You can use the following predefined Qt Quick
    3D components to add materials to models:

    \list
        \li Default material
        \li Principled material
        \li \l{Custom Effects and Materials}{Custom material}
        \li Texture
    \endlist

    Before a model can be rendered in a scene, it must have at least one
    material to define how the mesh is shaded. The DefaultMaterial component
    is the easiest way to define such a material. The PrincipledMaterial
    component specifies the minimum amount of properties. The CustomMaterial
    component enables you to construct your own materials.

    You can use the \l Texture component to apply textures to materials. It
    defines an image and how the image is mapped to meshes in a 3D scene. For
    more information, see \l {Textures}.

    You can create and modify materials in
    \uicontrol {Material Editor} and \uicontrol {Material Browser}. The availability
    of the properties depends on the material type.

    \image studio-qtquick-3d-default-material.webp "DefaultMaterial properties"

    You can animate material properties in the \uicontrol Timeline view, as
    instructed in \l {Creating Timeline Animations}.
*/
