// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-build-process-customizing.html
    \page creator-testing.html
    \nextpage creator-debugging.html

    \title Testing

    \image front-testing.png

    \list

        \li \l{Debugging}

            If you install \QC as part of \QSDK, the GNU Symbolic Debugger
            is installed automatically and you should be ready to start
            debugging after you create a new project. However, you can
            change the setup to use debugging tools for Windows, for
            example. You can connect \l{glossary-device}{devices} to your
            development host and debug processes running on the devices.

        \li \l{Analyzing Code}

            \QC integrates Valgrind code analysis tools for detecting memory
            leaks and profiling function execution. You must download and
            install them separately to use them from \QC. The QML Profiler is
            installed as part of \QC. It enables you to profile your Qt Quick
            applications.

        \li \l{Running Autotests}

            Create, build and run Qt tests, Qt Quick tests, Google
            tests, and Boost tests using \QC.

        \li \l{Using Squish}

            Map AUTs to \QC and run Squish test suites and cases from it.

    \endlist

*/
