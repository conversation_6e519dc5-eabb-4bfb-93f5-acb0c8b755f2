// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-exporting-qml.html
    \previouspage studio-importing-3d.html
    \nextpage qt-effect-maker-files.html

    \title Exporting Components

    \note In this section, you are using advanced menu items. These are not
    visible by default. To toggle the visibility of advanced menu items, see
    \l{Customizing the Menu}.

    \l{glossary-component}{Components} contained in \l{UI Files}
    {UI files} (.ui.qml) can be exported to JSON metadata format and PNG assets.

    To export the UI files from the current project, select \uicontrol Build >
    \uicontrol {Export Components}.

    The primary use of exported metadata and assets is to generate native file
    formats in content creation tools, such as Adobe Photoshop, using \QB. \QBPS
    can generate PSD files by importing the metadata and assets.

    Components are exported as follows:

    \list
        \li Components (QML types) inherited from \l [QML]{Item}{Item} are
            exported, other components are ignored.
        \li \l [QML]{Text}{Text} components are exported as metadata only
            and no assets are generated.
        \li \l [QML]{Rectangle}{Rectangle} and \l [QML]{Image}{Image}
            components generate assets as PNG files.
    \endlist

    \section1 Configuring QML Export

    You can configure the export in the \uicontrol {Export Components} dialog,
    which lists the UI files (.ui.qml) of the current project.

    \image qtquick-qml-export-dialog.png "Export Components dialog"

    \list 1
        \li In the \uicontrol {Export path} field, specify the path where
            the metadata file and assets are exported.
        \li Deselect the \uicontrol {Export assets} check box to disable
            exporting assets and only generate the metadata file.
        \li Select the \uicontrol {Export components separately} check box to
            generate separate metadata files for each component.
        \li In the file list, select the .ui.qml files to be exported.
        \li Select \uicontrol {Export} export to start the export process.
    \endlist
*/
