The QtCreator 1.3 release contains bug fixes and new features.

Below is a list of relevant changes. You can find a complete list of changes
within the logs of QtCreator's sources. Simply check it out from the public git
repository e.g.,

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --pretty=oneline v1.2.0..master

This release introduces source and binary incompatible changes to the plugin
API, so if you have created your own custom plugins, they will need to be
adapted accordingly.

General
   * Added the option to automatically reload files when externally modified,
     or to ignore external modifications
   * Improved the general resizing behavior for small screen sizes
   * Fixed a crash in the RSS reader that sometimes causes Qt Creator to crash
     on startup
   * Use the default encoding instead of system encoding for the editor when 
     choosing a file through the "Search results" pane
   * Reworked the projects pane
   * Made the welcome screen tabs into plugins, reimplement IWelcomePage 
     to add your own welcome screen page
   * Overhauled the project pane.

Editing
   * Added support for text editor color schemes
   * Added highlighting of uses of the symbol under the cursor
   * Added completion of include directives
   * Added the option to turn off marking of text changes
   * Added automatic matching of brackets and quotes
   * Objective-C: Much improved parsing
   * Objective-C: Added partial semantic checking and symbol navigation
   * Fixed searching in files to take open documents into account
   * Added a Locator filter for symbols in the current document
   * Handle block selection in fakevim
   * Added Copy full path to the editors combobox
   * Added left/right arrow buttons for "Go back/forward" in navigation history
   * Added smart indentation for pasted text blocks

Refactoring
   * Added rename symbol under cursor
   * Find usages of a symbol

Project support
   * Added support for adding and removing files from a generic Makefile-based
     project
   * Added better control over the environment used for running.
   * Add all cmake files to the project tree (only works with a  cmake 2.8)
   * Support cmake with Microsoft Visual Studio Compiler (only works with 
     cmake 2.8 )
   * Fix a few cmake wizard bugs, where canceling left creator in a strange
     state
   * The qmake and make steps can now be removed.
   * The qmake step is a lot smarter and tries harder to not run
   * By default projects using the Microsoft Visual Studio toolchain use jom
     instead of nmake, for better utilization of all processors.
   * Show subdirectory structure below .pro/.pri files in project tree
   * Add "Show file in Finder/Explorer" (Mac/Windows) to context menu.
     On Linux it opens the containing directory.
   * The qmake step and make step can be removed from qt projects now.
   * Made importing build settings for qt projects more robust
   * Only run qmake, if it needs to be run.

Compilation
   * Support multi-core compilation on Windows/MSVC via jom
     (see http://qt.gitorious.org/qt-labs/jom/)

Debugging
   * Detect debugger from project tool chain
   * CDB: Added more types to the dumpers (QSharedPointer, QVector, common
     QMap/QSet types), dereference reference and pointer parameters
   * CDB: Simplified display of STL types in the locals window
   * CDB: Fixed thread handling, display thread position
   * CDB: Added internal dumpers for string types for debuggee crashes
   * CDB: Set symbol paths correctly
   * Improved QObject dumping, print out QRect/QSize, enumerations and flags
   * Made it possible to use the BinEditor plugin for displaying raw memory
   * Replace disassembler window by a real text editor enabling "mixed" output
   * Improved dumper building on options page, run in background

Designer
   * Added support for rearranging and floating form editor tools

Version control plugins
   * Added CVS support
   * Display diff/annotation with correct encoding
   * Added "sync" menu item to the Perforce plugin
   * Fixed locking of temporary submit message files on Windows
   * Use a single, colored output pane for all version control systems
   * Position annotation view of file at current line of editor
   
Wizards
   * Fixed GUI project and form class wizards to use the same settings.
   * Added version control checkout wizards
   * Added a license header template setting
   * Added a wizard for Qt Designer custom widgets
   * Added a gitorious clone wizard

Platform Specific

Mac
   * Make use of system's language settings

Symbian Target
   * Preliminary support for targeting Qt for Symbian applications

Additional credits go to:
   * Christian Hoenig (Locator filter for symbols in current document)
   * Henrik Abelsson (Configure what to do with externally modified files)
   * Kevin Michel (Adding and removing files from a generic project)
