// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-vcs-clearcase.html
    \page creator-vcs-cvs.html
    \nextpage creator-vcs-git.html

    \title Using CVS

    CVS is an open source version control system.

    In addition to the standard version control system functions described in
    \l {Using Common Functions}, you can select \uicontrol Tools >
    \uicontrol CVS > \uicontrol Edit to set a file as writable, notify
    watchers that the file is being edited, and watch for actions performed
     on the file by other users.

    To discard the changes that you made in a file, notify watchers that
    the file is no longer being edited, and set the file as read-only,
    select \uicontrol Unedit.

    To unedit files in the local directory, as well as recursively in all
    subdirectories, select \uicontrol {Unedit Repository}.

    To specify the CVS root directory, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Version Control} > \uicontrol CVS, and then
    specify the path to the directory in the \uicontrol {CVS root} field.

    You can specify settings for viewing diff output in the
    \uicontrol {Diff options} field.

    By default, you are prompted to confirm that you want to submit changes.
    To suppress the prompt, deselect the \uicontrol {Prompt on submit} check
    box.

    By default, all files that belong to the commit are annotated. To disable
    this feature, deselect the \uicontrol {Describe all files matching commit
    id} check box.
*/
