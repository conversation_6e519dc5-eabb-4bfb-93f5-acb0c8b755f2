// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-build-settings-qmake.html
    \page creator-build-settings-qbs.html
    \nextpage creator-build-settings-meson.html

    \title Qbs Build Configuration

    \image qtcreator-build-settings-qbs.png "Qbs build settings"

    Qbs builds projects in the directory specified in the
    \uicontrol {Build Directory} field.

    In the \uicontrol {Tooltip in target selector} field, you can enter text
    that is displayed as a tooltip when you hover the mouse over the build
    configuration in the \l{Building for Multiple Platforms}{kit selector}.

    You can enter a name for the build configuration in the
    \uicontrol {Configuration name} field.

    If debug info is being generated, you can have it placed into separate
    files, rather than embedded into the binary, by selecting
    \uicontrol Enable in the \uicontrol {Separate debug info} field. For
    more information, see \l{Using the Performance Analyzer}. To use default
    settings, select \uicontrol {Leave at Default}.

    For more information about the QML debugging options, see
    \l{Setting Up QML Debugging}.

    For more information about configuring Qbs, see \l{Setting Up Qbs}.

    \section1 Qbs Build Steps

    \image creator-qbs-build-app.png "Qbs build steps"

    To specify build steps for Qbs:

    \list 1

        \li In the \uicontrol {Build variant} field, select \uicontrol Debug to
            include debug symbols in the build for debugging the application and
            \uicontrol Release to create the final installation file.

        \li In the \uicontrol ABIs field, select the ABIs for
            the \l{Connecting Android Devices}{Android} device
            architectures to build the project for.

        \li In the \uicontrol {Parallel jobs} field, specify the number of
            parallel jobs to use for building.

        \li In the \uicontrol Properties field, specify the properties to pass
            to the project. Use colons (:) to separate keys from values.
            For more information, see
            \l{http://doc.qt.io/qbs/language-introduction.html}
            {Modules} in the Qbs Manual.

        \li In the \uicontrol Flags field:

            \list

                \li Select \uicontrol {Keep going} to continue building when
                    errors occur, if possible.

                \li Select \uicontrol {Show command lines} to print actual
                    command lines to \l{Compile Output} instead of
                    high-level descriptions.

                \li Select \uicontrol {Force probes} to force re-execution of
                    the configure scripts of
                    \l{https://doc.qt.io/qbs/qbsprobes-qmlmodule.html}{Probes}.

            \endlist

        \li In the \uicontrol {Installation flags} field:

            \list

                \li Select \uicontrol Install to copy artifacts to their install
                    location after building them. This option is enabled by
                    default.

                    \note On Windows, the build will fail if the application
                    is running because the executable file cannot be
                    overwritten. To avoid this issue, you can deselect this
                    check box and add a \uicontrol {Qbs Install} deployment step
                    in the run settings that will be performed just before
                    running the application.

                \li Select \uicontrol {Clean install root} to remove the
                    contents of the install root directory before the build
                    starts.

                \li Select \uicontrol {Use default location} to install the
                    artifacts to the default location. Deselect the check box to
                    specify another location in the
                    \uicontrol {Installation directory} field.

            \endlist

    \endlist

    The \uicontrol {Equivalent command line} field displays the build command
    that is constructed based on the selected options.

    \section1 Qbs Clean Steps

    When building with Qbs, you can specify flags in \uicontrol {Clean Steps}:

    \image creator-qbs-build-clean.png "Qbs clean steps"

    \list

        \li Select \uicontrol {Dry run} to test cleaning without executing
            commands or making permanent changes to the build graph.

        \li Select \uicontrol {Keep going} to continue cleaning when errors
            occur, if possible.

    \endlist

    The \uicontrol {Equivalent command line} field displays the clean command
    that is constructed based on the selected options.
*/
