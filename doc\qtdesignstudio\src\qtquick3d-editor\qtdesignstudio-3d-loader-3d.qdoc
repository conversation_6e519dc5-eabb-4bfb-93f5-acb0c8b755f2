// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-loader-3d.html
    \previouspage studio-3d-repeater-3d.html
    \nextpage quick-component-instances.html

    \title Loader3D

    \note The \uicontrol Loader3D component is released as a tech preview
    feature in \QDS 2.2, and its functionality will be improved in future
    releases.

    \uicontrol Loader3D is a loader component used to dynamically load 3D
    components. It can load a QML file using the \uicontrol Source property or a
    component using the \uicontrol {Source component} property. \uicontrol Loader3D
    is useful for delaying the creation of a component until it is required, for
    example, when a component should be created on demand or when a component
    should not be created unnecessarily for performance reasons.

    For more information, see the \l{https://doc.qt.io/qt/qml-qtquick3d-loader3d.html}
    {Loader3D QML type} in the \uicontrol {Qt Quick 3D} documentation.

    \section1 Loader3D Properties

    \section2 Active

    The \uicontrol Active property is set to \uicontrol true by default, which
    makes the \uicontrol Loader3D currently active. Setting \uicontrol Active to
    \uicontrol false makes \uicontrol Loader3D inactive. If you change the
    \uicontrol Source or \uicontrol {Source component} of an inactive
    \uicontrol Loader3D, the component will not be instantiated until
    \uicontrol Loader3D is made active. Setting \uicontrol Loader3D inactive
    will also cause any item loaded by the loader to be released, but this will
    not affect the files or components defined as \uicontrol Source or
    \uicontrol {Source component}.

    \section2 Source

    The \uicontrol Source property defines the URL of
    the 3D component to instantiate. To unload the currently loaded object, set
    this property to an empty string or set the \uicontrol {Source component} to
    undefined. Setting \uicontrol Source to a new URL will also cause the item
    created by the previous URL to be unloaded.

    \section2 Source Component

    The \uicontrol {Source Component} property defines the component for
    \uicontrol Loader3D to instantiate. Currently, the
    \uicontrol {Source component} needs to be defined in code using the
    \l {Working in Edit Mode}{Edit} mode or the \l {Code} view.

    \section2 Asynchronous

    The \uicontrol Asynchronous property defines whether the component
    will be instantiated asynchronously. This property is set to \uicontrol false
    by default. When used in conjunction with the source property, loading and
    compilation will be performed in a background thread. Loading asynchronously
    creates the objects declared by the component across multiple frames and
    reduces the likelihood of glitches in animation. Setting the value of
    \uicontrol Asynchronous to \uicontrol false while an asynchronous load is in
    progress will force immediate synchronous completion. This allows an
    asynchronous loading to begin and then forces completion if the
    \uicontrol Loader3D content must be accessed before the asynchronous
    loading is completed.

    \section1 Setting the Loader3D to Load a QML File

    To add a Loader3D component and set it to load a QML file:
    \list 1
      \li From \uicontrol {Components}, drag a Loader3D component to
      \e scene in the \uicontrol Navigator or \uicontrol{3D} view.
      \li In \uicontrol {Navigator}, select \e{loader3D}.
      \image loader3d-navigator.png
      \li In \uicontrol {Properties}, select \uicontrol{Source} and select a
      QML file.
      \image loader3d-select-source.png
    \endlist

    \section1 Setting the Loader3D  to Load a Component3D Component

    To add a Loader3D component and set it to load a Component3D component:
    \list 1
      \li From \uicontrol {Components}, drag a Loader3D Component to
      \e scene in the \uicontrol Navigator or \uicontrol{3D} view.
      \li From \uicontrol {Components}, drag a Component3D component to
      \e scene in \uicontrol {Navigator}.
      \li In \uicontrol {Navigator}, select the filter icon and clear
      \uicontrol {Show Only Visible Components}. This makes the Component3D
      component visible in \uicontrol Navigator.
      \image navigator-show-all-loader.png
      \li In \uicontrol {Navigator}, select \e{loader3D}.
      \li In \uicontrol {Properties}, select \uicontrol{Source Component}
      and select \e {component3D}.

      \image loader3d-select-source-component.png
    \endlist

    \section1 Setting the Visibility of Loading Components

    To avoid seeing the components loading progressively, set the \uicontrol
    Visibility property for \uicontrol Loader3D appropriately in
    \uicontrol {Binding Editor}:
        \list 1
            \li In \uicontrol Properties > \uicontrol Loader3D, select the
                \uicontrol Asynchronous check box.
                \image loader3d-visibility.png
            \li In \uicontrol {Visibility},
                select \inlineimage icons/action-icon.png
                to open the actions menu, and then select \uicontrol {Set Binding}.
            \li Type \c {status === Loader3D.Ready} into \uicontrol {Binding Editor}.
                \image studio-3d-loader3d-binding-editor.png "Setting Visibility in Binding Editor"
            \li Select \uicontrol OK.
        \endlist

*/
