// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \example Loginui1
    \ingroup gstutorials
    \nextpage {Log In UI - Positioning}

    \title Log In UI - Components
    \brief Illustrates how to use wizard templates to create a simple UI
    that contains a text label, images, and push buttons.

    \image loginui1.jpg

    \e{Log In UI - Components} is the first tutorial in a series of tutorials
    that describes how to use the \QDS wizard templates to create a project and
    a button UI control, and how to modify the files generated by the wizard
    templates to design the UI.

    You can donwnload the completed project from
    \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/tutorial%20projects/Loginui1}{here}.

    \section1 Creating the UI Project

    For the purposes of this tutorial, you will use the empty wizard template.
    Wizard templates are available also for creating UIs that are optimized for
    mobile platforms and for launcher applications. For more information about
    the options you have, see \l {Creating Projects}.

    To create a project:

    \list 1
        \li Select \uicontrol File > \uicontrol {New Project}.
        \li In the \uicontrol Presets tab, select \uicontrol General >
        \uicontrol {Empty}.
        \li In the \uicontrol Details tab:
            \list
                \li Enter \e Loginui1 as the name for the project. Keep in mind
                that projects cannot be easily renamed later.
                \li Select the path for the project files. You can move project
                folders later.
                \li Set \uicontrol Width to 720 and \uicontrol Height to 1280.
                You can change the screen size later in \l Properties.
            \endlist

        \li Select \uicontrol Create to create the project.
    \endlist

    Your project should now look something like this in the \uicontrol Design
    mode:

    \image loginui1-project.png "Log In UI project in the Design mode"

    The wizard constructs the \e Screen01 \l{glossary-component}{component}
    using instances of a \l{basic-rectangle}{Rectangle} component that forms
    the background, a \l Button, and a \l Text component that displays some text.

    \note The visibility of views depends on the selected workspace,
    so your \QDS might look somewhat different from the above image.
    To open hidden views, select \uicontrol View > \uicontrol Views
    in the \uicontrol Design mode. For more information about moving
    views around, see \l {Managing Workspaces}.

    You should remove this \l Button for now from the UI
    to have a clean workspace. You'll add this later in the course of the tutorial.
    Then you shall know how to modify and adjust it as you need.

    To remove this Button, just select it and press \key {Backspace}.

    Next, you will edit the values of the properties of the component instances
    to create the main page of the UI.
    \section1 Creating the Main Page

    You will now change the values of the properties of the \l Text component
    instance to create a tag line using a custom font. You will add the font
    as an asset and set it to be used in the properties of the component.
    In addition, you will import a background image and logo as assets and
    add them to the page as instances of the \l{Images}{Image} component.

    You can download the logo and the background image from here:

    \list
        \li \l{https://git.qt.io/public-demos/qtdesign-studio/-/blob/master/tutorial%20projects/Loginui1/content/images/qt_logo_green_128x128px.png}
            {qt_logo_green_128x128px.png}
        \li \l{https://git.qt.io/public-demos/qtdesign-studio/-/blob/master/tutorial%20projects/Loginui1/content/images/adventurePage.jpg}
            {Background image} (\e adventurePage.jpg)

            Photo by \l{https://unsplash.com/photos/a2MgJdG6UvE}
            {Benjamin DeYoung} on \l{https://unsplash.com}{Unsplash}.
    \endlist

    You can download the font from
    \l{https://fonts.google.com/specimen/Titillium+Web#standard-styles}
    {Titillium Web ExtraLight font} or use Arial as a substitute.

    To add the assets:
    \list 1
        \li Select \uicontrol Assets > \inlineimage icons/plus.png
        (Select \uicontrol View > \uicontrol Views > \uicontrol Assets to enable it,
            if you can't find it).
        \li Select the asset files, and then select \uicontrol Open.
        \li Select the location where the files will be saved in the
            \uicontrol {Add Resources} dialog.
        \li Select \uicontrol OK.
    \endlist

    To preview the changes that you make to the UI while you make
    them, select the \inlineimage icons/live_preview.png
    (\uicontrol {Show Live Preview}) button on the \uicontrol {2D}
    view toolbar or press \key {Alt+P}.

    The \e Screen01.ui.qml file that the wizard template created for you should
    be open in the \uicontrol Design mode. If it is not, you can double-click it
    in the \uicontrol Projects view to open it.

    To modify the \e Screen01 component in the \uicontrol {2D} view:

    \list 1
        \li Drag-and-drop the background image (1) from \uicontrol Assets to the
            \l{basic-rectangle}{Rectangle} (2) in \l Navigator.
            \image loginui1-library-assets.jpg "Assets view"
        \li \QDS automatically creates an instance of the \l{Images}{Image}
            component for you with the path to the image file set as the
            value of \l Properties > \uicontrol Image > \uicontrol Source.
            \image loginui1-image-properties.png "Image properties"
        \li Drag-and-drop the Qt logo from \uicontrol Assets to the rectangle
            in \uicontrol Navigator and move it to the top-center of the
            background image in the \uicontrol {2D} view.
        \li Select \e Text in \uicontrol Navigator and drag it below the logo
            in the \uicontrol {2D} view. If the text is hidden behind the
            background, select \inlineimage icons/navigator-arrowdown.png
            in \uicontrol Navigator to move the text as the last item in the
            component tree structure. The text should then appear on top
            of the background.
        \li Edit text properties in \uicontrol Properties:
            \list a
                \li In \uicontrol Component > \uicontrol ID, enter the ID
                    \e tagLine.
                \li In \uicontrol Character > \uicontrol Text, enter the tag
                    line: \e {Are you ready to explore?}.
                    \image loginui1-text-properties.png "Text properties"
                \li In \uicontrol Font, select \e {Titillium Web ExtraLight}.
                \li In \uicontrol Size, first select the scale to pixels  (\uicontrol px),
                then set font size of the tag line to \e 50 (\uicontrol px).
                \li In \uicontrol {Text color}, set the text color to white
                    (\e #ffffff).
            \endlist
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    Your UI should now look something like this in the \uicontrol {2D} view
    and live preview:

    \image loginui1-main-page.jpg "Modified UI in the Design mode"

    You can resize the preview dialog to display the whole screen.
    \section1 Creating a Push Button

    You can use another wizard template to create a push button and to add it to
    the project. The wizard template creates a reusable button component that
    appears in \uicontrol Components > \uicontrol {My Components}.
    You can drag-and-drop it to \l Navigator to create button instances and
    modify their properties in the \uicontrol Properties view to change their
    appearance and functionality.

    If you cannot use the wizard template nor the preset
    button controls available in \uicontrol Components >
    \uicontrol {Qt Quick Controls} to create the kind of push button that you
    want, you can create your button from scratch using default components. For
    more information, see \l {Creating Buttons} and
    \l {Creating Scalable Buttons and Borders}.

    To create a push button by using the wizard template:

    \list 1
        \li Select \uicontrol File > \uicontrol {New File} >
            \uicontrol {Qt Quick Controls} >
            \uicontrol {Custom Button} > \uicontrol Choose.
        \li In the \uicontrol {Component name} field, enter a name for your
            button component: \e {EntryField}.
        \li Select \uicontrol Finish (or \uicontrol Done on \macos) to create
            the button UI file, \e EntryField.ui.qml.
    \endlist

    Your button should now look something like this in the \uicontrol Design
    mode:

    \image loginui1-button.png "Button in the Design mode."

    \note To open the \uicontrol States view, select it from
    \uicontrol View > \uicontrol Views > \uicontrol States, if
    it is not available by default.

    Next, you will change the appearance of the EntryField component by
    modifying its properties.

    \section1 Styling the Button

    You can now modify the properties of the EntryField component to your
    liking. To make the changes apply to all the EntryField instances, you
    must make them in the \e EntryField.ui.qml file.

    The Custom Button wizard template adds a \e normal state and a \e down state
    to change the button background and text color when the button is clicked.
    You will now change the colors in all states. When you make changes to the
    button in the \e base state, they are automatically applied to the other
    states. However, the property values that have been explicitly changed in
    the \e normal or \e down state are not changed automatically and you have
    to either reset them to the base state or set them separately in that state.

    To change the button property values:

    \list 1
        \li Select \e control in \uicontrol Navigator to display its
            properties in \uicontrol Properties.
        \li In \uicontrol {Geometry - 2D} > \uicontrol Size, set button
            width (\uicontrol W) to \e 500 and height (\uicontrol H)
            to \e 100, to match the width of the tag line.
        \li In the \uicontrol Control section, deselect the \uicontrol Hover
            check box because we don't want the hover effect for the button.
        \li Select \e buttonBackground in \uicontrol Navigator to display its
            properties in \uicontrol Properties.
        \li In \uicontrol Rectangle > \uicontrol {Fill color}, set the color to
            transparent light gray (\e #28e7e7e7) in \uicontrol Hex. You can
            also use the \l{Picking Colors}{color picker} to set the color.
        \li In \uicontrol {Border Color}, select white (\e #ffffff).
        \li In \uicontrol Radius, enter \e 50 to give the button
            rounded corners.
        \li Select \e textItem in \uicontrol Navigator to display its
            properties in \uicontrol Properties.
        \li In \uicontrol Character > \uicontrol Font, select
            \e {Titillium Web ExtraLight}.
        \li In \uicontrol Size, first select the scale to pixels  (\uicontrol px),
                then set font size to \e 34 (\uicontrol px).
        \li In \uicontrol {Text color}, set the text color to white
            (\e #ffffff).
        \li In \uicontrol {Alignment H}, select the \uicontrol Left button to
            align the text horizontally to the left.
        \li In \uicontrol Padding > \uicontrol Horizontal >
            \uicontrol Left, set the padding in the field between background
            border and text to \e 50.
            \image loginui1-text-properties-button.png "Text properties"
        \li In the \uicontrol States view, select the \e normal state and
            repeat the changes in the background color and text properties,
            as necessary. Repeat for the \e down state.
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    Your button should now look something like this:

    \image loginui1-entry-field-styled.jpg "Modified button in the 2D view"

    \note Do not edit the the value of \uicontrol Text in the \uicontrol Character
    property, because this will break the connection, and later you won't be able
    to change the text in \uicontrol {Button Content} > \uicontrol Text.

    Next, you will add instances of the \e EntryField component to the
    \e Screen01 component and modify their properties.

    \section1 Adding Entry Fields to the UI

    You will now add EntryField instances to the UI and modify their properties.

    \list 1
        \li Double-click \e Screen01.ui.qml in \uicontrol Projects
            to open it in the \uicontrol {2D} view.
        \li Drag-and-drop two instances of the EntryField component from
            \uicontrol Components > \uicontrol {My Components} to the rectangle
            in \uicontrol Navigator.
            \image loginui1-library.jpg "My Components tab of Components view"
        \li Select the first EntryField instance in \uicontrol Navigator
            to modify its ID and text in \uicontrol Properties.
        \li In \uicontrol Component > \uicontrol ID, enter \e username.
        \li In \uicontrol {Button Content} > \uicontrol Text, enter
            \e {Username or Email} and select \uicontrol tr to mark the text
            \l {Internationalization and Localization with Qt Quick}
            {translatable}.
        \li Select the second EntryField instance, and change its ID to
            \e password and text to \e Password. Again, mark the text
            translatable.
        \li Move the cursor on the selected button instance to make the
            selection icon appear. You can now drag the button instance
            to another position in the \uicontrol {2D} view. Use the
            guidelines to align the button instances below the tag line:
            \image loginui1-align-buttons.jpg
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    \section1 Creating Another Button

    We want to center-align the text of two additional push buttons and use
    brighter colors for them, so we create a second button component as
    instructed in \l{Creating a Push Button}. This time we call it
    \e PushButton.

    To make the changes apply to all the PushButton instances, you
    must make them in the \e PushButton.ui.qml file.

    To change the button property values:

    \list 1
        \li Select \e control in \uicontrol Navigator to display its
            properties in \uicontrol Properties.
        \li In \uicontrol {Geometry - 2D} > \uicontrol Size, set button
            width (\uicontrol W) to \e 500 and height (\uicontrol H)
            to \e 100.
        \li In the \uicontrol Control section, deselect the \uicontrol Hover
            check box because we don't want the hover effect for the button.
        \li Select \e buttonBackground in \uicontrol Navigator to display its
            properties in \uicontrol Properties.
        \li In \uicontrol Rectangle > \uicontrol {Border color}, select the
            green used in the logo (\e #41cd52).
        \li In \uicontrol Radius, enter \e 50 to give the button rounded
            corners.
        \li Select \e textItem in \uicontrol Navigator to display
            its properties in \uicontrol Properties.
        \li In \uicontrol Character > \uicontrol Font, select
            \e {Titillium Web ExtraLight}.
        \li In \uicontrol Size, first select the scale to pixels  (\uicontrol px),
                then set font size to \e 34 (\uicontrol px).
        \li In \uicontrol {Text color}, set the text color to \e #41cd52.
        \li In the \uicontrol States view, select the \e normal state and repeat
            the changes, as necessary.
        \li Repeat for the \e down state. However, in \uicontrol Rectangle >
            \uicontrol {Fill color}, set the color to green (\e #41cd52) to
            turn the button background green when the button is pressed down.
            Also, in \uicontrol Text > \uicontrol Character >
            \uicontrol {Text color}, keep the text color as white (\e #ffffff).
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    \section1 Adding Push Buttons to the UI

    You will now add PushButton instances to the UI and modify their properties.

    \list 1
        \li Double-click \e Screen01.ui.qml in \uicontrol Projects
            to open it in the \uicontrol {2D} view.
        \li Drag-and-drop two instances of the PushButton component from
            \uicontrol Components > \uicontrol {My Components} to the rectangle
            in \uicontrol Navigator.
        \li Drag the button instances to the bottom of the background image in
            the \uicontrol {2D} view. Use the guidelines to align the button
            instances horizontally with the entry fields.
        \li Select the first PushButton instance in \uicontrol Navigator
            to modify its ID and text label in \uicontrol Properties.
        \li In \uicontrol Component > \uicontrol ID, enter \e login.
        \li In \uicontrol {Button Content} > \uicontrol Text, enter
            \e Continue and select \uicontrol tr to mark the text
            translatable.
        \li Select the second PushButton instance, and change its ID to
            \e createAccount and text label to \e {Create Account}. Again,
            mark the text translatable.
        \li Select \uicontrol File > \uicontrol Save or press \key {Ctrl+S}
            to save your changes.
    \endlist

    The first iteration of your UI is now ready and should now look something
    like this in the \uicontrol {2D} view and live preview:

    \image loginui1-ready.jpg "The finished UI in the 2D view"

    \section1 Learn More
    The \e {Learn More} sections provide additional information about the
    tasks performed by the wizards and about other basic tasks and concepts.

    \section2 Projects and Files
        \QDS creates a set of files and folders that you need to create
        a UI. The files are listed in the \l{File System} view.

        \image loginui1-project-files.png
        \list
            \li The \e {loginui1.qmlproject} project file defines that all
                component, JavaScript, and image files in the project folder belong
                to the project. Therefore, you do not need to individually list new
                files when you add them to the project.
            \li The \e {loginui1.qml} file defines the functionality of
                the UI. For the time being, it does not do anything.
            \li The \e {Screen01.ui.qml} file is a custom component created by
                the wizard template. For more information, see \l {UI Files}.

                By default, this is the main file in the project, but you can
                change that in the .qmlproject file. While the custom component
                is a good starting point for new users, you don't have to use it.
                Specifically, if you export and import designs using \QB, your main
                file is most likely called something else. For more information,
                see \l {Exporting from Design Tools}.
            \li The \e CMakeLists.txt project configuration file allowing you to
                share your project as a fully working C++ application with
                developers.
            \li The \e {qtquickcontrols2.conf} file specifies the selected
                \l {Styling Qt Quick Controls}{UI style} and some style-specific
                arguments.
            \li The \e imports folder contains \e {Constants.qml} and
                \e {DirectoryFontLoader.qml} files that specify a font loader
                and a \e qmldir module definition file that declares the Constant
                component. For more information, see
                \l {Module Definition qmldir Files}. The \e EventListModel.qml and
                \e EventListSimulator.qml files are not used in this example, so
                you can ignore them for now.
        \endlist
        \l{UI Files}{UI files} define a hierarchy of components with a
        highly-readable, structured layout. Every UI file consists of two parts:
        an imports section and an component declaration section. The components and
        functionality most common to UIs are provided in the \c QtQuick import. You
        can view the code of a \e .ui.qml file in the \l{Code} view.


    \section2 Components

        \QDS provides preset \l{glossary-component}{components} for creating
        UIs, including components for creating and animating visual components,
        receiving user input, and creating data models and views.

        To be able to use the functionality of preset components, the wizard template
        adds the following \e import statements to the UI files (.ui.qml) that it
        creates:

        \quotefromfile Loginui1/content/Screen01.ui.qml
        \skipto import
        \printuntil Controls

        You can view the import statements in the \uicontrol {Code} view.

        The \l Components view lists the components in each module that are
        supported by \QDS. You can use the basic components to create your own
        components, and they will be listed in \uicontrol {My Components}.
        This section is only visible if you have created custom components.

        The \l {basic-rectangle}{Rectangle}, \l Text, and \l {Images}{Image}
        components used in this tutorial are based on the \l Item component.
        It is the base component for all visual elements, with implementation
        of basic functions and properties, such as component type, ID, position,
        size, and visibility.

        For more information, see \l{Use Case - Visual Elements In QML}. For
        descriptions of all components, see \l{All QML Types} in the Qt reference
        documentation.

    \section3 Regtangle Properties

        The default \l {basic-rectangle}{Rectangle} component is used for drawing
        shapes with four sides and four corners. You can fill rectangles either with
        a solid fill color or a gradient. You can specify the border color separately.
        By setting the value of the radius property, you can create shapes with
        rounded corners.

        If you want to specify the radius of each corner separately, you can use the
        \l{studio-rectangle}{Rectangle} component from the
        \uicontrol {Qt Quick Studio Components} module instead of the basic rectangle
        component. It is available in \uicontrol Components
        > \uicontrol {Qt Quick Studio Components}.

    \section3 Text Properties

        The \l Text component is used for adding static text to the UI, such as
        titles and labels. You can select the font to use and specify extensive
        properties for each text component, such as size in points or pixels,
        weight, style, and spacing.

        If you want to create a label with a background, use the \l Label component
        from the \uicontrol {Qt Quick Controls} module instead of the Text component.

    \section3 Image Properties

        The \l {Images}{Image} component is used for adding images to the UI in several
        supported formats, including bitmap formats such as PNG and JPEG and vector
        graphics formats such as SVG. To add an image to \uicontrol Assets, select
        \inlineimage icons/plus.png
        , and then select the image file.

        If you need to display animated images, use the \l {Animated Image}
        component, also available in \uicontrol Components >
        \uicontrol {Default Components} > \uicontrol Basic.

    \section2 UI Controls

        The \e {Custom Button} wizard template creates a button component
        based on the \l {Button} control in the \l {Qt Quick Controls} module. It
        is a push-button control that can be pushed or clicked by the user. Buttons
        are normally used to perform an action or to answer a question. The
        properties and functionality inherited from the Button component enable
        you to set text, display an icon, react to mouse clicks, and so on.

        To be able to use the functionality of the Button control, the wizard template
        adds the following \e import statements to the \e EntryField.ui.qml file:

        \quotefromfile Loginui1/content/EntryField.ui.qml
        \skipto import
        \printuntil Controls

    \section2 Component IDs

    Each component and each instance of a component has an \e ID that uniquely
    identifies it and enables other components' properties to be bound to it.
    An ID must be unique, it must begin with a lower-case letter or an
    underscore character, and it can contain only letters, numbers, and
    underscore characters.

    For more information, see \l{The id Attribute}.

    \section1 Next Steps

    To learn how to add more UI controls and position them on the page using
    anchors and layouts so that the UI is scalable, see the next tutorial in
    the series, \l {Log In UI - Positioning}.

    For a more advanced example of creating a menu button and using it to
    construct a button bar, see \l {Side Menu}.
*/
