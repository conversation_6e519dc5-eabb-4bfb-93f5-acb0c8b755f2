// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-build-dependencies.html
    \page creator-project-settings-environment.html
    \nextpage creator-custom-output-parsers.html

    \title Specifying Environment Settings

    You can specify the environment you want to use for building
    a project in the \uicontrol {Build Environment} section of
    the \uicontrol {Build Settings}.

    By default, the environment in which \QC was started is used and modified
    to include the Qt version. Depending on the selected Qt version, \QC
    automatically sets the necessary environment variables. You can edit
    existing environment variables or add, reset and unset new variables
    based on your project requirements.

    To globally change the system environment from the one in which
    \QC is started, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Environment > \uicontrol System, and then select
    \uicontrol Change in the \uicontrol Environment field.

    \image qtcreator-options-environment-system.png "Environment preferences System tab"

    In addition, you can specify custom environment variables in the
    \uicontrol {Project Settings} > \uicontrol Environment settings.
    They are added to all build environments. The final build environment
    is specified separately for each kit. The project-specific environment
    settings enable you to amend the build environment for all kits that
    you use to build the project simultaneously, rather than having to edit
    it separately for each kit.

    \image qtcreator-build-environment.png "Build Environment"

    The changes are stored in the local project specific \c{CMakeLists.txt.user}
    or \c{.pro.user} file, depending on the build system you use. Therefore,
    they are not suitable for sharing between developers or development PCs. To
    share settings, incorporate them into the build system. For example, if you
    use CMake, make the changes in the \c {CMakeLists.txt} file, and if you use
    qmake, make the changes in the \c{.pro} file.

    \section1 Batch Editing

    To modify environment variable values for build or run environments,
    select \uicontrol {Batch Edit} in the \uicontrol {Build Environment}
    or \uicontrol {Environment} pane and enter environment variables in
    the \uicontrol {Edit Environment} dialog.

    To remove a variable value from the environment, enter the variable name.
    For example, \c TEST sets the value of the \c TEST variable empty when
    building or running the project.

    To add a variable value to the environment, enter the variable name and
    value, separated by the equals sign. For example, the following line
    prepends the \c /opt/bin folder to the existing PATH:

    \list
        \li On Windows: \c {PATH=C:\opt\bin;${PATH}}
        \li On Linux: \c {PATH=/opt/bin:${PATH}}
    \endlist

    To add or remove several variables, place them on separate lines. The order
    is important. If you remove a value on a line, you cannot refer to it on the
    following lines. However, you can remove a value after you have referred to
    it on an earlier line.

    To temporarily disable a variable, add a hash character (#) to the beginning
    of the line.

    \section1 Clearing the System Environment

    To build with a clean system environment, select the \uicontrol {Clear
    system environment} check box. \QC discards the current environment, and
    populates a clean system environment with the environment variables that
    the compilers and tools need. Therefore, the environment is never totally
    empty, even after you clear it.

    \section1 Using Environment Variables

    You can use any environment variables in build, deploy, and run
    configurations. For a list of variable names, select \uicontrol {Build
    Settings} > \uicontrol {Build Environment} > \uicontrol Details.
    Environment variables are referenced using the native syntax: $VARNAME
    or ${VARNAME} on Unix and %VARNAME% on Windows.

    \include qtcreator-variables.qdocinc qtcreator variables
*/
