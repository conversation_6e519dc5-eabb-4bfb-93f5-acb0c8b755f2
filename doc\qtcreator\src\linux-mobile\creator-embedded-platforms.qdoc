// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-embedded-platforms.html
    \previouspage creator-desktop-platforms.html
    \nextpage creator-mobile-platforms.html

    \title Embedded Platforms

    You can develop applications for the following embedded platforms:

    \list
        \li \l {Bare Metal}
        \li \l {Boot2Qt}
        \li \l {Remote Linux}
        \li \l {Microcontroller Units (MCU)}
        \li \l QNX
    \endlist

    You must install the tool chain for building applications for the targeted
    embedded platform on the development PC and use the Qt Maintenance Tool to
    install Qt libraries that are built for the platform. You can then add a
    \l{glossary-buildandrun-kit}{kit} with the tool chain and the Qt version
    for the device's architecture. When possible, the Maintenance Tool creates
    suitable kits for you.

    You can connect embedded devices to the development PC to run, debug, and
    analyze applications built for them from \QC.

    \section1 Bare Metal

    You can run and debug applications on small devices that are not supported
    by the remote Linux device plugin by using GDB or a hardware
    debugger.

    For more information about developing applications for Bare Metal devices,
    see \l{Connecting Bare Metal Devices}.

    \section1 Boot2Qt

    The Boot2Qt stack can be made to run on a variety of hardware. For
    license holders, tooling is provided to customize the contents of the stack
    as well as to take it into desired production hardware.

    Either Windows 10 64-bit or later or Ubuntu Linux 64-bit 20.04 LTS
    or later is required to install and use Boot2Qt.

    The following topics contain more information about developing applications
    for Boot2Qt devices:

    \list
        \li \l{https://doc.qt.io/Boot2Qt/qtdc-supported-platforms.html}
            {Boot2Qt: Supported Target Devices and Development Hosts}
        \li \l{https://doc.qt.io/Boot2Qt/b2qt-installation-guides.html}
            {Boot2Qt: Installation Guides}
        \li \l{Connecting Boot2Qt Devices}
        \li \l{Specifying Run Settings for Boot2Qt Devices}
        \li \l{Deploying to Boot2Qt}
        \li \l{https://doc.qt.io/qtcreator/creator-overview-qtasam.html}
            {Qt Creator Plugin for Qt Application Manager}
    \endlist

    \section1 Remote Linux

    You must have a tool chain for building applications for embedded Linux
    devices installed on the development PC.

    The following topics contain more information about developing applications
    for remote Linux devices:

    \list
        \li \l{Connecting Remote Linux Devices}
        \li \l{Deploying to Remote Linux}
        \li \l{Specifying Run Settings for Linux-Based Devices}
        \li \l{Running on Remote Linux Devices}
        \li \l{https://doc.qt.io/qtcreator/creator-overview-qtasam.html}
            {Qt Creator Plugin for Qt Application Manager}
    \endlist

    \section1 Microcontroller Units (MCU)

    You need the GNU Arm Embedded GCC compiler, libraries, and other GNU tools
    necessary for bare metal software development on devices based on the Arm
    Cortex-M processors.

    The following topics contain more information about developing applications
    for MCUs:

    \list
        \li \l{Connecting MCUs}
        \li \l{Running Applications on MCUs}
        \li \l{https://doc.qt.io/QtForMCUs/index.html}{Qt for MCUs}
    \endlist

    \section1 QNX

    The QNX Neutrino RTOS should provide a few additional command line tools
    and services, as described in \l {Qt for QNX}.

    \note In Qt 6, \QC support for QNX is considered experimental.

    The following topics contain more information about developing applications
    for QNX devices:

    \list
        \li \l{Connecting QNX Devices}
        \li \l{Deploying to QNX Neutrino}
        \li \l{Specifying Run Settings for QNX Devices}
        \li \l{Running on QNX Devices}
        \li \l{Qt for QNX}
    \endlist
*/
