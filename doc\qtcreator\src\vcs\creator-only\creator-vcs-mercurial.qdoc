// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-vcs-gitlab.html
    \page creator-vcs-mercurial.html
    \nextpage creator-vcs-perforce.html

    \title Using Mercurial

    Mercurial is a free, distributed source control management tool.

    In addition to the standard version control system functions described in
    \l {Using Common Functions}, you can select the following functions in the
    \uicontrol Tools > \uicontrol Mercurial submenu:

    \table
        \header
            \li Menu Item
            \li Description
        \row
            \li \uicontrol{Import}
            \li Apply changes from a patch file.
        \row
            \li \uicontrol{Incoming}
            \li Monitor the status of a remote repository by listing
                the changes that will be pulled.
        \row
            \li \uicontrol{Outgoing}
            \li Monitor the status of a remote repository by listing
                the changes that will be pushed.
        \row
            \li \uicontrol{Pull}
            \li Pull changes from the remote repository.
        \row
            \li \uicontrol{Push}
            \li Push changes to the remote repository.
     \endtable
*/
