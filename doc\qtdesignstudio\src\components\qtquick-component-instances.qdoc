// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-component-instances.html
    \previouspage studio-3d-loader-3d.html
    \nextpage quick-components-creating.html

    \title Creating Component Instances

    \QDS comes with \e {preset components} that you can use in your UI by
    creating instances of them.

    \image qmldesigner-editing-components.png "Creating Component Instances"

    To create component instances and edit their properties:

    \list 1
        \li Drag-and-drop components from \uicontrol Components (1) to the
            \l Navigator (2), \l {2D} (3), or \l {3D} view (4).
            This creates instances of the components in the current
            component file.
        \li Select component instances in \uicontrol Navigator to edit the
            values of their properties in \uicontrol Properties.
            \image qmldesigner-properties-view.png "Properties view"
            For more information, see \l {Specifying Component Properties}.
        \li To change the appearance and behavior of the component instances
            in ways that are not supported in \QDS by default, you can define
            custom properties on the \uicontrol {Properties} tab in the
            \l {Connections} view.
            \image qmldesigner-dynamicprops.png "Connections View Properties tab"
            For more information, see \l{Specifying Custom Properties}.
        \li To enable users to interact with the component instances, connect
            the instances to signals on the \uicontrol Connections tab in the
            \uicontrol {Connections} view. For example, you can specify what
            happens when a component instance is clicked. For more information,
            see \l{Connecting Components to Signals}.
            \image qmldesigner-connections.png "Connections View Connections tab"
        \li To dynamically change the behavior of a component instance when
            another component instance changes, create bindings between them on
            the \uicontrol Bindings tab in the \uicontrol {Connections} view.
            For more information, see \l{Adding Bindings Between Properties}.
            \image qmldesigner-bindings.png "Connections view Bindings tab"
        \li Add states to apply sets of changes to the property values of one
            or several component instances in the \uicontrol States view.
            For more information, see \l{Working with States}.
        \li Animate the properties of component instances in the
            \uicontrol Timeline view. For more information, see
            \l{Creating Timeline Animations}.
    \endlist
*/
