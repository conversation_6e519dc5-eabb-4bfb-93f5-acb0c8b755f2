// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// Note: The \page value is hard-coded as a link in Qt Bridge for Adobe XD.

/*!
    \previouspage qtbridge-ps-using.html
    \page xdqtbridge.html
    \nextpage qtbridge-xd-setup.html

    \title Exporting Designs from Adobe XD

    \note This is a \e {Technical Preview} release of the \QBXD. Some design
    elements might not be exported and imported into \QDS as expected.

    You can use \QBXD to export designs from Adobe XD to \e {.qtbridge}
    format that you can \l{Importing 2D Assets}{import} to projects in \QDS.

    \image qt-bridge-xd.png

    The following topics describe setting up and using \QBXD:

    \list

        \li \l{Setting Up Qt Bridge for Adobe XD}

            You must install and set up the \QBXD export plugin before you can use
            it to export designs.

        \li \l{Using Qt Bridge for Adobe XD}

            To get the best results when you use \QB to export designs from
            Adobe XD, you should follow the guidelines for working with
            Adobe XD and organizing your assets.
    \endlist
*/
