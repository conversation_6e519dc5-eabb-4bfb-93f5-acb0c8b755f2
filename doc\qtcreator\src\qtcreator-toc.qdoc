// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only


/*!
    \page qtcreator-toc.html

    \title All Topics

    \list
        \li \l{Getting Started}
        \list
            \li \l{IDE Overview}
            \li \l{User Interface}
                \list
                    \li \l{Selecting Modes}
                    \li \l{Working with Sidebars}
                    \li \l{Browsing Project Contents}
                        \list
                            \li \l{Projects}
                            \li \l{File System}
                            \li \l{Open Documents}
                        \endlist
                    \li \l{Viewing Output}
                 \endlist
            \li \l{Configuring Qt Creator}
            \li \l{Building and Running an Example}
            \li \l{Tutorials}
            \list
                \li \l{Creating a Qt Quick Application}
                \li \l{Creating a Qt Widget Based Application}
                \li \l{Creating a Mobile Application}
            \endlist
        \endlist
        \li \l{Managing Projects}
        \list
            \li \l{Creating Projects}
            \list
                \li \l{Creating Files}
                \li \l{Opening Projects}
                \li \l{Adding Libraries to Projects}
                \li \l{Adding New Custom Wizards}
            \endlist
            \li \l{Using Version Control Systems}
            \list
                \li \l{Using Bazaar}
                \li \l{Using ClearCase}
                \li \l{Using CVS}
                \li \l{Using Git}
                \li \l{Using GitLab}
                \li \l{Using Mercurial}
                \li \l{Using Perforce}
                \li \l{Using Subversion}
            \endlist
            \li \l{Configuring Projects}
            \list
                \li \l{Adding Kits}
                \li \l{Adding Qt Versions}
                \li \l{Adding Compilers}
                \li \l{Adding Debuggers}
                \li \l{Specifying Build Settings}
                    \list
                        \li \l{Cmake Build Configuration}
                        \li \l{qmake Build Configuration}
                        \li \l{Qbs Build Configuration}
                        \li \l{Meson Build Configuration}
                        \li \l{IncrediBuild Build Configuration}
                        \li \l{Conan Build Configuration}
                    \endlist
                \li \l{Specifying Run Settings}
                \li \l{Specifying Editor Settings}
                \li \l{Specifying Code Style Settings}
                \li \l{Specifying Dependencies}
                \li \l{Specifying Environment Settings}
                \li \l{Using Custom Output Parsers}
                \li \l{Sharing Project Settings}
            \endlist
            \li \l{Managing Sessions}
        \endlist
        \li \l{Designing User Interfaces}
        \list
            \li \l{Developing Qt Quick Applications}
            \list
                \li \l {Creating Qt Quick Projects}
                \li \l {Using \QMLD}
                \li \l {Converting UI Projects to Applications}
                \li \l {UI Files}
                \li \l {Using QML Modules with Plugins}
            \endlist
            \li \l{Developing Widget Based Applications}
            \list
                \li  \l{Adding Qt Designer Plugins}
            \endlist
            \li \l{Optimizing Applications for Mobile Devices}
        \endlist
        \li \l{Coding}
        \list
        \li \l{Writing Code}
            \list
                \li \l{Working in Edit Mode}
                \li \l{Semantic Highlighting}
                \li \l{Checking Code Syntax}
                \li \l{Completing Code}
                \li \l{Indenting Text or Code}
                \li \l{Using Qt Quick Toolbars}
                \li \l{Pasting and Fetching Code Snippets}
                \li \l{Using Text Editing Macros}
                \li \l{Comparing Files}
                \li \l{Parsing C++ Files with the Clang Code Model}
            \endlist
            \li \l{Finding}
            \list
                \li \l{Finding and Replacing}
                \li \l{Searching with the Locator}
            \endlist
            \li \l{Refactoring}
            \li \l{Applying Refactoring Actions}
            \li \l{Beautifying Source Code}
            \li \l{Configuring the Editor}
            \list
                \li \l{Specifying Text Editor Settings}
                \li \l{Using FakeVim Mode}
            \endlist
            \li \l{Using Language Servers}
            \li \l{Editing MIME Types}
            \li \l{Modeling}
            \li \l{Editing State Charts}

        \endlist
        \li \l{Building and Running}
        \list
            \li \l{Validating with Target Hardware}
            \list
                \li \l{Previewing on Desktop}
                \li \l{Previewing on Devices}
                \li \l{Previewing in Browsers}
            \endlist
            \li \l{Building for Multiple Platforms}
            \li \l{Running on Multiple Platforms}
            \li \l{Deploying to Devices}
            \list
                \li \l{Deploying to Android}
                \li \l{Deploying to Boot2Qt}
                \li \l{Deploying to QNX Neutrino}
                \li \l{Deploying to Remote Linux}
            \endlist
            \li \l{Connecting Devices}
            \list
                \li \l{Connecting Android Devices}
                \li \l{Connecting Bare Metal Devices}
                \li \l{Connecting Boot2Qt Devices}
                \li \l{Adding Docker Devices}
                \li \l{Connecting iOS Devices}
                \li \l{Connecting MCUs}
                \li \l{Connecting QNX Devices}
                \li \l{Connecting Remote Linux Devices}
                \li \l{Building Applications for the Web}
            \endlist
            \li \l{Customizing the Build Process}
        \endlist
        \li \l{Testing}
        \list
            \li \l{Debugging}
            \list
                \li \l{Setting Up Debugger}
                \li \l{Launching the Debugger}
                \li \l{Interacting with the Debugger}
                \li \l{Using Debugging Helpers}
                \li \l{Debugging Qt Quick Projects}
                \li \l{Debugging a C++ Example Application}
                \li \l{Debugging a Qt Quick Example Application}
                \li \l{Troubleshooting Debugger}
            \endlist
            \li \l{Analyzing Code}
            \list
                \li \l{Profiling QML Applications}
                \li \l{Checking Code Coverage}
                \li \l{Using Valgrind Code Analysis Tools}
                \list
                    \li \l{Detecting Memory Leaks with Memcheck}
                    \li \l{Profiling Function Execution}
                    \li \l{Running Valgrind Tools on External Applications}
                \endlist
                \li \l{Using Clang Tools}
                \li \l{Detecting Memory Leaks with Heob}
                \li \l{Analyzing CPU Usage}
                \li \l{Analyzing Code with Cppcheck}
                \li \l{Visualizing Chrome Trace Events}
            \endlist
            \li \l{Running Autotests}
            \li \l{Using Squish}
        \endlist
        \li \l{Advanced Use}
        \list
            \li \l{Supported Platforms}
                \list
                    \li \l {Desktop Platforms}
                    \li \l {Embedded Platforms}
                    \li \l {Mobile Platforms}
                \endlist
            \li \l{Build Systems}
            \list
                \li \l{Setting Up CMake}
                \li \l{Setting Up Qbs}
                \li \l{Setting Up an Autotools Project}
                \li \l{Setting Up a Generic Project}
                \li \l{Setting Up Nimble}
                \li \l{Setting Up Meson}
                \li \l{Setting Up IncrediBuild}
                \li \l{Setting Up Conan}
            \endlist
            \li \l{Using Command Line Options}
            \li \l{Keyboard Shortcuts}
            \li \l{Using External Tools}
            \li \l{Showing Task List Files in Issues}
            \li \l{Inspecting Internal Logs}
            \li \l{Managing Data Collection}
                \list
                    \li \l {Collecting Usage Statistics}
                \endlist
        \endlist
        \li  \l{Getting Help}
        \list
            \li \l{Using the Help Mode}
            \li \l{FAQ}
            \li \l{How-tos}
            \li \l{Known Issues}
            \li \l{Glossary}
            \li \l{Technical Support}
            \li \l{Acknowledgements}
        \endlist
    \endlist
*/
