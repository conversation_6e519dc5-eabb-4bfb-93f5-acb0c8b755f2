// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-form-editor.html
    \previouspage creator-using-qt-quick-designer.html
    \nextpage studio-3d-editor.html

    \title 2D

    You design applications in the \uicontrol {2D} view by opening
    component files and placing instances of \l{Component Types}{2D components}
    and \l{Assets}{assets} into them.

    \image qmldesigner-form-editor.png "The 2D view"

    When you select component instances in the \uicontrol {2D} view, markers
    appear around their edges and in their corners. Depending on the shape of
    the cursor, you can apply the following actions to the component instances
    by dragging them:

    \list
        \li \l{Moving Components}{Move}
        \li \l{Resizing 2D Components}{Resize}
        \li \l{Rotating 2D Components}{Rotate}
    \endlist

    \section1 Summary of 2D View Buttons

    The \uicontrol {2D} view toolbar contains the following buttons and
    fields.

    \table
    \header
        \li Button/Field
        \li Tooltip
        \li Read More
    \row
        \li \inlineimage icons/no_snapping.png
        \li Disables snapping.
        \li \l{Snapping to Parent and Sibling Components}
    \row
        \li \inlineimage icons/snapping_and_anchoring.png
        \li Anchors the component instance to the component instances that it
            is snapped to.
        \li \l{Snapping to Parent and Sibling Components}
    \row
        \li \inlineimage icons/snapping.png
        \li Snaps component instances to their parent or siblings when you
            align them.
        \li \l{Snapping to Parent and Sibling Components}
    \row
        \li \inlineimage icons/boundingrect.png
        \li Hides and shows component instance boundaries.
        \li \l{Hiding Component Boundaries}
    \row
        \li \uicontrol {Override Width}
        \li Shows a preview of the component using the specified width.
        \li \l{Previewing Component Size}
    \row
        \li \uicontrol {Override Height}
        \li Shows a preview of the component using the specified height.
        \li \l{Previewing Component Size}
    \row
        \li \inlineimage icons/canvas-color.png
        \li Sets the color of the the \uicontrol {2D} view working area.
        \li \l{Setting Canvas Color}
    \row
        \li \inlineimage icons/zoomIn.png
        \li Zooms in.
        \li \l{Zooming}
    \row
        \li \inlineimage icons/zoomOut.png
        \li Zooms out.
        \li \l{Zooming}
    \row
        \li Zoom level
        \li Sets the zoom level that you select from the list.
        \li \l{Zooming}
    \row
        \li \inlineimage icons/zoomAll.png
        \li Zooms to fit all content.
        \li \l{Zooming}
    \row
        \li \inlineimage icons/zoomSelection.png
        \li Zooms to fit the current selection.
        \li \l{Zooming}
    \row
        \li \inlineimage icons/reset.png
        \li Refreshes the contents of the \uicontrol {2D} view.
        \li \l{Refreshing 2D View Contents}
    \endtable

    \section1 Moving Components

    When the move cursor is displayed, you can move the selected component
    instance to any position in the \uicontrol {2D} view.

    \image qmldesigner-form-editor-move-cursor.png "Move cursor in the 2D view"

    For more information about alternative ways of positioning component
    instances in UIs, see \l{Scalable Layouts}.

    \section1 Resizing 2D Components

    When the resize cursor is displayed, you can drag the markers to resize
    component instances.

    \image qtquick-designer-scaling-items.png "The 2D view"

    To have the resizing done from the center of the selected component instance
    rather than from its edges, press \key Alt (or \key Opt on \macos).

    To preserve the image aspect ratio while resizing when using the corner
    markers, press \key Shift. This also works on component instances that
    are anchored using left, right, top, or bottom anchors.

    To both resize from the center of the component instance and preserve the
    aspect ratio, press \key Alt+Shift (or \key {Opt+Shift} on \macos).

    For more information about alternative ways to specify the size of a
    component or component instance in a UI, see \l{2D Geometry}.

    \section1 Rotating 2D Components

    When the rotation cursor \inlineimage icons/rotation-cursor.png
    is displayed in one of the corners of a component instance, you can drag
    clockwise or counter-clockwise to freely rotate the component instance
    around its origin.

    \image qtquick-designer-rotating-items.png "2D rotation tool"

    Additionally, press \key Shift or \key Alt (or \key Opt on \macos)
    to rotate component instances in steps of 5 or 45 degrees, respectively.

    You can set the \l{Managing 2D Transformations}{origin} in \l Properties >
    \uicontrol {Geometry - 2D} > \uicontrol Origin. There, you can also enter
    the value of the \uicontrol Rotation property in degrees.

    \section1 Zooming

    You can use the zoom buttons on the toolbar to zoom into and out of
    the \uicontrol {2D} view or select the zoom level as a percentage
    from a list. More buttons are availabe for zooming to fit all content
    in the view or zooming to fit the currently selected component instances.

    \image qmldesigner-zooming.gif "Zooming in the 2D view"

    \section1 Snapping to Parent and Sibling Components

    You can use snapping to align component instances in
    the \uicontrol {2D} view. Select the \inlineimage icons/snapping.png
    button to have the component instances snap to their parent or siblings.
    Snapping lines automatically appear to help you position the component
    instances. Click the \inlineimage icons/snapping_and_anchoring.png
    button to anchor the selected component instance to those that you snap to.
    Only one snapping button can be selected at the time. Selecting
    one snapping button automatically deselects the others.

    Choose \uicontrol Edit > \uicontrol Preferences > \uicontrol {Qt Quick} >
    \uicontrol {Qt Quick Designer} to specify settings for snapping. In the
    \uicontrol {Parent component padding} field, specify the distance in
    pixels between the parent and the snapping lines. In the
    \uicontrol {Sibling component spacing} field, specify the
    distance in pixels between siblings and the snapping lines.

    \image qtquick-designer-options.png "Qt Quick Designer preferences"

    The following image shows the snapping lines (1) when
    \uicontrol {Parent component padding} is set to 5 pixels.

    \image qmldesigner-snap-margins.png "Snapping lines on canvas"

    For alternative ways of aligning and distributing component instances by
    using the \l Properties view, see \l{Aligning and Distributing Components}.

    \section1 Hiding Component Boundaries

    The \uicontrol {2D} view displays the boundaries of component instances.
    To hide them, select the \inlineimage icons/boundingrect.png
    button.

    \section1 Previewing Component Size

    The width and height of the root component in a UI file determine the size
    of the component. You can reuse components, such as buttons, in different
    sizes in other UI files and design UIs for use with different device
    profiles, screen resolution, or screen orientation. The component size
    might also be zero (0,0) if its final size is determined by
    \l{Setting Bindings}{property bindings}.

    To experiment with different component sizes, enter values in the
    \uicontrol {Override Width} and \uicontrol {Override Height} fields (1) on
    the toolbar. The changes are displayed in the \uicontrol {2D}
    view (2) and in the \uicontrol States view (3), but the property
    values are not changed permanently in the UI file. You can permanently
    change the property values in the \uicontrol Properties view (4).

    \image qmldesigner-preview-size.png "Component width and height"

    To set the initial size of the root component, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Qt Quick} > \uicontrol {Qt Quick Designer}
    and specify the component width and height in the
    \uicontrol {Root Component Init Size} group.

    \section1 Specifying Canvas Size

    To change the canvas size, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Qt Quick} > \uicontrol {Qt Quick Designer} and
    specify the canvas width and height in the \uicontrol Canvas group.

    \section1 Setting Canvas Color

    If you set the background of the root component transparent, the color of
    the working area can make it difficult to see the component instance you
    are working on. To make component instances more visible, you can select
    the canvas color in the \inlineimage icons/canvas-color.png
    list. By default, the color is transparent. Setting the canvas color does
    not affect the background color of your root component or component
    instances in any way.

    \image qmldesigner-canvas-color.png "Transparent canvas color for a transparent component instance"

    \section1 Refreshing 2D View Contents

    When you open a UI file, the component defined in the file and the component
    instances it contains are drawn in the \uicontrol {2D} view. When you
    edit component instance properties in \l Properties, the code and its
    representation in the \uicontrol {2D} view might get out of sync. For
    example, when you change the position of a component instance within a
    column or a row, the new position might not be displayed correctly in
    the \uicontrol {2D} view.

    To refresh the contents of the \uicontrol {2D} view, press \key R or
    select the \inlineimage icons/reset.png
    (\uicontrol {Reset View}) button.

    \include qtquick-component-context-menu.qdocinc context-menu
*/
