// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-projects-view.html
    \if defined(qtdesignstudio)
    \previouspage qtquick-curve-editor.html
    \else
    \previouspage creator-views.html
    \endif
    \nextpage creator-file-system-view.html

    \title Projects

    The \uicontrol Projects view displays projects in a project tree.
    The project tree contains a list of all projects open in the current
    \l{Managing Sessions}{session}. For each project, the tree visualizes
    the build system structure of the project and lists all files that
    are part of the project.

    \if defined(qtdesignstudio)
    The following image displays the \uicontrol Projects view in the
    \uicontrol Design mode:

    \image qtcreator-projects-view-design.png "Projects view in the Design mode"
    \else
    \image qtcreator-projects-view-edit.png "Projects view in the sidebar"
    \endif

    You can use the project tree in the following ways:

    \list
        \li To open files that belong to a \l{Creating Projects}{project},
            double-click them in the project tree. Files open in the
            appropriate editor, according to the file type. For example, code
            source files open in the code editor. Use the \l{Selecting Modes}
            {mode selector} to open the current file in another editor.
        \li To bring up a \l{Projects View Context Menu}{context menu}
            containing the actions most commonly needed, right-click an
            item in the project tree. For example, through the menu of
            the project root directory you can, among other actions, run
            and close the project.
        \li To see the absolute path of a file, move the mouse pointer over the
            file name.
        \li To move files from one project to another, drag-and-drop them
            in the project tree. \QC makes the necessary changes to project
            configuration files.
    \endlist

    \section1 Projects View Context Menu

    The \uicontrol Projects view contains context menus for managing projects,
    subprojects, folders, and files. Use the following functions to manage
    projects and subprojects:

    \list
        \li Set a project as the active project.
        \li Execute the \uicontrol Build menu commands.
        \li Create new files. For more information, see
            \if defined(qtdesignstudio)
            \l{Adding Files to Projects}.
            \else
            \l{Creating Files}.
            \endif
        \li Rename or remove existing files. If you change the base name of a
            file, \QC displays a list of other files with the same base name
            and offers to rename them as well.
        \if defined(qtcreator)
        \li Remove existing directories from \l{Setting Up a Generic Project}
            {generic projects}.
        \li Add existing files and directories.
        \li Add libraries. For more information, see
            \l{Adding Libraries to Projects}.
        \li Add and remove subprojects.
        \endif
        \li Search from the selected directory.
        \li Open a terminal window in the project directory. To specify the
            terminal to use on Linux and \macos, select \uicontrol Edit >
            \uicontrol Preferences > \uicontrol Environment > \uicontrol System.
        \li Open a terminal window in the project directory that you configured
            for building or running the project.
        \li Expand or collapse the tree view to show or hide all files and
            folders.
        \li Close all files in a project.
        \li Close the selected project or all projects except the selected
            one. By default, this closes all files in the projects. To keep
            them open, deselect the \uicontrol Edit >
            \uicontrol Preferences > \uicontrol {Build & Run} > \uicontrol General
            > \uicontrol {Close source files along with project} check box.
    \endlist

    For managing files and directories, use the same functions as in
    the \l {File System} view. To view a project in it, select
    \uicontrol {Show in File System View}.

    \section1 Projects View Toolbar

    \if defined(qtdesignstudio)
    In the \uicontrol Edit and \uicontrol Debug mode, the
    \l{Working with Sidebars}{sidebar} contains the \uicontrol Projects
    view. It has a toolbar with additional options.

    \image qtcreator-projects-view-edit.png "Projects view in the sidebar"
    \else
    The toolbar in the \uicontrol Projects view contains additional options.
    \endif


    To filter view contents, select \inlineimage icons/filtericon.png
    (\uicontrol {Filter Tree}):

    \list
        \li \uicontrol {Simplify Tree} hides the categories and sorts project
            files alphabetically.
        \li \uicontrol {Hide Generated Files} hides the source files that the
            build system automatically generates.
        \li \uicontrol {Hide Disabled Files} hides the source files that you
            have not enabled for the current target,
        \li \uicontrol {Hide Empty Directories} hides directories that do not
            contain any files.
        \li \uicontrol {Show Source and Header Groups} shows source and header
            files grouped together in the view, regardless of their location in
            the file system.
    \endlist

    To stop synchronizing the position in the project tree with the file
    currently opened in the editor, deselect \inlineimage icons/linkicon.png
    (\uicontrol {Synchronize with Editor}).

    \if defined(qtcreator)
    Some build systems support adding and removing files to a project in \QC
    (currently qmake and Qbs). The faithful display of the project structure
    enables you to specify exactly where to place a new file in the build system.

    If you cannot see some files, you might have to declare them as part of the
    project. For more information, see \l{Specifying Project Contents}.

    If the project is under version control, you might see information
    from the version control system in brackets after the project name.
    \QC currently implements this for Git (the view displays the branch name
    or a tag) and ClearCase (the view displays the branch name).
    \else
    If the project is under Git version control, you can see the currently
    checked out branch or tag in brackets after the project name.
    \endif
*/
