// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-editor-functions.html
    \page creator-coding-navigating.html
    \if defined(qtdesignstudio)
    \nextpage creator-sidebars.html
    \else
    \nextpage creator-highlighting.html
    \endif

    \title Working in Edit Mode

    This section describes how to use the \uicontrol Edit mode. For more
    information about using the sidebar, see \l{Browsing Project Contents}.

    \section1 Using the Editor Toolbar

    The editor toolbar is located at the top of the editor view. The editor
    toolbar is context sensitive and shows items relevant to the file currently
    open in the editor.

    \if defined(qtcreator)
    \image qtcreator-editortoolbar-symbols.png
    \else
    \image studio-edit-mode.png
    \endif

    \section2 Navigating Between Open Files and Symbols

    Use the toolbar, \uicontrol Window menu items, or
    \l{General Keyboard Shortcuts}{keyboard shortcuts}
    to navigate between open files and symbols in use.

    To browse backward or forward through your
    location history, click \inlineimage icons/prev.png
    (\uicontrol {Go Back}) and \inlineimage icons/next.png
    (\uicontrol {Go Forward}).

    To return to the last location where you made a change, select
    \uicontrol Window > \uicontrol {Go to Last Edit}.

    To go to any open file, select it from the \uicontrol {Open files} drop-down
    menu (1). To open a context menu that contains commands for managing open
    files, right-click the file name or icon on the toolbar. In addition to the
    commands also available in the \uicontrol File menu, you can copy the path
    and name of the current file and the number of the line where the cursor is
    currently located to the clipboard by selecting \uicontrol {Copy Full Path},
    \uicontrol {Copy File Name}, or \uicontrol {Copy Path and Line Number}.

    To jump to any symbol used in the current file, select it from the
    \uicontrol Symbols drop-down menu (2). By default, the symbols are displayed
    in the order in which they appear in the file. Right-click the menu title
    and select \uicontrol {Sort Alphabetically} to arrange the symbols in
    alphabetic order.

    To jump to a line and column in the current file, select the line and column
    indicator (3) or press \key {Ctrl+K} (or \key {Cmd+K} on \macos) to open the
    \l{Searching with the Locator}{locator}. Enter the line number and column
    number in the locator, separated by a colon (:).

    \note Other convenient ways of navigating in \QC are provided
    by the \l{Browsing Project Contents}{sidebars}.

    \if defined(qtcreator)
    \section2 Selecting Parse Context

    Code might be interpreted differently in different contexts. A file can be
    used by different projects or subprojects with different defines, or it can
    be included in the context of C, C++, Objective-C, or Objective-C++. To
    change the active parse context, select an available parse context in the
    \uicontrol {Active Parse Context} menu (4). The menu is visible only when
    several parse contexts are available. To reset the parse context,
    right-click on the menu to open a context menu, and then select
    \uicontrol {Clear Preferred Parse Context}.
    If the information needed for parsing the project is still incomplete or
    incorrect, select \uicontrol {Additional Preprocessor Directives} to
    add preprocessor directives.

    \section2 Changing Text Encoding

    To show the file encoding of the current file on the editor toolbar (5),
    select \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol Display > \uicontrol {Display file encoding}.

    To change the text encoding, click it on the toolbar and select new
    encoding in the \uicontrol {Text Encoding} dialog:

    \image qtcreator-text-encoding.png "Text Encoding dialog"

    To reload the file with the selected encoding, select
    \uicontrol {Reload with Encoding}. To save the file with
    the new encoding, select \uicontrol {Save with Encoding}.
    \endif

    \section2 Selecting Line Ending Style

    To switch between Windows line endings (CRLF) and Unix line endings (LF),
    select the ending style on the editor toolbar (6). To hide this field,
    select \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor}
    > \uicontrol Display, and deselect \uicontrol {Display file line ending}.

    To set the line endings to use for all projects by default, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol Behavior, and then select the ending style in the
    \uicontrol {Default line endings} field.

    To set the line endings to use for a project, select \uicontrol Projects >
    \uicontrol {Project Settings} > \uicontrol Editor.
    \if defined(qtcreator)
    For more information, see \l {Specifying Editor Settings}.
    \endif

    \section1 Editing Selected Lines

    The \uicontrol Edit > \uicontrol Advanced menu contains options for editing
    selected lines of text.

    To duplicate the selected lines, select \uicontrol {Duplicate Selection}.
    To format the duplicated lines as a comment, select
    \uicontrol {Duplicate Selection and Comment}.

    To turn selected text into lowercase, select \uicontrol {Lowercase Selection}
    or press \key {Alt+U}. To turn it into uppercase, select
    \uicontrol {Uppercase Selection} or press \key {Alt+Shift+U}.

    To sort selected lines alphabetically, select \uicontrol {Sort Selected Lines}
    or press \key {Alt+Shift+S}.

    Select \uicontrol {Add Next Occurrence to Selection} or press \key {Ctrl+D}
    to add a cursor at the next occurrence of selected text for multi-cursor
    editing.

    \section1 Multi-Cursor Editing

    To apply a change to several places simultaneously, press and hold \key Alt,
    and click to place cursors in several places. Any changes you make are
    applied simultaneously at all the cursor positions.

    Use the arrow keys to move all the cursors up and down. The \key Home and
    \key End key move all the cursors to the beginning or to the end of the
    line.

    Press and hold \key Alt and double-click strings to select several strings
    simultaneously.

    Press \key {Alt+Shift+I} to create cursors at the ends of selected lines.

    Press \key Esc to remove all the cursors and selections.

    \section1 Splitting the Editor View

    Split the editor view or open the editor in a new window when you want to
    work on and view multiple files on the same screen or on multiple screens.

    \image qtcreator-spliteditorview.png

    You can view multiple files simultaneously in the following ways:

    \list

        \li To split the editor view into a top and bottom view, select
            \uicontrol Window > \uicontrol Split, press \key {Ctrl+E, 2}, or
            select the \inlineimage icons/splitbutton_horizontal.png
            (\uicontrol Split) button and then select \uicontrol Split.

            Split command creates views below the currently active editor view.

        \li To split the editor view into adjacent views, select
            \uicontrol Window > \uicontrol {Split Side by Side}, press
            \key {Ctrl+E, 3}, or select \uicontrol Split >
            \uicontrol {Split Side by Side}.

           Side by side split command creates views to the right of the
           currently active editor view.

        \li To open the editor in a detached window, press \key {Ctrl+E, 4}, or
            select \uicontrol Window > \uicontrol {Open in New Window}.

            The new window behaves basically in the same way as the editor area
            in the main window. For example, you can split this window, as well.
            Documents are opened in the currently active editor window.

    \endlist

    To move between split views and detached editor windows, select
    \uicontrol Window > \uicontrol {Go to Next Split or Window} or press
    \key {Ctrl+E, O}.

    To remove a split view, place the cursor within the view you want to
    remove and select \uicontrol Window > \uicontrol {Remove Current Split},
    press \key {Ctrl+E, 0}, or select the \inlineimage icons/splitbutton_closetop.png
    (\uicontrol {Remove Split}) button. To remove all but the currently selected
    split view, select \uicontrol Window > \uicontrol {Remove All Splits} or
    press \key {Ctrl+E, 1}.

    \section1 Using Bookmarks

    To insert or delete a bookmark in the \uicontrol Edit mode:

    \list

        \li Right-click the line number and select \uicontrol {Toggle Bookmark}.

        \li Press \key Shift and click the left margin at a line.

        \li Press \key {Ctrl+M} when the cursor is on a line.

    \endlist

    \image qtcreator-togglebookmark.png

    \section2 Adding Notes to Bookmarks

    To add a note to a bookmark:

    \list
        \li Select \uicontrol Tools > \uicontrol Bookmarks >
            \uicontrol {Edit Bookmark}.
        \li Press \key {Ctrl+Shift+M}.
        \li Right-click a bookmark and select \uicontrol {Edit Bookmark}
            in the context menu.
    \endlist

    To view the note, move the mouse pointer over the bookmark or open the
    \uicontrol Bookmarks view in the \l{Working with Sidebars}{sidebar}.

    \section2 Navigating Bookmarks

    To go to the previous bookmark in the current session, select
    \uicontrol Tools > \uicontrol Bookmarks > \uicontrol {Previous Bookmark}
    or press \key {Ctrl+,}.

    To go to the next bookmark in the current session, select \uicontrol Tools >
    \uicontrol Bookmarks > \uicontrol {Previous Bookmark} or press
    \key {Ctrl+.}.

    To use the locator to go to a bookmark, press \key {Ctrl+K} (or \key {Cmd+K}
    on \macos) to open the locator. Enter \e b and a space to display a list of
    bookmarks. To filter the bookmarks by line number or a text string, enter
    the number or string after the space. Double-click a bookmark in the list to
    go to it in the editor.

    \image qtcreator-locator-bookmark-filtering.png "Filtering bookmarks in locator"

    \section2 Viewing Bookmarks

    Bookmarks are listed in the \uicontrol Bookmarks view in the sidebar. To
    move between bookmarks, select the \uicontrol {Previous Bookmark} or
    \uicontrol {Next Bookmark} button or use the keyboard shortcuts.

    \image qtcreator-bookmarks-view.png "Listing bookmarks in Bookmarks view"


    \section1 Moving to Symbol Definition or Declaration

    You can move directly to the definition or the declaration of a symbol
    in the \uicontrol Edit mode by
    holding the \key Ctrl key and clicking the symbol. If you have multiple
    splits opened, you can open the link in the next split by holding \key Ctrl
    and \key Alt while clicking the symbol.

    To enable this moving function, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol Behavior >
    \uicontrol {Enable mouse navigation}.

    There are several additional ways of moving between symbol definitions and
    declarations. All the functions described below are also available from the
    \uicontrol Tools > \uicontrol C++ menu. The functions supported for QML and
    JavaScript code are available from the \uicontrol Tools > \uicontrol QML/JS
    menu.

    You can select the symbol and press \key F2, or right-click the symbol
    and select \uicontrol {Follow Symbol Under Cursor} to move to its definition
    or declaration. To follow the symbol in the next split, select
    \uicontrol {Follow Symbol Under Cursor in Next Split}. Following symbols is
    supported for namespaces, classes, functions, variables, include statements,
    and macros.

    To move to the type definition of a symbol, select
    \uicontrol {Follow Symbol Under Cursor to Type} or press
    \key {Ctrl+Shift+F2}. To follow the symbol in the next split, select
    \uicontrol {Follow Symbol Under Cursor to Type in Next Split} or
    press \key {Ctrl+E, Ctrl+Shift+F2}.

    When the cursor is on a string literal and the string is a QRC file path,
    following the symbol opens the specified resource file for editing in the
    \uicontrol {Resource Browser}.

    To switch between the definition and declaration of a function, place the
    cursor on either and press \key {Shift+F2} or right-click and select
    \uicontrol {Switch Between Function Declaration/Definition} or
    \uicontrol {Open Function Declaration/Definition in Next Split}.
    For example, this allows you to navigate from anywhere within a function
    body directly to the function declaration.

    Links are opened in the same split by default. To open links in the next
    split, prepend \key {Ctrl+E} to the shortcut. For example, press
    \key {Ctrl+E,F2} to follow the symbol in the next split. If necessary, the
    view is automatically split. To change the default behavior, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol Display > \uicontrol {Always open links in another split}.
    Additional symbols are
    displayed and switching between definition and declaration is done in
    another split. If you change the default behavior, the shortcuts for opening
    link targets in the next split are used to open them in the current split.

    To switch between C++ header and source files, right-click anywhere in a
    file and select \uicontrol {Switch Header/Source} or
    \uicontrol {Open Corresponding Header/Source in Next Split}. You can also
    press \key F4 or \key {Ctrl+E,F4}, respectively.

    \if defined(qtcreator)
    \section1 Reparsing Externally Changed Files

    If source files are modified from outside \QC, the opened files will be
    reparsed automatically. For all other files, you can use \uicontrol Tools >
    \uicontrol {C++} > \uicontrol {Reparse Externally Changed Files} to update
    the code model.

    \section1 Inspecting the Code Model

    When you \l{https://bugreports.qt.io/}{report a bug} that is related to the
    C++ code model, the \QC developers might ask you to write information about
    the internal state of the code model into a log file and to deliver the file
    to them for inspection.

    To view information about the C++ code model in the
    \uicontrol {C++ Code Model Inspector} dialog and write it to a log file,
    select \uicontrol Tools > \uicontrol {Debug \QC } >
    \uicontrol {Inspect C++ Code Model} or press \key {Ctrl+Shift+F12}.

    \QC generates the code model inspection log file in a temporary folder.

    \QC underlines semantic errors in olive in the C++ code editor. To check the
    correct paths for includes that are not resolved or that are resolved to the
    wrong file, select \uicontrol {Project Parts} > \uicontrol {Header Paths}.
    \endif

    \if defined(qtdesignstudio)
    \section1 Related Topics

    \list
        \li \l{Working with Sidebars}
        \li \l{Browsing Project Contents}
        \li \l{Viewing Output}
    \endlist
    \endif
*/
