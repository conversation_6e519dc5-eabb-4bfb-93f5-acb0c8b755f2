// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qt-design-viewer.html
    \previouspage creator-live-preview-devices.html
    \nextpage creator-building-targets.html

    \title Previewing in Browsers

    \image qt-design-viewer.png

    \QDV is a QML viewer that runs in your web browser. This means that you can
    run applications in most widely-used web browsers, such as Apple Safari,
    Google Chrome, Microsoft Edge, and Mozilla Firefox, on the desktop and on
    mobile devices.

    The startup and compilation time depend on your browser and configuration.
    However, the actual performance of the application once started is
    indistinguishable from the same application running on the desktop.

    You can run \l{Creating Qt Quick UI Projects}{Qt Quick UI projects}, which
    have a .qmlproject file that define the main QML file and the import paths.
    Compress the project folder into a ZIP file that you upload to \QDV.

    The loaded applications remain locally in your browser. No data is uploaded
    into the cloud.

    To preview an application in a web browser:

    \list 1
        \li In the browser, open \l{ https://designviewer.qt.io/}{\QDV}.
        \li Drag and drop your application package to \QDV, or click the load
            icon to browse for your file.
    \endlist

    Your application is compiled and run on \QDV.
*/
