// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page multilanguage-tutorial.html
    \ingroup gstutorials

    \title Adding Multi-language Support to Your Project
    \brief Illustrates how to add support for multiple languages to your
    project.

    \image multilanguage-project.png

    The \e{Multi-language Support} tutorial illustrates how you can add
    support for multiple languages to your project. In this tutorial you
    prepare a simple log in UI for multi-language support and import
    translations from a JSON file.

    You need to download the starting project for this tutorial from
    \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/tutorial%20projects/multi-language%20tutorial/Loginui2}{here}
    before you start.

    Download the project and open the \e loginui2.qmlproject file in \QDS
    to get started.

    This project consists of a login page with a couple of text elements.

    Additionally, you will use a JSON translation file in this tutorial.
    Download it from \l{https://git.qt.io/public-demos/qtdesign-studio/-/tree/master/tutorial%20projects/multi-language}{here}.

    \section1 JSON Translation File

    The JSON translation file you are using in this project has the following
    structure:

    \code
    {
    "translatables": [
        {
            "trId": "translation_password",
            "translations": [
                {
                    "language": "en",
                    "translation": "Password"
                },
                {
                    "language": "sv",
                    "translation": "Lösenord"
                }
            ]
        },
        {
            "trId": "translation_account",
            "translations": [
                {
                    "language": "en",
                    "translation": "Qt Account"
                },
                {
                    "language": "sv",
                    "translation": "Qt Konto"
                }
            ]
        },
        {
            "trId": "translation_createaccount",
            "translations": [
                {
                    "language": "en",
                    "translation": "Create Account"
                },
                {
                    "language": "sv",
                    "translation": "Skapa Konto"
                }
            ]
        },
        {
            "trId": "translation_login",
            "translations": [
                {
                    "language": "en",
                    "translation": "Log In"
                },
                {
                    "language": "sv",
                    "translation": "Logga In"
                }
            ]
        },
        {
            "trId": "translation_username",
            "translations": [
                {
                    "language": "en",
                    "translation": "Username"
                },
                {
                    "language": "sv",
                    "translation": "Användarnamn"
                }
            ]
        }
    ]
}
    \endcode

\section1 Preparing Your Project

    First, you need to prepare your project for translation:

    \list 1
      \li In \uicontrol{Projects}, double-click \e{Screen01.ui.qml} to open it.
      \li All text strings that you want to translate need to be of \c
      qsTrId type. In this project the text strings are of \c qsTr type so you
      need to change them. Open the \uicontrol{Code} view, find all five
      occurrences of \c qsTr and replace them with \c{qsTrId}. For example,
      replace:
      \code
        text: qsTr("Qt Account")
      \endcode
      with:
      \code
        text: qsTrId("Qt Account")
      \endcode
      \li Next, you need to replace the text value with the text string
      variable from the translation file for the five text strings you want
      to translate. For example, replace:
      \code
        text: qsTrId("Qt Account")
      \endcode
      with:
      \code
        text: qsTrId("translation_account")
      \endcode
    \endlist

    \section1 Importing Translations

    Next, you need to import your JSON translation file:

    \list 1
      \li Go to \uicontrol View > \uicontrol Views and select \uicontrol
      Translations to open the \uicontrol Translations view. You can
      drag it to a workspace to dock it.
      \li In \uicontrol Translations, select
      \inlineimage icons/select-languages.png
      .
      \li Select the languages that you want to support in your project,
      in this case \uicontrol{English - American English} and
      \uicontrol{Swedish - Svenska}.
      \li Set \uicontrol{English - American English} as primary language and
      select \uicontrol{Ok}.
      \li Select \inlineimage icons/import-json-translations.png
      and open the \e ml_translations.json file. Now you can see all your
      imported translations in the \uicontrol Translations view.
      \image imported-translations.png
    \endlist

    You can select language from the dropdown list on the left side of the
    \uicontrol Translations view. Translations for the selected languages will
    show in the \uicontrol Translations view as well as in the
    \uicontrol {2D} view.

    You can run translation tests, import, and export translations as described
    in \l{Translations}.

    Finally, before you can see your translations in the application or in
    \uicontrol{Live Preview}, you must generate Qt translation source files.
    To do this, select
    \inlineimage icons/generate-translation-files.png
    in the \uicontrol Translations view.

*/
