// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-view.html
    \previouspage quick-animations.html
    \nextpage studio-3d-node.html

    \title 3D Views

    To create a Qt Quick 3D UI project, we recommend using a \uicontrol
    {Qt Quick 3D Application} wizard template that adds the
    \l {3D Components}{Qt Quick 3D} components to \uicontrol Components
    and contains a 3D view. A 3D view component includes a
    \l {Scene Environment}{scene environment} as well as a scene
    \l {Lights}{light}, \l {Cameras}{camera}, and
    \l {3D Models}{model}. A default \l {Materials and Shaders}{material}
    is attached to the model. You can attach \l {Textures}{textures}
    to materials. For more information about creating projects, see \l{Creating Projects}.

    To add a 3D view to some other kind of a project, you first need to add the
    \uicontrol {Qt Quick 3D} module to \uicontrol {Components}, as described in
    \l {Adding and Removing Modules}.

    \image studio-qtquick-3d-components.png "Qt Quick 3D components in Components"

    You can now drag-and-drop a \uicontrol View3D component to the \l Navigator
    or \l {2D} view.

    \image studio-navigator-view3d.png "A View 3D component in the Navigator"

    By default, a directional light and a perspective camera are used in a 3D
    scene created by using the wizard template mentioned above. To use other
    light and camera types, select the component in the \uicontrol{3D} or
    \uicontrol Navigatorn view and change the type of the component in the \uicontrol
    Type field in \l Properties. For example, to use a point light, enter
    \e {PointLight}.

    \image studio-3d-properties-type.png "Type field in Properties view"

    Similarly to other components, you can select a 3D view in \uicontrol
    Navigator or the \uicontrol{3D} view and modify its property values in the
    \uicontrol Properties view. Use the properties in the \uicontrol View3D
    tab to set properties specific to a 3D view component.

    \image studio-qtquick-3d-view.png "View 3D component properties"

    The \uicontrol Camera property defines which camera is used to render the
    scene to the \uicontrol {2D} view. If this property is not defined, the
    first enabled camera in the scene will be used.

    The \uicontrol Environment property specifies the \uicontrol
    {Scene Environment} used to render the scene. By default, the first
    \uicontrol {Scene Environment} in the scene is set as the property value.

    The \uicontrol {Import Scene} property defines the ID of the component to
    render to the \uicontrol {2D} view. The component does not have to be a
    child of a 3D view component. This referenced component becomes a sibling to
    child items of a 3D view, if there are any. You can use this property, for
    example, to create a split screen view showing your scene from multiple
    cameras. For more information on how to to use a 3D view to show a scene
    from multiple cameras, see \l {Qt Quick 3D - View3D Example}.

    \note The \uicontrol {Import Scene} property can only be set once.
    Subsequent changes will have no effect.
*/
