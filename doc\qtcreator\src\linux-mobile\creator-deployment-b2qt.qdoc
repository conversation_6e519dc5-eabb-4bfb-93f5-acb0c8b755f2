// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-deployment-b2qt.html
    \previouspage creator-deploying-android.html
    \nextpage creator-deployment-qnx.html

    \title Deploying to Boot2Qt

    You can specify settings for deploying applications to \l{Boot2Qt} devices
    in the project configuration file and in \uicontrol Projects >
    \uicontrol {Run Settings} > \uicontrol Deployment.

    \image qtcreator-boot2qt-deployment-steps.png "Boot2Qt deployment steps"

    The deployment process is described in more detail in
    \l{Deploying to Remote Linux}.

    \section1 Launching Applications on Boot

    In addition, to have your application launch on boot, select
    \uicontrol {Add Deploy Step} > \uicontrol {Change default application}
    > \uicontrol {Set this application to start by default}.
*/
