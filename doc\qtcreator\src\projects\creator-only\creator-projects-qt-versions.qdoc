// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-targets.html
    \page creator-project-qmake.html
    \nextpage creator-tool-chains.html

    \title Adding Qt Versions

    You can install multiple versions of Qt development PC and use them to build
    your projects. For example, \l{glossary-device}{device} manufacturers provide
    special Qt versions for developing applications for their devices.

    \section1 Registering Installed Qt Versions

    The \uicontrol {Qt Versions} tab lists the installed Qt versions. To view
    detailed information about each Qt version, select it in the list and select
    \uicontrol Details in the \uicontrol {Qt version for} section.

    \image qtcreator-qt-versions.png "Qt Versions tab in Kit preferences"

    To remove invalid Qt versions, select \uicontrol {Clean Up}.

    You can link to a Qt that the Qt Installer installed to
    automatically detect the installed Qt versions. However, you cannot link
    to a Qt that the system installed with some other package
    manager, such as your Linux distribution, brew on \macos, or Chocolatey on
    Windows, nor a self-built Qt. In those cases, select \uicontrol {Add} in
    the \uicontrol {Qt Versions} tab to add the Qt version manually, as
    instructed in \l{Setting Up New Qt Versions}.

    To link to a Qt installation:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences >
            (or \uicontrol {Qt Creator} > \uicontrol Preferences on
            \macos) > \uicontrol Kits > \uicontrol {Qt Versions} >
            \uicontrol {Link with Qt}.
            \image qtcreator-link-with-qt.png "Choose Qt Installation dialog"
        \li In the \uicontrol {Qt installation path} field, enter the path to
            the directory where you installed Qt.
        \li Select \uicontrol {Link with Qt} to automatically register Qt
            versions and kits in the Qt installation directory.
        \li Select \uicontrol {Restart Now} to restart \QC.
    \endlist

    To remove the automatically detected Qt versions from the list, select
    \uicontrol {Remove Link}.

    If the \uicontrol {Qt Versions} tab does not show a Qt version
    under \uicontrol Auto-detected, set it up manually, as described
    in the following section.

    You specify the Qt version to use for each \l{glossary-buildandrun-kit}
    {kit} for building and running your projects
    in \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits, as described
    in \l{Specifying Kit Settings}.

    \section1 Setting Up New Qt Versions

    To add a Qt version:

        \list 1

            \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
                \uicontrol {Qt Versions} > \uicontrol Add.

            \li Select the qmake executable for the Qt version to add.

            \li Select the Qt version to view and edit it.

            \li In the \uicontrol{Name} field, edit the name that \QC
                suggests for the Qt version.

            \li In the \uicontrol{qmake path} field, you can change the qmake
                location.

            \li If the Qt version is for QNX, enter the path to the QNX SDK in the
                \uicontrol{QNX SDK} field.

        \endlist

    To remove a Qt version that you added manually, select it in the
    \uicontrol Manual list and then select \uicontrol Remove.

    \section1 Registering Documentation

    By default, \QC registers only the latest available version of the documentation for each
    installed Qt module.

    To register the documentation sets of all installed Qt versions, choose
    \uicontrol{All} from the \uicontrol{Register documentation} list.
    To register no Qt documentation at all, choose \uicontrol{None}.
    The default behavior is \uicontrol{Highest Version Only}.

    \section1 Troubleshooting Qt Installations

    If \QC detects problems in the installation of a Qt version, it displays
    warnings and errors beside the name of the Qt version in the list. Select
    the Qt version to see more information about the issue and suggestions for
    fixing it.

    To verify the installation of a particular Qt version, \QC
    calls \c {qmake -query} and checks that the directories referenced in the
    output exist. When \QC complains about the installation of a self-built Qt
    version, try running \c {make install} in the build directory to actually
    install Qt into the configured location. If you installed Qt using the Qt
    Installer, run the Qt maintenance tool to check for updates or to reinstall
    the Qt version.

    \section1 Minimum Requirements
    If your build of Qt is incomplete but you still want to use qmake as build
    system, you need to ensure the following minimum requirements to use that
    setup with \QC.

    \list 1
        \li qmake is an executable that understands the \c -query command line
            argument.
        \li The \c bin and \c include directories have to exist. \QC fetches
            these directories by running \c{qmake -query}.
        \li The \c mkspecs directory should be complete enough to parse .pro
            files.
    \endlist

    If your Qt version has no \c libQtCore.so, \QC cannot detect the ABI.

*/

