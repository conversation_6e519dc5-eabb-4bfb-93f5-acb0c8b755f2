// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-fonts.html
    \if defined(qtdesignstudio)
    \previouspage studio-importing-2d.html
    \nextpage studio-importing-3d.html
    \else
    \previouspage qtquick-positioning.html
    \nextpage qtquick-annotations.html
    \endif

    \title Using Custom Fonts

    For your UI to use custom fonts when you preview it on a device,
    you have to import the fonts to the project folder. \QDS deploys them to
    the device along with your UI.

    \note Use fixed-width fonts if you plan to animate running numbers, so that
    they do not appear to jump around the screen when they change.

    To add fonts:

    \list 1
        \li Select \uicontrol Assets
         > \inlineimage icons/plus.png
         .
        \li Browse to the folder that contains the font files and select them,
            and then select \uicontrol Open.
        \li Select the location where the file will be saved in the
            \uicontrol {Add Resources} dialog.
        \li Select \uicontrol OK to save the fonts.
    \endlist

    The fonts are added to the list of fonts for the \uicontrol Font property
    in \l Properties.
*/
