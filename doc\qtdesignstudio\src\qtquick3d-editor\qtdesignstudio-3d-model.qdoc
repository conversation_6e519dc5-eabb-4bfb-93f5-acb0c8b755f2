// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page studio-3d-model.html
    \previouspage studio-skeletal-components.html
    \nextpage studio-3d-materials.html

    \title 3D Models

    The \l Components view features some built-in primitive 3D models. This allows
    you to add cubes, cones, cylinders, and planes (rectangles) to your scene.

    \image studio-3d-models.png "Various 3D models in the 3D view"

    A model component loads mesh data from a file. You can modify how the
    component is shaded by using materials. For more information, see
    \l {Materials and Shaders} and \l {Creating Custom Materials}.

    To add a model component to your UI, do one of the following:
    \list
      \li Drag a model component from \uicontrol Components >
      \uicontrol {Qt Quick 3D} to the \l {3D} view or to
      \l Navigator > \uicontrol {View3D} > \uicontrol Scene.
      \li Right-click in the \uicontrol 3D view and select
      \uicontrol Create > \uicontrol Primitives from the context menu.
      \note You can not create \uicontrol Empty models this way.
    \endlist

    If you cannot find the model components in \uicontrol {Components}, add the
    \uicontrol QtQuick3D module to your project, as described in
    \l {Adding and Removing Modules}.

    \image studio-qtquick-3d-components.png "The Qt Quick 3D section in Components"

    \section1 Model Properties

    You can change the model type in \uicontrol Properties > \uicontrol Model
    > \uicontrol Source field. Select the \inlineimage icons/plus.png
    button to add custom model types to the list.

    \image studio-qtquick-3d-model.png "Model properties"

    To use the geometry of this model when rendering to shadow maps, select the
    \uicontrol {Casts shadows} check box. To allow casting shadows on the model,
    select the \uicontrol {Receives shadows} check box.

    To enable picking the model against the scene, select the
    \uicontrol Pickable check box. Picking transforms the screen
    space x and y coordinates to a ray cast towards the specified
    position in scene space.

    A model can consist of several sub-meshes, each of which can have its own
    material. Select the material from the list in the \uicontrol {Materials}
    field. Select the \inlineimage icons/plus.png
    button to add materials to the list. For more information about materials,
    see \l {Materials and Shaders}.

    Specify a custom geometry for the model in the \uicontrol Geometry field.
    The \uicontrol Source field must be empty when custom geometry is used.

    Set the \uicontrol Instancing property to render a number of instances of
    the model as defined by an instancing table. The instancing tables can be
    defined by using one of the \l{Instanced Rendering}{instancing components}
    available in \uicontrol Components > \uicontrol {Qt Quick 3D}
    > {Qt Quick 3D}.

    Set the \uicontrol {Instance root} property to define the origin of the
    instance's coordinate system.

    Specify the \uicontrol Skeleton property to define the \uicontrol Skeleton
    component used for this model. \uicontrol Skeletons are used for skeletal
    animation.

    Specify \uicontrol {Morph targets} to use for rendering the provided
    geometry. Meshes should have at least one attribute among positions, normals,
    tangent, and bitangent for the morph targets. \uicontrol {Quick 3D} supports
    the maximum of eight \uicontrol {Morph targets}. Any additional targets
    after the first eight will be ignored. This property is not used when the
    model is shaded by a \uicontrol {Custom Material}.

    Define the \uicontrol {Depth bias} property to ensure the shadows of your
    model are displayed correctly.

    \section1 Tessellation Properties Available in Qt 5

    The properties discussed in this section are only available if you selected
    \uicontrol {Qt 5} as the \uicontrol {Target Qt Version} when creating the
    project.

    \image studio-qtquick-3d-properties-tessellation.png "Tessellation properties"

    Tessellation refers to additional geometry that resembles tiling, which you
    can add to your model. To dynamically generate tessellation for the model,
    select \uicontrol Linear, \uicontrol Phong, or \uicontrol NPatch as the
    \uicontrol {Tessellation mode}. Tessellation is useful when using a
    displacement map with geometry, or to generate a smoother silhouette
    when zooming in.

    Specify an edge multiplier to the tessellation generator in the
    \uicontrol {Edge tessellation} field and an inner multiplier in
    the \uicontrol {Inner tessellation} field.

    To display a wireframe that highlights the additional geometry created by
    the tessellation generator, select the \uicontrol {Enable wireframe mode}
    check box.

*/
