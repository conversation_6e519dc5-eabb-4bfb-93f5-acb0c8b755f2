The QtCreator 2.0 release contains bug fixes and new features.

Below is a list of relevant changes. You can find a complete list of changes
within the logs of Qt Creator's sources. Simply check it out from the public git
repository e.g.,

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --cherry-pick --pretty=oneline v1.3.1...v2.0.0

General
   * The Output mode was replaced by the functionality to maximize
     the output panes
   * File dialog is now opened in current directory
   * File system view improvements (context menu)
   * Options dialog: Added filters and ordering
   * .user-files get now saved before they are upgraded to a new version
     of Qt creator
   * Added option to set interface language explicitly, overriding the locale
     setting
   * New "Design" mode for visual editors
   * Fixed crash when working with empty pro-file
   * Ask for close, save, or save as when file is removed while open in Qt Creator
   * Use shadowbuilding by default in new projects whenever possible

Editing
   * Text based search and replace in multiple files
   * Added option to disable scroll wheel zooming
   * Added option to indent braces (aligning them with the indented block)
   * Added option to choose tabs or spaces based on surrounding code
   * Added Join Lines action (Ctrl+J)
   * Code completion is now only first-letter case-sensitive by default
   * Open with "System Editor"
   * Fixed missing semicolon after } when creating unnamed enumeration
   * Fixed auto indent for C style coments
   * Copying text from the editor now supports HTML mime type, preserving
     the syntax highlighting.
   * Block selection defines a find & replace scope
   * Added customizable default file encoding (in addition to the project setting)
   * Added syntax highlighting for CMake project files
   * Fixed .pro files being reformatted if files have been added or removed.
     In addition, whitespace is preserved
   * Fixed the file system sidebar to update automatically
   * Fixed updating code completion for generated UI header files

CodePaster
   * Implemented new protocol of pastebin.com including list functionality

C++ Support
   * Recognize *.cu as C files
   * Fixed false errors in code model on windows when used with MSVC
   * Added "Auto fold first comment" option

Project support
   * Reworked support for developing for different targets:
      * Reworked project settings
      * Reworked selection of active configuration
   * Expand environment variables in Generic Project file lists
   * Preserve the original paths of the Generic Project file list when
     adding/removing files
   * Allow changing the build environment for Generic Projects
   * Added context menu options to open file manager or terminal in a files
     directory
   * Fixed the DEFINES and INCLUDES set in .pro files to be dealt with
     on a file-specific level and enabled the handling of DEFINES.
     Also, the .qmake.cache is now parsed

Debugging
   * Add on-device debugging for the Symbian platform using gdb
   * Add on-device debugging for the Maemo platform using gdb
   * gdb: Replace compiled "debugging helpers" by Python based ones
     (except on Mac)
   * New debugging helpers for QUrl, QAtomicInt, __gnu_ext::hash_map and more
   * Additional features for breakpoint, disassembler output, stack,
     registers and watchers views
   * Improve variable tooltip handling
   * gdb: Support reverse execution where available
   * gdb: Added mixed disassembler/source output whenever possible
   * cdb: Fixed bug in location marker
   * cdb: Fixed handling of mixed-case file names correctly by normalizing file
     names
   * pdb: Added some basic debugging for Python scripts based on pdb
   * Improvements in the dialogs, status messages, and general appearance
   * Fixed debugging helpers to work while debugging applications on devices
   * On Linux and Windows, enabled installing Qt with one user account and
     then using it with another without workarounds
   * Fixed all data types to work in the Watch Window of CDB

Help

QML/JS Support
  * Runtime QML errors become links to the location of the error
  * Extended code navigation and completion
  * Added initial semantic checks

  * New qml based .qmlproject file format (replaces old format)

Platform Specific

Mac
   * Show build error count in Dock icon
   * Handle the OBJECTIVE_HEADERS qmake variable

Linux (GNOME and KDE)
   * Dropping files on the editor now opens them instead of inserting a url

Windows
   * Fixed possible crash when searching files in file system
   * Show build error count in Taskbar (Windows 7 only)
   * Show build progress in Taskbar (Windows 7 only)
   * Support Windows SDKs
   * Register Creator for post-mortem debugging using the
     settings dialog

Symbian Target
   * Deploying shows real progress bar
   * Show the connection state of devices in the selector
     for the active configuration
   * Respect the EPOCROOT environment variable and the default device
     if no S60 SDK is explicitly given in the Qt Version preferences
   * Prevent launching of debugger while application is running and vice
     versa
   * Handle unplugging of device
   * Handle application crash when running, print proper error message
   * Add support for command line arguments
   * Move package creation and signing to be a build step
     (Users that had multiple symbian run configurations with different
      signing settings need to re-create their settings as different
      build configurations)
   * Add support for the mobility API into any mobile project set up
     using the Qt wizards
   * Add capabilities and UID into the pro-file

Maemo Target
   * New feature
   * Built on top of MADDE package
   * Supports building for, deploying to and running on N900 devices
   * Add support for the mobility API into any mobile project set up
     using the Qt wizards

Version control plugins
   * Made menu actions more consistent, added some missing actions
   * Added "Annotate previous" accessible from context menu of annotation
     view, added "annotate" action to context menu of file log
   * Added command locator to be able to trigger menu actions from the
     locator.
   * Added setting for time-outs
   * Added a plugin for Mercurial
   * Diff highlighter: Mark trailing whitespace in added lines
   * Support creating repositories from the project wizards (git, Mercurial)
   * git: Added support for stashes
   * git: Support adding files with intend-to-add in file wizards including
     check on version of git
   * git: Expand commit template relative to repository
   * git: Added setting for whitespace in diff/blame
   * git: Added options to apply patches from file and current editor,
     allowing for CodePaster-based review workflows
   * Subversion: Add parent directories correctly when adding new files
   * Added "Open file" context menu action to log pane

Qt Designer integration
   * Use 'Design' mode

Wizards
   * Improved summary page, added ordering, simplified application wizards
   * Added wizards for unit tests and classes based on QSharedData
   * Introduced project directory setting
   * Add a wizard for mobile Qt applications

Additional credits go to:
   * Roopesh Chander (choosing spaces or tabs based on surrounding code)
   * Jeffrey Baumes (added option to indent braces)
   * Roquette (fixed missing semicolon)
   * Sergey Demchenko (fixed auto indent for C style coments)
   * Falco Hirschenberger (added Join Lines action)
   * Brian McGillion (Mercurial plugin)
   * Laurent Desmecht (build configuration fixes)
