// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-qml-performance-monitor.html
    \page creator-coco.html
    \nextpage creator-valgrind-overview.html

    \title Checking Code Coverage

    \l{https://doc.qt.io/coco/}{Coco} is a complete code coverage tool chain for
    Tcl, QML, C# and C/C++ programs, which runs on \macOS, Linux, and Windows.

    Coco analyzes the way an application runs, as part of a test suite,
    for example. The results enable you to make the tests more efficient and
    complete.

    You can:

    \list
        \li Find untested code sections.
        \li Find redundant tests which can then be eliminated. Coco can
            identify portions of the source code that are covered by a test. It
            can detect whether a new test covers lines in the source code that
            the existing tests do not cover.
        \li Find dead code by displaying code that is never executed.
        \li Calculate the optimum test execution order so as to maximize
            test coverage for each run. This is particularly useful for manual
            testing.
        \li Analyze two separate versions of an application and compare the
            differences. This makes it possible to see which tests are affected
            by source code modifications and also to get some measure of the
            test coverage of a patch or hot fix.
        \li Measure the execution time of applications and tests.
    \endlist

    The experimental Coco plugin integrates Coco CoverageBrowser into \QC.
    It enables you to analyze the test coverage by loading an instrumentation
    database (a .csmes file), which was generated by Coco CoverageScanner.
    It is currently supported only on Windows, with Coco version 6.0,
    or later.

    To use the plugin, you must download and install Coco.

    \section1 Enabling the Coco Plugin

    To enable the Coco plugin:

    \list 1
        \li Select \uicontrol Help > \uicontrol {About Plugins} >
            \uicontrol Utilities > \uicontrol Coco to enable the plugin.
        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.
    \endlist

    \section1 Configuring Coco

    \list 1
        \li Select \uicontrol Analyze > \uicontrol {Squish Coco}.
            \image qtcreator-coco.png "Coco CoverageBrowser and CSMes file"
        \li In \uicontrol {CoverageBrowser}, enter the path to the Coco
            coverage browser to use for analyzing a .csmes file.
        \li In \uicontrol CSMes, select the instrumentation database to load.
        \li Select \uicontrol Open to start CoverageBrowser.
        \li In CoverageBrowser, select \uicontrol File >
            \uicontrol {Load Execution Report} and select the .csexe for the
            coverage scan.
            \image coco-coveragebrowser-load-execution-report.png "Load Execution Report dialog"
        \li If you want to reuse the execution report, deselect the
            \uicontrol {Delete execution report after loading} check box.
    \endlist

    Open the analyzed files in \QC. The results of the analysis are displayed
    after the code in \uicontrol Edit mode. You can change the fonts and colors
    used for different types of results.

    \section1 Changing Fonts and Colors

    To change the default fonts and colors, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol {Font & Colors}.
    Create your own color scheme and select new fonts and colors for the
    following results:

    \list
        \li Code Coverage Added Code
        \li Partially Covered Code
        \li Uncovered Code
        \li Fully Covered Code
        \li Manually Validated Code
        \li Code Coverage Dead Code
        \li Code Coverage Execution Count too Low
        \li Implicitly Not Covered Code
        \li Implicitly Covered Code
        \li Implicit Manual Coverage Validation
    \endlist

    For more information, see \l{Specifying Text Editor Settings}.
*/
