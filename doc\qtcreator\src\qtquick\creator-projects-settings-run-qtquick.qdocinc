// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [run settings qt quick ui]

    \section1 Specifying Run Settings for Qt Quick UI Projects

    You can specify run settings for \l{glossary-buildandrun-kit}{kits}
    with \uicontrol Desktop device type:

    \list

        \li In the \uicontrol {QML Viewer} field, specify the \QQV to use.

        \li In the \uicontrol {Command line arguments} field, specify arguments
            to be passed to the executable.

        \li In the \uicontrol {Main QML file}, select the file that \QQV will be
            started with.

    \endlist

    \image qmldesigner-run-settings.png "Run settings for Qt Quick UI projects"

//! [run settings qt quick ui]
*/
