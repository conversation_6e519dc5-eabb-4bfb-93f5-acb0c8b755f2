// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-coding-navigating.html
    \page creator-highlighting.html
    \nextpage creator-checking-code-syntax.html

    \title Semantic Highlighting

    \QC understands the C++, QML, and JavaScript languages as code, not as plain
    text. It reads the source code, analyzes it, and highlights it based on the
    semantic checks that it does for the following code elements:

    \list
        \li Types (such as classes, structs, and type definitions)
        \li Local variables
        \li Class fields
        \li Virtual functions
    \endlist

    To specify the color scheme to use for semantic highlighting, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol {Fonts & Color}.

    \QC supports syntax highlighting also for other types of files than C++,
    QML, or JavaScript.

    \section1 Generic Highlighting

    Generic highlighting is provided by
    \l{https://api.kde.org/frameworks/syntax-highlighting/html/index.html}
    {KSyntaxHighlighting}, which is the syntax highlighting engine for Kate
    syntax definitions. \QC comes with most of the commonly used syntax files,
    and you can download additional files. For more information about the
    definition files, see
    \l{https://docs.kde.org/trunk5/en/kate/katepart/highlight.html}
    {Working with Syntax Highlighting}.

    Font attributes that a syntax definition file explicitly specifies, such as
    bold, italic, underline, or strike through, are applied. Colors are applied
    if they are readable with \QC themes.

    If more than one highlight definition is available for the file that you
    open for editing, the editor asks you to select the one to use. To save
    the selection, select \uicontrol {Remember My Choice}. To reset the
    remembered definitions, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Text Editor} > \uicontrol {Generic Highlighter} >
    \uicontrol {Reset Remembered Definitions}.

    If the editor cannot find the highlight definition for a file that you open
    for editing, it prompts you to download additional highlight definition
    files. Select \uicontrol {Download Definitions} to download the files.
    Information about the downloaded files is displayed in \l{Viewing Output}
    {General Messages}.

    To suppress the message for a particular file pattern, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor}
    > \uicontrol {Generic Highlighter} and add the pattern to the
    \uicontrol {Ignored file patterns} field.

    \image qtcreator-syntax-highlighter.png "Generic Highlighter preferences"

    If you have written your own syntax definition files, you
    can provide an additional definition search path in the
    \uicontrol {User Highlight Definition Files} field. To
    apply the changes you make to the definition files, select
    \uicontrol {Reload Definitions}.

    \section1 Highlighting and Folding Blocks

    Use block highlighting to visually separate parts of the code that belong
    together. For example, when you place the cursor within the braces, the code
    enclosed in braces is highlighted.

    \image qtcreator-blockhighlighting.png

    To enable block highlighting, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol Display >
    \uicontrol {Highlight blocks}.

    Use the folding markers to collapse and expand blocks of code within braces.
    Click the folding marker to collapse or expand a block. In the figure above,
    the folding markers are located between the line number and the text pane.

    To show the folding markers, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Text Editor} > \uicontrol Display >
    \uicontrol {Display folding markers}. This option is enabled by default.

    \image qtcreator-options-text-editor-display.png "Text Editor Display preferences"

    When the cursor is on a brace, the matching brace is animated by default. To
    turn off the animation and just highlight the block and the braces, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol Display and deselect \uicontrol {Animate matching parentheses}.

    You can use keyboard shortcuts to move within and between blocks. To go to
    block end, press \key {Ctrl+]} and to go to block start, press
    \key {Ctrl+[}. To also select the lines from the cursor position to the end
    or beginning of the block, press \key {Ctrl+Shift+]} and
    \key {Ctrl+Shift+[}, respectively.

    To select the current block, press \key Ctrl+U. A second key press extends
    the selection to the parent block. To undo the last selection, press
    \key {Ctrl+Alt+Shift+U}. To enable smart block selection, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Text Editor} >
    \uicontrol Behavior > \uicontrol {Enable smart selection changing}.

    \image qtcreator-options-text-editor-behavior.png "Text Editor Behavior preferences"
*/
