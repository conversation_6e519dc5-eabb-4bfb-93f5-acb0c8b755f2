// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [scxml state machines]

    \section1 Using SCXML State Machines

    To use QML together with an SCXML state machine, add states and
    bind them to the state machine in \l {Connection View} >
    \uicontrol Backends, as described in \l {Managing C++ Backend Objects}.

    In the \uicontrol States view, you can select \uicontrol Actions >
    \uicontrol {Set when Condition} to edit the \c when condition of states
    to map QML states to the states of the SCXML state machine. For an example,
    see \l {Qt SCXML Traffic Light QML Example (Dynamic)}.

    \image qmldesigner-states-when-condition.png

    If you add animation to the states, you can
    \l{Validating with Target Hardware}{preview}
    or \l{Running on Multiple Platforms}{run}
    the application to test the animation.

//! [scxml state machines]
*/
