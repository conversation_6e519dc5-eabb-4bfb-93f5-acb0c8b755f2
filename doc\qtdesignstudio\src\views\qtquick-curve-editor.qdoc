// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-curve-editor.html
    \previouspage qtquick-timeline-view.html
    \nextpage qtquick-text-editor.html

    \title Curves

    The \uicontrol {Curves} view shows the interpolated values of an animated
    property over the animation range.

    \image studio-curve-editor.png "Curves"

    When you edit an animation curve, you implicitly edit the
    \l{Editing Easing Curves}{easing curves} that the underlying system uses
    to define an animation. The animation curve is an extension to visualize
    both the value of a keyframe and the interpolation between keyframes
    simultaneously.

    You can use the toolbar buttons to add \uicontrol Linear, \uicontrol Step,
    or \uicontrol Spline interpolation between two keyframes.

    When you set interpolation to \uicontrol Spline, handles appear in
    \uicontrol {Curves} that you can use to modify the curve. Select
    \uicontrol Unify to lock the handle on the left of a keyframe to the one
    on the right of it so that moving the left handle also moves the right
    handle.

    If the component that contains the animated property has been locked
    in \l Navigator, you can select \inlineimage icons/lockon.png
    to unlock it. You can also lock individual easing curves for editing.

    To lock an animation curve, hover the mouse over the keyframe in the list,
    and then select \inlineimage icons/lockoff.png
    .

    To pin an animation curve, hover the mouse over the keyframe in the list,
    and then select \inlineimage icons/pin.png
    .

    \section1 Curves Toolbar

    The \uicontrol {Curves} toolbar contains the following buttons and
    fields.

    \table
    \header
        \li Button/Field
        \li Action
    \row
        \li \inlineimage icons/easing-curve-linear-icon.png
        \li \uicontrol Linear specifies that the interpolation between
            keyframes is linear.
    \row
        \li \inlineimage icons/easing-curve-step-icon.png
        \li \uicontrol Step uses steps for interpolation between keyframes.
    \row
        \li \inlineimage icons/easing-curve-spline-icon.png
        \li \uicontrol Spline uses bezier spline curves for interpolation
            between keyframes and displays handles for managing them.
    \row
        \li \uicontrol {Set Default}
        \li Currently not used.
    \row
        \li \uicontrol Unify
        \li For \uicontrol Spline curves, locks the handle on the left of a
            keyframe to the one on the right.
    \row
        \li Start Frame
        \li Specifies the first frame of the curve.
    \row
        \li End Frame
        \li Specifies the last frame of the curve.
    \row
        \li Current Frame
        \li Displays the frame that the playhead is currently on. Enter a
            number in the field to move the playhead to the respective frame.
    \endtable

    \section1 Editing Animation Curves

    To edit animation curves:

    \list 1
        \li In the \l Timeline view, animate at least one property value by
            \l{Managing Keyframes}{inserting keyframes} for it.
        \li Select \uicontrol View > \uicontrol Views >
            \uicontrol {Curves} to open the animation curve editor.
        \li Right-click in \uicontrol {Curves}, and select
            \uicontrol {Insert Keyframe} to add a keyframe.
        \li Select keyframes to display the easing curves attached to them.
            To select multiple keyframes, press and hold \key Ctrl.
    \endlist

    Your changes are automatically saved when you close the view.

    \section1 Deleting Keyframes in Curves

    To delete the selected keyframe, select \uicontrol {Delete All Keyframes}
    in the context menu.
*/
