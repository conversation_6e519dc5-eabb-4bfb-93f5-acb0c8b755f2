// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-project-meson.html
    \page creator-project-incredibuild.html
    \nextpage creator-project-conan.html

    \title Setting Up IncrediBuild

    \l{https://www.incredibuild.com/}{IncrediBuild} accelerates process
    execution and thus shortens the time you spend on building C++ code.
    In addition, you can view the build progress in the graphical Build
    Monitor.

    The IncrediBuild plugin is delivered with \QC. For more information, see
    \l{https://incredibuild.atlassian.net/wiki/spaces/IUM/pages/19202836/IncrediBuild+for+Qt+Creator}
    {IncrediBuild for Qt Creator}.

    To use IncrediBuild, install IncrediBuild Agent on the development
    host. Then specify IncrediBuild build steps for your project. For
    more information, see \l{IncrediBuild Build Steps}.
*/
