// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

//! [baremetal-common]

    \li In the \uicontrol Name field, enter a name for the connection.
    \li In the \uicontrol {Startup mode} field, select the mode to start
        the debug server provider in.
    \li In the \uicontrol Host field, select the host name and port number
        to connect to the debug server provider.
    \li In the \uicontrol {Executable file} field, enter the path to the
        debug server provider executable.

//! [baremetal-common]


//! [baremetal-init-reset]

    \li In the \uicontrol {Init commands} field, enter the commands
        to  execute when initializing the connection.
    \li In the \uicontrol {Reset commands} field, enter the commands
        to  execute when resetting the connection.
    \li Select \uicontrol Apply to add the debug server provider.

//! [baremetal-init-reset]


//! [uvision-common]

    \li In the \uicontrol Name field, enter a name for the connection.
    \li In the \uicontrol Host field, select the host name and port
        number to connect to the debug server provider.
    \li In the \uicontrol {Tools file path} field, enter the path to
        the Keil toolset configuration file.
    \li In the \uicontrol {Target device} field, select the device to
        debug.
    \li In the \uicontrol {Target driver} field, select the driver for
        connecting to the target device.

//! [uvision-common]
