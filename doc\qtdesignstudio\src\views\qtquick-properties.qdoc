// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-properties.html
    \previouspage quick-scalable-image.html
    \nextpage qtquick-positioning.html

    \title Specifying Component Properties

    The \l Properties view displays all the properties of the selected
    \l{glossary-component}{component}.

    \target basic-item
    \section1 Basic Component Properties

    All components share a set of properties that you can specify in the
    \uicontrol Properties view.

    \image qtquick-item-properties-common.png "Basic component properties"

    \section2 Type

    When you create an instance of a \l{Component Types}{preset component},
    it has all the properties of the preset you used. If you realize later that
    another preset component with another set of default properties would be
    more suitable for your purposes, you can change the component type by
    double-clicking the \uicontrol Type field and entering the name of
    another preset component in the field.

    If you have specified values for properties that are not supported by the
    new component type, \QDS offers to remove them for you. If you'd rather do
    this yourself, you can select the \inlineimage icons/action-icon.png
    (\uicontrol Actions) menu next to the property name, and then select
    \uicontrol Reset to remove the property values before trying again.

    \section2 ID

    Each component and each instance of a component has an \e ID that uniquely
    identifies it and enables other components' properties to be bound to it.
    You can specify IDs for components in the \uicontrol ID field.

    An ID must be unique, it must begin with a lower-case letter or an
    underscore character, and it can contain only letters, numbers, and
    underscore characters.

    For more technical information about IDs, see \l{The id Attribute}.

    To add a \l{Adding Property Aliases}{property alias} that you can use
    from  outside of the component, select \inlineimage icons/alias.png
    . You can use a menu item in the \uicontrol Actions menu to add property
    aliases for property values of child components.

    \section2 Name

    The value of the \uicontrol {Name} field specifies the component name
    used in an \l{Annotating Designs}{annotation}. It is a free-form text
    that descibes the component.

    \section1 2D Geometry

    Set the properties in the \uicontrol {Geometry - 2D} section to determine
    the position and size of a component.

    \image qtquick-properties-2D-geometry.png "2D Geometry properties"

    In the \uicontrol Position group, you can set the position of a component on
    the x and y axis. The position of a component in the UI can be either
    absolute or relative to other components. For more information, see
    \l{Scalable Layouts}.

    In the 2D space, the z position of a component determines its position in
    relation to its sibling components in the component hierarchy. You can set
    the z position in the \uicontrol {Z stack} field.

    In the \uicontrol Size group, you can set the width and height of a
    component. You can also use the resize cursor to \l{Resizing 2D Components}
    {resize 2D components} in the \uicontrol {2D} view or the scaling gizmo
    to \l{Scaling Components}{scale 3D components} in the \uicontrol{3D} view.
    The values in the \uicontrol X and \uicontrol Y fields change accordingly.

    The component size and position can also be managed automatically
    when \l{Using Layouts}{using layouts}.

    The width and height of the root component in a component file determine
    the size of a component. The component size might also be zero (0,0)
    if its final size is determined by property bindings. For more
    information, see \l {Previewing Component Size}.

    \section2 Resetting Component Position and Size

    To return a component to its default position after moving it,
    select the \inlineimage icons/qtcreator-reset-position-icon.png
    (\uicontrol {Reset Position}) button on the \l{Design Views}
    {Design mode toolbar}. To return it to its default size, select
    \inlineimage icons/qtcreator-reset-size-icon.png
    (\uicontrol {Reset Size}) button.

    \section2 Managing 2D Transformations

    You can assign any number of transformations, such as rotation and scaling,
    to a component. Each transformation is applied in order, one at a time.

    In the \uicontrol Origin field, select the origin point for scaling and
    rotation.

    Set the scale factor in the \uicontrol Scale field. A value of less than
    1.0 makes the component smaller, whereas a value greater than 1.0 makes
    it larger. A negative value causes the component to be mirrored in
    the \uicontrol {2D} view.

    In the \uicontrol Rotation field, specify the rotation of the component
    in degrees clockwise around the origin point.

    Alternatively, you can move, resize, or rotate components by dragging them
    in the \l {2D} view.

    For more information about transforming 3D components, see
    \l{Managing 3D Transformations} and \l{3D}{the 3D view}.

    \section1 Visibility

    Set the properties in the \uicontrol Visibility section to show and
    hide components.

    \image qtquick-properties-visibility.png "Visibility properties"

    Deselect the \uicontrol Visible check box to hide a component and all
    its child components, unless they have explicitly been set to be visible.
    This might have surprise effects when using property bindings. In such
    cases, it may be better to use the \uicontrol Opacity property instead.

    If this property is disabled, the component will no longer receive
    \l{Mouse Area}{mouse events}. However, it will continue to receive key
    events and will retain the keyboard focus events if the \uicontrol Enabled
    check box in the \uicontrol Advanced section is selected.

    The visibility value is only affected by changes to this property or the
    parent's visible property. It does not change, for example, if this
    component moves off-screen, or if the opacity changes to 0.

    In the \uicontrol Opacity field, specify the opacity of a component as a
    number between 0.0 (fully transparent) and 1.0 (fully opaque). The specified
    opacity is also applied individually to child components, sometimes with
    surprising effects.

    Changing a component's opacity does not affect whether the component
    receives user input events.

    You can \l{Creating Timeline Animations}{animate} the opacity value to make a
    component fade in and out.

    If the \uicontrol Clip check box is selected, the component and its children
    are clipped to the bounding rectangle of the component.

    in the \uicontrol State field, select the \l{Working with States}{state} to
    change the value of a property in that state.

    \section1 Picking Colors

    You can define color properties for some of the components in the
    \uicontrol Properties view by using the color picker. Open the color picker
    by clicking, for example, the color field of the \uicontrol {Fill color} or
    \uicontrol {Border color} property.

    \image qtquick-designer-color-picker.png "Color Picker view"

    To select a new color, click the color of your choice in the color selector
    (1), or click the \uicontrol {Eye Dropper} icon (2) to be able to select any
    color visible in your screen to use further in your project.

    The \uicontrol Original field displays the original color of the component,
    while the \uicontrol New field displays the current color.

    Make the color fully transparent by clicking the \uicontrol Transparent icon
    (3).

    To use preset \l {Selecting Web Gradients}{web gradients}, click the
    \uicontrol {Gradient Picker} icon (4).

    Use the dropdown menu (5) to determine the color fill type you wish to use.
    You can choose a solid or a gradient color. Available gradient types vary
    between components. The items listed in light grey are not available for the
    selected component.

    The current color bar (6) shows gradient and gradient stops when a gradient
    is selected.

    Use the hue slider (7) or the alpha slider (8) to further define a new
    color.

    Click the \uicontrol X icon to close the color picker.

    \section2 Picking Gradients

    A gradient is defined by two or more colors which are blended
    seamlessly. The colors are specified as a set of gradient stops,
    each of which defines a position on the gradient bar from 0.0 to 1.0
    and a color. Drag the gradient stops along the gradient bar to set their
    values. Select the arrow below a gradient stop to see its value as
    a number.

    To add gradient stops, move the cursor over the gradient bar and point at
    it with the finger-shaped cursor. To remove gradient stops, pull them away
    from the gradient line.

    Set the direction of the gradient by selecting \uicontrol Horizontal
    or \uicontrol Vertical in the \uicontrol {Gradient Controls} section of the
    color picker.

    \image qtquick-designer-gradient-stops.gif

    Calculating gradients can be computationally expensive compared to the
    use of solid color fills or images. Consider using gradients only for
    static components in a UI.

    \if defined(qtdesignstudio)
    \section2 Setting Gradient Properties

    \image qtquick-designer-gradient-types.png "Available gradient types"

    You can select \uicontrol Linear (1), \uicontrol Radial (2), or
    \uicontrol Conical (3) as the color fill type. After selecting one of the
    gradient types, you can define the gradient properties for
    \l{Shapes}{Qt Quick Studio Components} in the \uicontrol {Gradient Controls}
    section of the color picker.

    \section3 Linear Gradients

    A \e {linear gradient} interpolates colors between start and end points.
    Outside these points, the gradient is either padded, reflected, or repeated
    depending on the spread type. Set start and end points for horizontal and
    vertical interpolation in the \uicontrol X1, \uicontrol X2, \uicontrol Y1,
    and \uicontrol Y2 fields.

    \image qtquick-designer-gradient-properties-linear.png "Linear gradient controls"

    \section3 Radial Gradients

    A \e {radial gradient} interpolates colors between a focal circle and a
    center circle. Points outside the cone defined by the two circles will
    be transparent. Outside the end points, the gradient is either padded,
    reflected, or repeated depending on the spread type.

    You can set the center and focal radius in the \uicontrol {Center radius}
    and \uicontrol {Focal radius} fields. For simple radial gradients, set
    \uicontrol {Focal radius} to 0.

    You can set the center and focal points in the \uicontrol CenterX,
    \uicontrol CenterY, \uicontrol FocalX, and \uicontrol FocalY fields.
    To specify a simple radial gradient, set the \uicontrol FocalX and
    \uicontrol FocalY to the value of \uicontrol CenterX and \uicontrol CenterY,
    respectively.

    \image qtquick-designer-gradient-properties-radial.png "Radial gradient properties"

    \section3 Conical Gradients

    A \e {conical gradient} interpolates colors counter-clockwise around a
    center point. Set the horizontal and vertical center point of the gradient
    in the \uicontrol CenterX and \uicontrol CenterY fields and the start angle
    in the \uicontrol Angle field.

    \image qtquick-designer-gradient-properties-conical.png "Conical gradient properties"
    \endif

    \section2 Selecting Web Gradients

    The \uicontrol {Gradient Picker} enables you to specify
    \l{https://webgradients.com/}{WebGradients} for components
    that support \l QGradient.

    To open the \uicontrol {Gradient Picker}, select the
    \uicontrol {Gradient Picker Dialog} icon (4).

    \image qtquick-designer-gradient-picker.png "Gradient Picker dialog"

    To apply a gradient on the selected component, select \uicontrol Apply.

    To save a gradient in the \uicontrol {User Presets} tab, select
    \uicontrol Save.

    By default, a linear gradient is used, but you can select another
    supported gradient type in the dropdown menu (5) of the color picker.

    \section2 Color Details

    Further define the colors in your project by modifying the properties in the
    \uicontrol {Color Details} section of the color picker.

    \image qtquick-designer-color-details.png "Color Details tab"

    Use the \uicontrol Hex property to enter the Hex value of a new color
    manually. Define exact values for red, green, and blue using the
    \uicontrol Hex property to create different shades for the colors used in
    the project.

    The default color value mode is HSVA (hue-saturation-value-alpha). Use the
    dropdown menu to change the color value mode to RGBA (red-green-blue-alpha)
    or HSLA (hue-saturation-lightness-alpha). \e Hue is defined in degrees which
    refer to different colors of the color wheel. \e Saturation modifies the
    intensity of the color. \e Value determines the brightness of the color. In
    HSLA \e Lightness signifies the amount of white or black blended with the
    color.

    \section2 Palette

    \image qtquick-designer-color-palette.png "Color Palette tab"

    Use the dropdown menu in the \uicontrol Palette section of the
    color picker to change the \uicontrol Palette type. \uicontrol Recent
    displays the recent colors used in the project, while \uicontrol Favorites
    shows the colors you have added to your collection of favorite colors. You
    can add colors to \uicontrol Favorites by right-clicking the color thumbnail
    for \uicontrol Original, \uicontrol New, or in \uicontrol Palette >
    \uicontrol Recent colors and selecting \uicontrol {Add to Favorites}.

    Click one of the \uicontrol Palette color thumbnails to select it as
    the new color.

    \section1 Specifying Developer Properties

    In the \uicontrol Advanced and \uicontrol Layer sections of the
    \uicontrol Properties view, you can manage the more advanced
    properties of components that are inherited from the \l Item
    component and that are mostly used by application developers.

    \image qtquick-properties-advanced.png "Advanced section in Properties"

    Select the \uicontrol Smooth check box to activate smooth sampling. Smooth
    sampling is performed using linear interpolation, while non-smooth sampling
    is performed using the nearest neighbor. Because smooth sampling has minimal
    impact on performance, it is activated by default.

    Antialiasing is used to make curved lines smoother on the screen. Select
    the \uicontrol Antialising check box to turn on antialiasing.

    The value of the \uicontrol {Baseline offset} specifies the position of the
    component's baseline in local coordinates. The baseline of a \l Text
    component is the imaginary line on which the text sits. Controls containing
    text usually set their baseline to the baseline of their text. For non-text
    components, a default baseline offset of 0 is used.

    \section2 Managing Mouse and Keyboard Events

    Select the \uicontrol Enabled check box to allow the component to receive
    mouse and keyboard events. The children of the component inherit this
    behavior, unless you explicitly set this value for them.

    You can enable the \uicontrol Focus check box to specify that the component
    has active focus and the \uicontrol {Focus on tab} check box to add
    the component to the \e {tab focus chain}. The tab focus chain traverses
    components by first visiting the parent, and then its children in the order
    they are defined. Pressing \key Tab on a component in the tab focus chain
    moves keyboard focus to the next component in the chain. Pressing back tab
    (usually, \key {Shift+Tab}) moves focus to the previous component.

    \section2 Using Layers

    Qt Quick makes use of a dedicated scene graph that is then traversed and
    rendered via a graphics API such as OpenGL ES, OpenGL, Vulkan, Metal, or
    Direct 3D. Using a scene graph for graphics rather than the traditional
    imperative painting systems, means that the scene to be rendered can be
    retained between frames and the complete set of primitives to render is
    known before rendering starts. This opens up for a number of optimizations,
    such as \l{Batching}{batch rendering} to minimize state changes and
    discarding obscured primitives.

    For example, a UI might contain a list of ten items where
    each item has a background color, an icon and a text. Using traditional
    drawing techniques, this would result in 30 draw calls and a similar
    amount of state changes. A scene graph, on the other hand, could reorganize
    the primitives to render such that all backgrounds are drawn in one call,
    then all icons, then all the text, reducing the total amount of draw calls
    to only 3. Batching and state change reduction like this can greatly
    improve performance on some hardware.

    You need a basic understanding of how components are rendered
    to be able to set layer properties. Rendering is described in
    \l {Qt Quick Scene Graph Default Renderer}.

    \image qtquick-item-properties-layer.png "Layer properties"

    Components are normally rendered directly into the window they belong to.
    However, by selecting the \uicontrol Enabled check box in the
    \uicontrol Layer section, you can delegate the component and its entire subtree
    into an offscreen surface. Only the offscreen surface, a texture, will
    then be drawn into the window. For more information, see \l{Item Layers}.

    When layering is enabled, you can use the component directly as a texture,
    in combination with the component you select in the \uicontrol Effect field.
    Typically, this component should be a shader effect with a source texture
    specified. You can use the effects in \uicontrol Components >
    \uicontrol {Qt Quick Studio Effects} that are based on the components in the
    \l {https://doc.qt.io/qt-5/qtgraphicaleffects-index.html}
    {Qt Graphical Effects} module.

    To enable the component to pass the layer's offscreen surface to the effect
    correctly, the \uicontrol {Sampler name} field is set to the source
    property of the texture.

    Note that when a component's layer is enabled, the scene graph will allocate
    memory in the GPU equal to width x height x 4. In memory constrained
    configurations, large layers should be used with care. Also, a component
    using a layer cannot be batched during rendering. This means that a
    scene with many layered components may have performance problems.

    By default, multisampling is enabled for the entire window if the scenegraph
    renderer is in use and the underlying graphics API supports it. By setting
    the value in the \uicontrol Samples field, you can request multisampling for
    a part of the scene. This way, multisampling is applied only to a particular
    subtree, which can lead to significant performance gain. Even then, enabling
    multisampling can be potentially expensive regardless of the layer's size,
    as it incurs a hardware and driver dependent performance and memory cost. If
    support for multisample renderbuffers and framebuffer blits is not
    available, the value is silently ignored.

    The value of the \uicontrol Format field specifies the internal OpenGL
    format of the texture. Depending on the OpenGL implementation, it might
    allow you to save some texture memory. However, use the \uicontrol RGB
    and \uicontrol Alpha values with caution because the underlying hardware
    and driver might not support them.

    In the \uicontrol {Texture mirroring} field, specify whether the generated
    OpenGL texture should be mirrored by flipping it along the x or y axis.
    Custom mirroring can be useful if the generated texture is directly accessed
    by custom shaders. If no effect is specified for the layered component,
    mirroring has no effect on the UI representation of the component.

    The component will use linear interpolation for scaling if the
    \uicontrol Smooth check box is selected. To use a mipmap for downsampling,
    select the \uicontrol Mipmap check box. Mipmapping may improve the visual
    quality of downscaled components. For mipmapping of single \uicontrol Image
    components, select the \uicontrol Mipmap check box in the \l{Images}
    {image properties}, instead.

    To use a texture with a size different from that of the component, specify
    the width and height of the texture in the \uicontrol {Texture size} field.

    The \uicontrol {Wrap mode} defines the OpenGL wrap modes associated with
    the texture. You can clamp the texture to edges or repeat it horizontally
    and vertically. Note that some OpenGL ES 2 implementations do not support
    the \uicontrol Repeat wrap mode with non-power-of-two textures.

    \section1 Copying and Pasting Formatting

    You can copy property values from a component and paste them to one or
    several other components. The values are applied if the target components
    have those particular properties.

    To copy property values from the selected component, select
    \inlineimage icons/copy-formatting.png
    on the \uicontrol Design mode \l{Summary of Main Toolbar Actions}
    {main toolbar}.

    To apply the values to one or several other components, select
    them in the \l Navigator or \l {2D} view, and then select
    \inlineimage icons/paste-formatting.png
    .

    \section1 Editing Properties Inline

    You can double-click components in the \l {2D} view to edit their
    text, color, or source properties inline. Because you can specify several
    of these properties for some components, such as \l {text-edit}{Text Edit},
    you can also right-click components to open the inline editors from the
    context menu.

    \image qmldesigner-inline-editing.png
*/
