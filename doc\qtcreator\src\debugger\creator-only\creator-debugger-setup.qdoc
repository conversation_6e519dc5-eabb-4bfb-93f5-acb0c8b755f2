// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************


/*!
    \previouspage creator-debugging.html
    \page creator-debugger-engines.html
    \nextpage creator-debugger-operating-modes.html

    \title Setting Up Debugger

    The main debugger settings are associated with the
    \l{glossary-buildandrun-kit}{kit} you build and run your project with. To
    specify the debugger and compiler to use for each kit, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits.

    You need to set up the debugger only if the automatic setup fails because
    the native debugger is missing (as is usually the case for the CDB debugger
    on Windows, which you always must install yourself) or because the installed
    version is not supported (for example, when your system contains no, or an
    outdated version of GDB and you want to use a locally installed replacement
    instead).

    \note If you need to change the debugger to use for an automatically
    detected \l{glossary-buildandrun-kit}{kit}, you can \uicontrol Clone the
    kit and change the parameters in the clone. Make sure to select the cloned
    kit for your project.

    If the debugger you want to use is not automatically detected, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
    \uicontrol Debuggers > \uicontrol Add to add it.

    \note To use the debugging tools for Windows, you must install them and add
    the Symbol Server provided by Microsoft to the symbol search path of the
    debugger. For more information, see \l{Setting CDB Paths on Windows}.

    \note To use the Free Software Foundation (FSF) version of GDB on \macos, you
    must sign it and modify your \l{glossary-buildandrun-kit}{kit} settings.

    This section explains the options you have for debugging C++ code and
    provides installation notes for the supported native debuggers. It also
    applies for code in other compiled languages such as C, FORTRAN, Ada.

    For more information on the debugger modes, see
    \l{Launching the Debugger in Different Modes}.

    \section1 Supported Native Debugger Versions

    \QC supports native debuggers when working with compiled code. On
    most supported platforms, the GNU Symbolic Debugger GDB can be used. On
    Microsoft Windows, when using the Microsoft tool chain, the Microsoft
    Console Debugger CDB is needed. On \macos and Linux, the LLDB debugger
    can be used.

    The following table summarizes the support for debugging C++ code:

    \table
        \header
            \li Platform
            \li Compiler
            \li Native Debugger
        \row
            \li Linux
            \li GCC, ICC
            \li GDB, LLDB
        \row
            \li Unix
            \li GCC, ICC
            \li GDB
        \row
            \li \macos
            \li GCC, Clang
            \li LLDB, FSF GDB (experimental)
        \row
            \li Windows/\MinGW
            \li GCC
            \li GDB
        \row
            \li Windows/MSVC
            \li Microsoft Visual C++ Compiler
            \li Debugging Tools for Windows/CDB
    \endtable

    \section2 Supported GDB Versions

    Starting with version 3.1, \QC requires the Python scripting extension. GDB
    builds without Python scripting are not supported anymore and will not work.
    The minimum supported version is GDB 7.5 using Python version 2.7, or 3.3,
    or newer.

    For remote debugging using GDB and GDB server, the minimum supported version
    of GDB server on the target \l{glossary-device}{device} is 7.0.

    \section2 Supported CDB Versions

    All versions of CDB targeting platforms supported by Qt are supported by
    \QC.

    \section2 Supported LLDB Versions

    The LLDB native debugger has similar functionality to the GDB debugger. LLDB
    is the default debugger in Xcode on \macos for supporting C++ on the desktop.
    LLDB is typically used with the Clang compiler (even though you can use it
    with GCC, too).

    On \macos you can use the LLDB version delivered with Xcode or build from source.
    The minimum supported version is LLDB 320.4.

    On Linux, the minimum supported version is LLDB 3.8.

    \omit

    \section2 GDB Adapter Modes

    [Advanced Topic]

    The GDB native debugger used internally by the debugger plugin runs in
    different adapter modes to cope with the variety of supported platforms and
    environments. All GDB adapters inherit from  AbstractGdbAdapter:

    \list

        \li PlainGdbAdapter debugs locally started GUI processes. It is
            physically split into parts that are relevant only when Python is
            available, parts relevant only when Python is not available, and
            mixed code.

        \li TermGdbAdapter debugs locally started processes that need a console.

        \li AttachGdbAdapter debugs local processes started outside \QC.

        \li CoreGdbAdapter debugs core files generated from crashes.

        \li RemoteGdbAdapter interacts with the GDB server running on Linux.

    \endlist

    \endomit

    \section1 Installing Native Debuggers

    The following sections provide information about installing native
    debuggers.

    \section2 GDB

    On Windows, use the Python-enabled GDB version that is bundled
    with the Qt package or comes with recent versions of \MinGW. On
    most Linux distributions, the GDB builds shipped with the system
    are sufficient.

    You can also build your own GDB, as instructed in
    \l{http://wiki.qt.io/QtCreator_Build_Gdb}{Building GDB}.

    Builds of GDB shipped with Xcode on \macos are no longer supported.

    \section2 Debugging Tools for Windows

    To use the CDB debugger, install the \e {Debugging Tools for Windows} when
    you install \QC either by using the Qt Online Installer (in \uicontrol Qt
    > \uicontrol Tools > \uicontrol {\QC}) or by using the stand-alone \QC
    installation packages.

    The 32-bit CDB version can only debug 32-bit executables, whereas the 64-bit
    version can debug both 64-bit and 32-bit executables. However, interrupting a
    32-bit executable with a 64-bit debugger can result in a stack trace of the
    WOW64 emulator 32-bit emulation layer being displayed.

    \QC extends the command line debugger by loading the
    \c qtcreatorcdbext.dll extension library into it. The
    library must be available in the \c {libs\qtcreatorcdbext64}
    and \c {libs\qtcreatorcdbext32} folder. To install it there,
    select \uicontrol {\QC CDB Debugger Support} when you install \QC.

    When manually building \QC using
    the Microsoft Visual C++ Compiler, the build process checks for
    the required files in
    \c{"%ProgramFiles%\Debugging Tools for Windows"}.

    \section3 Symbol Server

    It is highly recommended that you add the Symbol Server provided
    by Microsoft to the symbol search path of the debugger. The
    Symbol Server provides you with debugging information for the
    operating system libraries for debugging Windows applications.
    For more information, see
    \l{Setting CDB Paths on Windows}.

    \section2 Debugging Tools for \macos

    The Qt binary distribution contains both debug and release
    variants of the libraries. But you have to explicitly tell the
    runtime linker that you want to use the debug libraries even if
    your application is compiled as debug, as release is the default
    library.

    If you use a qmake based project in \QC,  you can set a flag in
    your \l{glossary-run-config}{run configuration}, in
    \uicontrol Projects mode. In the run configuration, select
    \uicontrol{Use debug version of frameworks}.

    For more detailed information about debugging on \macos,
    see: \l{http://developer.apple.com/library/mac/#technotes/tn2124/_index.html#//apple_ref/doc/uid/DTS10003391}
    {Mac OS X Debugging Magic}.

    \section2 LLDB

    We recommend using the LLDB version that is delivered with the latest Xcode.

    \section1 Setting up FSF GDB for \macos

    To use FSF GDB on \macos, you must sign it and add it to the \QC
    \l{glossary-buildandrun-kit}{kits}.

    \list 1

        \li To create a key for signing FSF GDB, select
            \uicontrol {Keychain Access} > \uicontrol {Certificate Assistant} >
            \uicontrol {Create a Certificate}:

        \list 1

            \li In the \uicontrol Name field, input \uicontrol fsfgdb to
                replace the existing content.

            \li In the \uicontrol {Certificate Type} field, select
                \uicontrol {Code Signing}.

            \li Select the \uicontrol {Let me override defaults} check box.

            \li Select \uicontrol Continue, and follow the instructions of the
                wizard (use the default settings), until the
                \uicontrol {Specify a Location For The Certificate} dialog
                opens.

            \li In the \uicontrol Keychain field, select \uicontrol System.

            \li Select \uicontrol {Keychain Access} > \uicontrol System, and
                locate the certificate.

            \li Double click the certificate to view certificate information.

            \li In the \uicontrol Trust section, select
                \uicontrol {Always Trust} in the
                \uicontrol {When using this certificate} field, and then close
                the dialog.

        \endlist

        \li To sign the binary, enter the following command in the terminal:

            \code
            codesign -f -s "fsfgdb" $INSTALL_LOCATION/fsfgdb
            \endcode

        \li In \QC, select \uicontrol {\QC} > \uicontrol Preferences >
            \uicontrol Kits > \uicontrol Add to
            create a kit that uses FSF GDB.

            \li In the \uicontrol Debugger field, specify the path to FSF GDB
                (\c $HOME/gdb72/bin/fsfgdb, but with an explicit value for
                \c $HOME).

        \li To use the debugger, add the kit in the \uicontrol {Build Settings}
            of the project.

    \endlist
*/
