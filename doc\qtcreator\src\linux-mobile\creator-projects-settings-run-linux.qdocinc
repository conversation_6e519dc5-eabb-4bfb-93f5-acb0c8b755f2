// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [run settings linux]

    \section1 Specifying Run Settings for Linux-Based Devices

    To run and debug an application on a Linux-based device,
    you must create connections from the development
    host to the device and add the device configurations to
    \l{glossary-buildandrun-kit}{kits}. Click \uicontrol {Manage Kits} to add devices
    to kits. For more information, see
    \l {Connecting Remote Linux Devices}.

    When you run the application, \QC copies the files to the connected device.

    The run settings display the path to the
    executable file on the development host and on the device. To specify that
    another application launches your application, for example, enter the
    command in the \uicontrol {Alternate executable on device} field and select the
    \uicontrol {Use this command instead} check box.

    \image qtcreator-run-settings-linux.png "Run settings for Linux-based devices"

    You can specify arguments to pass to your application in the
    \uicontrol {Command line arguments} field.

//! [run settings linux]
*/
