// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-opening.html
    \page creator-project-qmake-libraries.html
    \nextpage creator-project-wizards.html

    \title Adding Libraries to Projects

    In addition to Qt libraries, you can add other libraries to your projects.
    The process depends on the type and location of the
    library. You can add a system library, your own library, or a 3rd party
    library. The library can be located either in the build tree of the
    current project or in another build tree.

    \image qtcreator-add-library-wizard.png "Add Library wizard"

    Because system libraries do not typically change and are often found by
    default, you do not need to specify the path to the library or to its
    includes when you add it. You can use \c{pkg-config} to query system
    libraries during compilation.

    For your own libraries and 3rd party libraries, you need to specify
    the paths. \QC tries to guess the include path for an external library,
    but you need to check it and modify it if necessary. \QC automatically
    adds the include path for an internal library.

    For all libraries, select the target platforms for the application, library,
    or plugin.

    Specify whether the library is statically or dynamically linked. For a
    statically linked internal library, \QC adds dependencies
    (\l{CMake: target_link_libraries command}{target_link_libraries} when using
    CMake or \l PRE_TARGETDEPS when using  qmake) in the project file.

    Depending on the development platform, \QC might detect some options
    automatically. For example, on \macos, it detects the library type
    (\uicontrol Library or \uicontrol Framework) automatically and hides the
    option. However, if you develop on another platform than \macos and want
    to build your project for \macos, you must specify the library type.

    The default convention on Windows is that the debug and release versions
    of a library have the same name, but are placed in different subdirectories,
    usually called \e debug and \e release. If the library path does not contain
    either of these folders, you cannot select the option to place the libraries
    in separate folders.

    Alternatively, the letter \e d can be added to the library name for the
    debug version. For example, if the release version is called example.lib,
    the debug version is called exampled.lib. You can specify that the letter
    is added for the debug version and removed for the release version.
    If the library name ends in \e d, deselect the \uicontrol {Remove "d" suffix
    for release version} option.

    \QC supports code completion and syntax highlighting for the added
    libraries once your project successfully builds and links to them.

    \section1 To Add Libraries

    \list 1

        \li In the \uicontrol Projects view, right-click the project name to open the
            context menu and select
            \uicontrol {Add Library}.

        \li Follow the instructions of the wizard.

    \endlist

    For more information about the project file settings, see
    \l{Declaring Other Libraries}.

    \section1 Example of Adding Internal Libraries

    To add an internal library to your project:

    \list 1

        \li Select \uicontrol File > \uicontrol {New Project} >
            \uicontrol Library > \uicontrol {C++ Library}.

        \li Select \uicontrol Choose to open the \uicontrol {Project Location}
            dialog.

            \image qtcreator-add-library-wizard-ex-1.png "Project Location dialog"

        \li In the \uicontrol Name field, give a name for the library. For example,
            \b mylib.

        \li Follow the instructions of the wizard until you get to the
            \uicontrol {Project Management} dialog. In the
            \uicontrol {Add as a subproject to project}
            list, select a project. For example, \b myapp.

        \li In the \uicontrol Projects view, right-click the project name to open the
            context menu and select
            \uicontrol {Add Library} > \uicontrol {Internal Library} >
            \uicontrol Next.

        \li In the \uicontrol Library field, select \b mylib, and then select
            \uicontrol Next.

        \li Select \uicontrol Finish to add the library declaration to the
            project file.

    \endlist

    When using CMake, the \c target_link_libraries command is added to the
    CMakeLists.txt file:

    \badcode
    target_link_libraries(myapp PRIVATE mylib)
    \endcode

    When using qmake, the following library declaration is added to the .pro
    file:

    \badcode
    win32:CONFIG(release, debug|release): LIBS += -L$$OUT_PWD/../../../projects/mylib/release/ -lmylib
    else:win32:CONFIG(debug, debug|release): LIBS += -L$$OUT_PWD/../../../projects/mylib/debug/ -lmylib
    else:unix: LIBS += -L$$OUT_PWD/../../../projects/mylib/ -lmylib
    INCLUDEPATH += $$PWD/../../../projects/mylib
    DEPENDPATH += $$PWD/../../../projects/mylib
    win32:CONFIG(release, debug|release): PRE_TARGETDEPS += $$OUT_PWD/../../../projects/mylib/release/mylib.lib
    else:win32:CONFIG(debug, debug|release): PRE_TARGETDEPS += $$OUT_PWD/../../../projects/mylib/debug/mylib.lib
    else:unix: PRE_TARGETDEPS += $$OUT_PWD/../../../projects/mylib/libmylib.a
    \endcode
*/
