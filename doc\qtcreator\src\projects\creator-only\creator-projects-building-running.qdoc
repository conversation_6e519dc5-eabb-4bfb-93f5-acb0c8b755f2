// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-scxml.html
    \page creator-building-running.html
    \nextpage creator-live-preview.html

    \title Building and Running

    \image front-preview.png

    \QC provides support for running and deploying Qt applications built
    for different target platforms, or using different compilers, debuggers, or
    Qt versions. \l{glossary-buildandrun-kit}{Kits} define the tools,
    \l{glossary-device}{device} type and other settings to use when building and
    running your project.

    \list

        \li \l {Validating with Target Hardware}

            You can use the QML live preview to preview a QML file or an
            entire Qt Quick application on the desktop, as well as on
            Android and embedded Linux devices. The changes you make to
            the UI are instantly visible to you in the preview.

        \li \l{Building for Multiple Platforms}

            \e {Build configurations} contain everything you need to compile
            the sources into binaries. Build configurations use the tools and settings
            defined in their corresponding kit.

        \li \l{Running on Multiple Platforms}

            \e {Run configurations} start the application in the location
            where it was copied by the \e{deploy configuration}. By default,
            when you select the \uicontrol Run function, \QC builds the project,
            deploys it to the device defined in the kit, and runs it there. However,
            if you have not made any changes to the project since you last
            built and deployed it, \QC simply runs it again.

        \li \l{Deploying to Devices}

            \e {Deploy configurations} handle the packaging and copying of
            the necessary files to a location you want to run the executable
            at. The files can be copied to a location in the file system of
            the development PC or a device.

        \li \l{Connecting Devices}

            When you install tool chains for device types as part of a Qt distribution,
            the build and run settings for the devices might be set up
            automatically. However, you might need to install and configure some
            additional software on the devices to be able to connect to them
            from the development PC.

    \endlist

    \section1 Related Topics

    \list

        \li \l{Customizing the Build Process}

            By default, running an application also builds it and deploys it to
            a location from where it can be run on the device. You can change
            the relationship between the build, run, and deploy configurations.

    \endlist

*/
