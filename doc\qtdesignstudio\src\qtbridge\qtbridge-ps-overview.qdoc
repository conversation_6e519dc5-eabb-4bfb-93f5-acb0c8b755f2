// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// Note: The \page value is hard-coded as a link in Qt Bridge for Adobe Photoshop.

/*!
    \previouspage qtbridge-ai.html
    \page psqtbridge.html
    \nextpage qtbridge-ps-setup.html

    \title Exporting Designs from Adobe Photoshop

    You can use \QBPS to export designs from Adobe Photoshop to \e {.metadata}
    format that you can \l{Importing 2D Assets}{import} to projects in \QDS.

    \image studio-ps-export.png

    The following topics describe setting up and using \QBPS:

    \list

        \li \l{Setting Up Qt Bridge for Adobe Photoshop}

            You must install and set up the \QBPS export tool before you can use
            it to export designs.

        \li \l{Using Qt Bridge for Adobe Photoshop}

            To get the best results when you use \QBPS to export designs from
            Photoshop, you should follow the guidelines for working with
            Photoshop and organizing your assets.
    \endlist
*/
