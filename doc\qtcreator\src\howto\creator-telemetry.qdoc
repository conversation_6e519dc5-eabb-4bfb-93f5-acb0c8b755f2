// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-telemetry.html
    \if defined(qtdesignstudio)
    \previouspage creator-quick-ui-forms.html
    \else
    \previouspage creator-logging-viewer.html
    \endif
    \nextpage collecting-usage-statistics.html

    \title Managing Data Collection

    \if defined (qtcreator)
    When you install \QC as a part of Qt installation, you are asked whether
    you allow it to collect pseudonymous information about your system and \QC
    use. If you decline, the plugin is not installed and no analytics data is
    collected.

    If you accept, all collected and transmitted data is fully transparent to
    you. You can change the settings for collecting and transmitting data any
    time. By default, no data is collected and you have to select a telemetry
    mode for data collection to begin.


    See \l {Collecting Usage Statistics} for more information about the data
    transmitted by the telemetry plugin and \l {Specifying Telemetry Settings}
    {specifying telemetry settings}.
    \else
    To enable the use of the telemetry plugin, you need to select \uicontrol
    {Enable Usage Statistics} in the splash screen that appears when you first
    launch \QDS. If the splash screen does not appear, you can enable the
    telemetry plugin by selecting \uicontrol Help > \uicontrol {About Plugins} >
    \uicontrol Utilities > \uicontrol UsageStatistic.
    \image studio-usage-statistics.png "Enabling Usage Statistics"
    \endif

    \if defined(qtdesignstudio)
    See below for more information about the collected data:

    \list
        \li \l {Collecting Usage Statistics}
        \li \l {Collecting User Feedback}
        \li \l {Reporting Crashes}
    \endlist
    \endif

    \section1 Principles of Data Collection

    No personal data, such as names, IP addresses, MAC addresses, or project
    and path names are collected. However, QUuid objects are used to identify
    data records that belong to one user. The objects cannot be converted
    back to the actual values from which they were generated.

    For more information about Qt privacy policy, select
    \l{https://www.qt.io/terms-conditions/#privacy}
    {Legal Notice and Privacy Policy}.
*/

/*!
    \page collecting-usage-statistics.html
    \previouspage creator-telemetry.html
    \if defined(qtdesignstudio)
    \nextpage studio-user-feedback.html
    \else
    \nextpage creator-help-overview.html
    \endif

    \title Collecting Usage Statistics

    The telemetry plugin uses the
    \l{https://api.kde.org/frameworks/kuserfeedback/html/index.html}
    {KUserFeedback} framework to collect the usage data. The library
    has been designed from the user data privacy point of view and
    \QC respects the same privacy rules.

    The data is transmitted to the backend storage using an encrypted
    connection. The storage is located in the same Heroku backend as the
    Qt installer backend. Physically, data is stored in the Amazon cloud.

    \section1 Specifying Telemetry Settings

    To determine what data is transmitted to the backend storage:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Telemetry
            > \uicontrol {Usage Statistics}.
            \image qtcreator-telemetry-settings.png "Telemetry settings"
        \li In the \uicontrol {Telemetry mode} list, select the mode that
            determines what kind of data is collected.
        \li In the \uicontrol {Data sources} list, select entries to view
            exactly what data is collected. Deselect check boxes for data
            that you do not want to transmit to the backend storage.
    \endlist
*/
