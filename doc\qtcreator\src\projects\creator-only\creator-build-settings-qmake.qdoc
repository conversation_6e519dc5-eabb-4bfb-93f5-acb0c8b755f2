// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-build-settings-cmake.html
    \page creator-build-settings-qmake.html
    \nextpage creator-build-settings-qbs.html

    \title qmake Build Configuration

    \image qtcreator-projectpane.png "qmake build settings"

    By default, \QC builds qmake projects in a separate directory from the
    source directory, as \l{glossary-shadow-build} {shadow builds}. This
    keeps the files generated for each \l{glossary-buildandrun-kit}
    {build and run kit} separate. If you only build and run with a single
    \l{glossary-buildandrun-kit}{kit}, you can deselect the
    \uicontrol {Shadow build} checkbox. Select the build directory in the
    \uicontrol {Build Directory} field.

    To make in-source builds the default option for all projects, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Build & Run} >
    \uicontrol {Default Build Properties}, and enter a period (.) in the
    \uicontrol {Default build directory} field.

    In the \uicontrol {Tooltip in target selector} field, you can enter text
    that is displayed as a tooltip when you hover the mouse over the build
    configuration in the \l{Building for Multiple Platforms}{kit selector}.

    You can create separate versions of project files to keep platform-dependent
    code separate. You can use qmake \l{Adding Platform Specific Source Files}
    {scopes} to select the file to process depending on which platform qmake is
    run on.

    If debug info is being generated, you can have it placed into separate
    files, rather than embedded into the binary, by selecting
    \uicontrol Enable in the \uicontrol {Separate debug info} field. For
    more information, see \l{Using the Performance Analyzer}. To use default
    settings, select \uicontrol {Leave at Default}.

    In the \uicontrol {qmake system() behavior when parsing} field, you can
    select whether processes are run via qmake's \c system() function or
    ignored. Setting this option to \uicontrol Ignore might help if opening
    or closing projects takes too long, but it might produce inexact parsing
    results.

    \section1 Global qmake Settings

    To specify settings for all qmake builds, select \uicontrol Edit
    > \uicontrol Preferences > \uicontrol {Build & Run} >
    \uicontrol Qmake.

    \image qtcreator-preferences-build-run-qmake.png "Qmake tab in Build & Run Preferences"

    To set the default build properties, select \uicontrol Edit
    > \uicontrol Preferences > \uicontrol {Build & Run} >
    \uicontrol {Default Build Properties}.

    \image qtcreator-build-settings-default.png "Default Build Properties tab in Build & Run Preferences"

    \section1 Compiling QML

    You can compile QML source code into the final binary to improve the
    startup time of the application and eliminate the need to deploy QML
    files together with the application. For more information, see
    \l{Ahead-of-Time Compilation}.

    \QC project wizard templates create Qt Quick projects that can be compiled
    because they are set up to use the Qt Resource System. To compile QML code,
    select \uicontrol Enable in the \uicontrol {Qt Quick Compiler} field. To
    use default settings, select \uicontrol {Leave at Default}.

    You can specify default behavior for compiling QML code in \uicontrol Edit
    > \uicontrol Preferences > \uicontrol {Build & Run} > \uicontrol Qmake >
    \uicontrol {Use qmlcachegen}.

    \section1 qmake Build Steps

    \QC builds qmake projects by running the \c make or \c nmake command from
    the Qt version defined for the current build configuration.

    \image qtcreator-build-steps.png "Build steps"

    To override the shell command that \QC constructs by default, disable or
    remove the build step and add a custom build step that specifies another
    shell command.

    By default, \QC uses all the CPU cores available to achieve maximum build
    parallelization. On Linux and \macos, you can specify the number of parallel
    jobs to use for building in the \uicontrol {Parallel jobs} field. Select the
    \uicontrol {Override MAKEFLAGS} check box to override existing MAKEFLAGS
    variables.

    Select \uicontrol {Disable in subdirectories} to execute the build step
    only for a top-level build.

    Select \uicontrol {Add Build Step} > \uicontrol {IncrediBuild for Linux} or
    \uicontrol {IncrediBuild for Windows} to accelerate builds by using
    \l{IncrediBuild Build Configuration}{IncrediBuild}.

    Select \uicontrol {Add Build Step} > \uicontrol {Run Conan Install} to use
    the \l{Conan Build Configuration}{Conan} package manager with qmake
*/
