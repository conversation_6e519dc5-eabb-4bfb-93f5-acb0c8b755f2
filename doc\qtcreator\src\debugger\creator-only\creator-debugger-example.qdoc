// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-debugging-qml.html
    \page creator-debugging-example.html
    \nextpage creator-qml-debugging-example.html

    \title Debugging a C++ Example Application

    This section uses the \l{Creating a Qt Widget Based Application}{TextFinder}
    example to illustrate how to debug Qt C++ applications in the
    \uicontrol Debug mode.

    Text<PERSON><PERSON> reads a text file into QString and then displays it with
    QTextEdit. To look at the TextFinder class and see the stored data, place
    a breakpoint in textfinder.cpp, as follows:

    \list 1

        \li Click in between the line number and the window border on the line
            where we change the cursor position to set a breakpoint.

            \image qtcreator-setting-breakpoint1.png

        \li Select \uicontrol Debug > \uicontrol {Start Debugging} >
            \uicontrol {Start Debugging of Startup Project} or press \key F5.

        \li To view information about the breakpoint, go to the
            \uicontrol Breakpoints view.

            \image qtcreator-setting-breakpoint2.png

        \li To remove a breakpoint, right-click it and select
            \uicontrol {Delete Breakpoint}.


        \li To view the base classes and data members of the TextFinder class,
            go to the \uicontrol Locals view.

            \image qtcreator-watcher.png

    \endlist

    Modify the \c{on_findButton_clicked()} function to move back to
    the start of the document and continue searching once the cursor hits the
    end of the document. Add the following code snippet:

    \code
    void TextFinder::on_findButton_clicked()
    {
        QString searchString = ui->lineEdit->text();

        QTextDocument *document = ui->textEdit->document();
        QTextCursor cursor = ui->textEdit->textCursor();
        cursor = document->find(searchString, cursor,
            QTextDocument::FindWholeWords);
        ui->textEdit->setTextCursor(cursor);

        bool found = cursor.isNull();

        if (!found && previouslyFound) {
            int ret = QMessageBox::question(this, tr("End of Document"),
            tr("I have reached the end of the document. Would you like "
            "me to start searching from the beginning of the document?"),
            QMessageBox::Yes | QMessageBox::No, QMessageBox::Yes);

            if (ret == QMessageBox::Yes) {
                cursor = document->find(searchString,
                    QTextDocument::FindWholeWords);
                ui->textEdit->setTextCursor(cursor);
            } else
                return;
        }
        previouslyFound = found;
    }
    \endcode

    If you compile and run the above code, however, the application does not
    work correctly due to a logic error. To locate this logic error, step
    through the code using the following buttons:
    \inlineimage icons/qtcreator-debug-button-stop.png
    (\uicontrol {Stop Debugger}), \inlineimage icons/debugger_stepover_small.png
    (\uicontrol {Step Over}), \inlineimage icons/debugger_stepinto_small.png
    (\uicontrol {Step Into}), and \inlineimage icons/debugger_stepout_small.png
    (\uicontrol {Step Out}).

*/
