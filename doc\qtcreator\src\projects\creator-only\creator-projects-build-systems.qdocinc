// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
//! [build systems]

    \section1 Selecting the Build System

    You can use several build systems to build your projects.

    \l{qmake Manual}{qmake} is a cross-platform system for build automation
    that helps simplify the build process for development projects across
    different platforms. qmake automates the generation of build configurations
    so that you need only a few lines of information to create each
    configuration. Qt installers install and configure qmake.
    To use one of the other supported build systems, you need to set it up.

    \l {Build with CMake}{CMake} is an alternative to qmake for automating the
    generation of build configurations. For more information, see
    \l {Setting Up CMake}.

    \l {https://mesonbuild.com/}{Meson} Meson is a fast and user-friendly
    open-source build system that aims to minimize the time developers spend
    writing or debugging build definitions and waiting for the build system
    to start compiling code. For more information, see \l {Setting Up Meson}.

    \l{Qbs Manual}{Qbs} is an all-in-one build tool that generates a build graph
    from a high-level project description (like qmake or CMake do) and executes
    the commands in the low-level build graph (like make does). For more
    information, see \l{Setting Up Qbs}.

    To change the location of the project directory, and to specify settings
    for building and running projects, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Build & Run} > \uicontrol General.

    Specify build and run settings for different target platforms, in the
    \uicontrol Projects mode. For more information on the options you have,
    see \l{Specifying Build Settings}.

//! [build systems]
*/
