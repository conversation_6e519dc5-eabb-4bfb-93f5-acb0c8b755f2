// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [clang format]

    \section2 Automatic Formatting and Indentation

    The Clang Format plugin uses the
    \l{https://clang.llvm.org/docs/LibFormat.html}{LibFormat}
    library for automatic formatting and indentation.

    To enable the plugin, select \uicontrol Help > \uicontrol {About Plugins} >
    \uicontrol {C++} > \uicontrol {ClangFormat}. Then select
    \uicontrol {Restart Now} to restart \QC and load the plugin.

    \note If you enable Clang Format, do not use the \l{Beautifying Source Code}
    {Beautifier} because combining them can provide unexpected results.

    You can use Clang Format to enforce a coding style for a project or the
    whole organization. Create a \c {.clang-format} file that contains the
    \l{https://clang.llvm.org/docs/ClangFormatStyleOptions.html}
    {Clang Format Style Options} to use and save it in the root folder of the
    project or one of its parent folders. The plugin searches for the Clang
    format file recursively from the directory that contains the source file
    up to the file system root.

    To override the \c {.clang-format} file globally for all projects, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {C++} > \uicontrol Copy >
    \uicontrol Edit > \uicontrol {ClangFormat} >
    \uicontrol {Override Clang Format configuration file}.

    \image qtcreator-code-style-clang-format.png "C++ Clang Format preferences"

    In \uicontrol {Formatting mode}, select \uicontrol {Indenting Only} to only
    indent code. Select \uicontrol {Full Formatting} to use the \key {Ctrl+I}
    keyboard shortcut to format code instead of indenting. To apply the
    formatting while you type, select \uicontrol {Format while typing}. To apply
    the formatting to the edited code when you save the file, select
    \uicontrol {Format edited code on file save}.

    This creates a local configuration file that overrides the one stored in the
    file system.

    To override the \c {.clang-format} file for a particular project, create a
    copy of the built-in style and edit its settings by selecting
    \uicontrol Projects > \uicontrol {Project Settings} >
    \uicontrol {Code Style} > \uicontrol Copy > \uicontrol Edit >
    \uicontrol {ClangFormat} >
    \uicontrol {Override Clang Format configuration file}.

    You can create \c {.clang-format} files that contain the configuration
    options of a certain predefined style from the command line. For example,
    to create a format file for the LLVM style, enter the following command:

    \badcode
    clang-format -style=llvm -dump-config > .clang-format
    \endcode

//! [clang format]
*/
