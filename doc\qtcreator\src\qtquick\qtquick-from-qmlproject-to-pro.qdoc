// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-converting-ui-projects.html
    \if defined(qtdesignstudio)
    \previouspage studio-porting-projects.html
    \nextpage creator-editor-external.html
    \else
    \previouspage creator-qtquickdesigner-plugin.html
    \nextpage creator-quick-ui-forms.html
    \endif

    \title Converting UI Projects to Applications

    Qt Quick UI projects are useful for creating user interfaces. To
    use them for application development in Qt Creator you have to add:

    \list
        \li Project configuration file (CMakeLists.txt or .pro)
        \li C++ code (.cpp)
        \li Resource files
        \li Code needed for deploying applications to \l{glossary-device}
            {devices}
    \endlist

    For more information about integrating QML and C++, see
    \l{Overview - QML and C++ Integration}.

    \note Since \QDS 2.3.0, \QDS project wizard templates generate projects that
    can be built with CMake. You can open the \e CMakeLists.txt project file in
    Qt Creator to continue developing the project.

    \if defined(qtdesignstudio)
    For more information, see \l{Designer-Developer Workflow}.
    \else
    For more information about using \QDS to create projects, see
    \l{Qt Design Studio Manual}.
    \endif


    If you want to use qmake as the build system, you can use a Qt Creator
    wizard template to create a Qt Quick application that is built using the
    qmake build system and then copy the source files from the Qt UI Quick
    project to the application project.

    You can use the \c RESOURCES option in the project configuration file to
    automatically add all the QML files and related assets to a
    \l{The Qt Resource System}{Qt resource collection file (.qrc)}. However,
    large files should be included as external binary resources instead of
    compiling them into the binary.

    The wizard automatically adds the \c QML_IMPORT_PATH option to the project
    file for specifying the required \l{QML Import Path}{QML import path}. The
    path is only needed if more than one subdirectory contains QML files.

    Then you can use the \l QQuickView class in the main C++ source file to
    show the main QML file when the application starts.

    The \e {Qt Quick Designer Components} module is installed when you
    install \QDS. If you use Qt Quick Studio Components or Effects
    from the module in a project that you want to edit in Qt Creator,
    you have to build the module and install it to your Qt to be able to
    build your project. For more information, see
    \l{Adding Qt Quick Designer Components to Qt Installations}.

    The \l{Qt Quick Timeline} module is installed when you install \QDS.
    If you only install Qt Creator and Qt, remember to also select the
    Qt Quick Timeline module for installation. If your Qt is older than
    5.14, you must build the Qt Quick Timeline module and install it to
    your Qt to be able to build your project. For more information, see
    \l{Adding Qt Quick Timeline Module to Qt Installations}.

    \section1 Converting into qmake Projects

    To convert a project that has a .qmlproject file to one that has a .pro
    file:

    \list 1
        \li Select \uicontrol File > \uicontrol {New Project} >
            \uicontrol {Application (Qt)} > \uicontrol {Qt Quick Application} >
            \uicontrol Choose.
        \li In the \uicontrol {Build system} field, select \l qmake as the build
            system to use for building and running the project, and then select
            \uicontrol Next (or \uicontrol Continue on \macos).
        \li Follow the instructions of the wizard to create the project.
        \li In the file explorer, copy the source files from the Qt Quick UI
            project directory to a subdirectory in the application
            project directory. For the purpose of these instructions, the
            directory is called \c qml.
        \li Open the application project file, and edit the value of the
            \c RESOURCES option to add the following line:
            \badcode
            RESOURCES += \
                $$files(qml/*)
            \endcode
        \li Also edit the value of the \c QML_IMPORT_PATH option to specify the
            QML import path:
            \badcode
            QML_IMPORT_PATH = qml/imports
            \endcode
            Where \c {qml/imports} is the import path.
        \li Select \uicontrol Build > \uicontrol {Run qmake} to apply the
            \c RESOURCES option to the build configuration.
        \li Open the \e {main.cpp} file and replace the QQmlApplicationEngine
            object with a QQuickView object:
            \quotefromfile progressbar/main.cpp
            \skipto QQuickView view;
            \printuntil view.show()
            Where \c {qrc:/qml/imports} is the import path and
            \c {qrc:/qml/ProgressBar.ui.qml} is the path to and the
            name of the main QML file in the Qt Quick UI project.
        \li Select \uicontrol Build > \uicontrol Run to build and run your
            project.
    \endlist

    For example, if you copy the source files of the \e ProgressBar
    example from your \QDS installation (located in the
    \c{\share\qtcreator\examples\ProgressBar} directory) to an empty
    Qt Quick application project and make the necessary changes, the
    \e {main.cpp} file should look as follows:

    \quotefile progressbar/main.cpp

    \section1 Handling Large Data Files

    Graphical assets used in the UI, such as images, effects, or 3D scenes
    are a typical cause for performance problems in UIs. Even building the
    application requires huge amounts of memory if you try to include large
    asset files, such as 100-MB 3D models or 64-MB textures, into the \c .qrc
    file for compiling them into the binary.

    First try to optimize your assets, as described in \l{Optimizing Designs}
    and \l {Creating Optimized 3D Scenes}.

    If you really need large assets, they should either be loaded directly from
    the file system or use the Qt resource system in the dynamic way. For more
    information, see \l{The Qt Resource System} in the Qt documentation.

    \section1 Adding Custom Fonts

    \if defined(qtdesignstudio)
    To \l{Using Custom Fonts}{use custom fonts}
    \else
    To use custom fonts
    \endif
    from the Qt Quick UI project, call the QFontDatabase::addApplicationFont()
    function from the \e {main.cpp} file.

    \section1 Adding Qt Quick Designer Components to Qt Installations

    If you use Qt Quick Studio Components or Effects in your project, you have
    to check out and install the \e {Qt Quick Designer Components} module from
    \l{https://code.qt.io/cgit/qt-labs/qtquickdesigner-components.git/}
    {Qt Code Review}.

    For example:
    \badcode
    git clone https://code.qt.io/qt-labs/qtquickdesigner-components.git
    \endcode

    Then use qmake from your Qt installation to build the module and to add it
    to your Qt. Switch to the directory that contains the sources (usually,
    qtquickdesigner-components), make sure you checkout the qmake branch, and enter
    the following commands:

    \badcode
    <path_to_qmake>\qmake -r
    make
    make install
    \endcode

    On Windows, use the \c nmake and \c {nmake install} commands instead.

    If you prefer CMake instead and you want to benefit from the QML compilation,
    then you can checkout the dev branch instead. CMake is only supported since Qt 6.2.
    Enter the following commands:

    \badcode
    mkdir build
    cd build
    cmake -GNinja -DCMAKE_INSTALL_PREFIX=<path_to_qt_install_directory> <path_to_qtquickdesigner-components>
    cmake --build .
    cmake --install .
    \endcode

    \section1 Adding Qt Quick Timeline Module to Qt Installations

    \note You only need to do this if your Qt version is older than 5.14.

    Check out the \l{Qt Quick Timeline} module from
    \l{https://codereview.qt-project.org/#/admin/projects/qt/qtquicktimeline}
    {Qt Code Review}.

    For example:
    \badcode
    git clone "https://codereview.qt-project.org/qt/qtquicktimeline"
    \endcode

    To use qmake, you need to check out a branch or tag that contains the
    qmake configuration files.

    For example:
    \badcode
    git checkout v5.15.2
    \endcode

    Then build the module and add it to your Qt as described in the previous
    section.
*/
