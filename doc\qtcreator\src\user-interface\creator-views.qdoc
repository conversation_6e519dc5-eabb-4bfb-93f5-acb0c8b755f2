// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-views.html
    \previouspage creator-sidebars.html
    \if defined(qtdesignstudio)
    \nextpage creator-output-panes.html
    \else
    \nextpage creator-projects-view.html
    \endif

    \title Browsing Project Contents

    You can organize \QC views in \l {Working with Sidebars}{sidebars} or as
    \if defined(qtdesignstudio)
    \l {Managing Workspaces}{workspaces},
    \else
    workspaces
    \endif
    depending on the \l{Selecting Modes}
    {mode} you are working in. Only views that are relevant to a mode are
    available in it.

    Manage projects and files in the following views:

    \list
        \li \l Projects shows a list of projects open in the current
            session and the project files needed by the build system.
        \li \l{File System} shows all files in the currently selected
             directory.
        \li \l{Open Documents} shows currently open files.
        \li \l{Using Bookmarks}{Bookmarks} shows all bookmarks for the current
            session.
        \li \l{Working with Branches}{Git Branches} shows the local and remote
            branches for the project in the Git version control system.
        \li \l{Viewing Defined Types and Symbols}{Outline} shows an overview of
            defined types and other symbols, as well as their properties and
            hierarchy in a source file.
          \endlist

    \if defined(qtcreator)
    The following views display additional information about C++ code:

    \list
        \li \l{Viewing the Class Hierarchy}{Class View} shows the class
            hierarchy of the currently open projects.
        \li \l{Running Autotests}{Tests} lists autotests and Qt Quick
            tests in the project.
        \li \l{Viewing Type Hierarchy}{Type Hierarchy} shows the base
            classes of a class.
        \li \l{Viewing Include Hierarchy}{Include Hierarchy} shows the
            files that the current file includes and those that include
            the current file.
    \endlist
    \else
    For more information about views that are only available when editing QML
    files in the \uicontrol Design mode, see \l{Design Views}.
    \endif

    The following sections describe the additional options in a particular view.

    In some views, right-clicking opens a context menu that contains functions
    for managing the objects listed in the view.

    \section1 Viewing Defined Types and Symbols

    The \uicontrol Outline view shows an overview of defined types and other
    symbols, as well as their properties and hierarchy in a source file.
    You can move the code for types and symbols around in the source file
    by dragging and dropping them in the outline.

    \image qtcreator-outline-view.png "Outline view"

    \list
        \li To see a complete list of all bindings, select
            \inlineimage icons/filtericon.png
            (\uicontrol {Filter Tree}) > \uicontrol {Show All Bindings}.
        \li To sort types or symbols alphabetically, select
            \inlineimage icons/sort_alphabetically.png
            (\uicontrol {Sort Alphabetically}).
        \li To stop the synchronization with the type or symbol selected in the
            editor, deselect \inlineimage icons/linkicon.png
            (\uicontrol {Synchronize with Editor}).
    \endlist

    \section1 Viewing the Class Hierarchy

    The \uicontrol {Class View} shows the class hierarchy of the currently
    open projects.

    \image qtcreator-class-view.png "Class View"

    To organize the view by subprojects, click
    \inlineimage icons/qtcreator-show-subprojects.png
    (\uicontrol {Show Subprojects}).

    To visit all parts of a namespace, double-click on the namespace item
    multiple times.

    \section1 Viewing Type Hierarchy

    To view the base classes and derived classes of a class, right-click the
    class in the code editor and select \uicontrol {Open Type Hierarchy} or
    press \key {Ctrl+Shift+T}.

    \image qtcreator-type-hierarchy-view.png "Type Hierarchy view"

    To navigate in the type hierarchy, double-click a class or select it,
    and then select \uicontrol {Open Type Hierarchy} in the context menu.
    The view highlights the previous class.

    The definition of the selected class opens in the code editor. You
    can also select \uicontrol {Open in Editor} in the context menu to open
    it.

    \section1 Viewing Include Hierarchy

    To view the files that the current file includes and those that include
    the current file, right-click in the editor and select
    \uicontrol {Open Include Hierarchy} or press \key {Ctrl+Shift+I}.

    \image qtcreator-include-hierarchy-view.png "Include Hierarchy view"

    To keep the view synchronized with the file currently open in the editor,
    select \inlineimage icons/linkicon.png
    (\uicontrol {Synchronize with Editor}).
*/
