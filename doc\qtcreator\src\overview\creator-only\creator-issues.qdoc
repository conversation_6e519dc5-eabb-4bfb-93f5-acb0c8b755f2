// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-how-tos.html
    \page creator-known-issues.html
    \nextpage creator-glossary.html

    \title Known Issues

    This section lists known issues in \QC version \qtcversion. The development
    team is aware of them, and therefore, you do not need to report them as
    bugs.

    For a list of fixed issues and added features, see the changelog file in
    the \c{qtcreator\dist} folder or the \l{https://bugreports.qt.io}
    {Qt Project Bug Tracker}.

    \section1 General Issues

    \list

        \li  If you change the Input Languages in Windows, \QC might not
            respond for 30 seconds. This is a known issue in the Advanced Text
            Service of Microsoft Windows.

        \li  \QC uses SQLite for storing some of its settings. SQLite is
            known to have problems with certain NFS servers (most notably the
            nfs-user-server 2.2beta), since they can lock up the application
            when it tries to lock the database. If your home directory is on an
            NFS share and you encounter this issue, one option would be to
            switch to the nfs-kernel-server, or create a symlink so that the
            settings are stored locally.

        \li  The Okteta KDE custom widget plugin might be installed as part of
            some Linux distributions. It can cause Qt Designer to crash. For
            more information, see:

            \list

                \li  \l{http://bugs.launchpad.net/ubuntu/+source/kdeutils/+bug/662005}
                    {Ubuntu bug 662005}

                \li  \l{https://bugreports.qt.io/browse/QTBUG-12025}
                    {QTBUG-12025}

           \endlist

            To resolve the issue, enter the following command to remove the
            package:
            \code
            sudo apt-get remove okteta
            \endcode
            Or delete the following file:
            \c /usr/lib/kde4/plugins/designer/oktetadesignerplugin.so.

    \endlist

    \section1 Editing Issues

    \list

        \li Code completion does not support typedefs for nested classes.

    \endlist

    \section1 Projects Issues

    \list

        \li  Paths or file names containing spaces or special characters
            (such as colons, dollar signs, and hash marks) may cause problems.
            This is because some of the tools \QC uses in the background have
            restrictions on the characters allowed in file and directory names.
            To be on the safe side, we recommend creating projects and project
            items with names consisting of plain characters, numbers,
            underscores, and hyphens.

        \li  If error messages displayed in \l {Compile Output} contain
            paths where slashes are missing (for example, C:QtSDK),
            check your PATH variable. For more information, see
            \l{Troubleshooting MinGW Compilation Errors}.

    \endlist

    \section1 Debugging Issues

    \list

        \li  When debugging executables created by the GNU Compiler version 4.5.0
            (all platforms), some data types will not be displayed in the
            \uicontrol Locals and \uicontrol Expressions views due to missing
            debug information.

        \li  GDB on Windows may not work if the 'Embassy \reg Security Center'
            software by 'Wave \reg Systems' is installed and active (causing
            crashes in \c{vxvault.dll)}).

        \li  GDB may take long to load debugging symbols, especially from large
            libraries.

        \li  Setting breakpoints in code that is compiled into the binary more
            than once does not work.

        \li  Setting breakpoints in files that do not have unique absolute
            paths may fail. For example, remounting parts of a file system
            using the \c {--bind mount} option.

        \li  Setting breakpoints in files will fail when using LLDB if the file path
            contains symbolic links.

        \li  A regression in GCC 4.5.0 causes return value optimization to
            produce inaccurate debug info that GCC applies also to
            non-optimized builds. For more information, see
            \l{http://gcc.gnu.org/bugzilla/show_bug.cgi?id=44731}
            {GCC Bugzilla - Bug 44731}.

    \endlist
*/
