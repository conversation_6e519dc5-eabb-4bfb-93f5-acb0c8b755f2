// Copyright (C) 2018 Blackberry
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-deployment-b2qt.html
    \page creator-deployment-qnx.html
    \nextpage creator-deployment-embedded-linux.html
    \title Deploying to QNX Neutrino

    You can specify settings for deploying applications to QNX Neutrino
    devices in the project configuration file and in \uicontrol Projects
    > \uicontrol {Run Settings} > \uicontrol Deployment.

    \image qtcreator-qnx-deployment.png "Deploy to device"

    The deployment process is described in more detail in
    \l{Deploying to Remote Linux}.

    \section1 Finding Configured Devices

    The \uicontrol {Check for a configured device} deployment step looks for a
    device that is ready for deployment.
*/
