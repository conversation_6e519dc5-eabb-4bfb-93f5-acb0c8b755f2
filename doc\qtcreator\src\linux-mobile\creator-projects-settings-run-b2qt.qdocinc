// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
//! [run settings boot2qt]

    \section1 Specifying Run Settings for Boot2Qt Devices

    To run and debug an application on a \l Boot2Qt device (commercial only), you
    must create connections from the development host to the device and add the
    device configurations to \l{glossary-buildandrun-kit}{kits}. Select
    \uicontrol {Manage Kits} to add devices to kits. For more information, see
    \l{http://doc.qt.io/Boot2Qt/b2qt-installation-guides.html}
    {Boot2Qt: Installation Guide}.

    The run settings display the path to the executable file on the development
    host and on the device.

    For more information on the deployment steps, see
    \l{Deploying to Boot2Qt}.

//! [run settings boot2qt]
*/
