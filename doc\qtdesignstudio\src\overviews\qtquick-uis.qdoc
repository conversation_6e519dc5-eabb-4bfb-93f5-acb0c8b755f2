// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-uis.html
    \if defined(qtdesignstudio)
    \previouspage {Examples}
    \nextpage studio-app-flows.html
    \else
    \previouspage qtquick-text-editor.html
    \nextpage quick-components.html
    \endif

    \title Wireframing

    \table
        \row
            \li \image front-ui.png
            \li Plan your UI properly. Know what elements, such as screens,
                components, and states, you need. Create a descriptive wireframe
                and acquire a detailed UI specification before you start to make
                the process of creating the UI more efficient.
                \QDS enables you to turn your UI concept into a wireframe with
                a scalable layout where all your screens and controls are in
                place. You can present your wireframe to developers and other
                stakeholders for discussion, review, and approval before
                continuing into the prototyping phase.
    \endtable

    In \QDS, you build UIs around the behavior of \l{glossary-component}
    {components} and how they connect with one another. You can use preset
    components available in the \l Components view or combine them to create
    your own components. You can specify values for the \e properties of a
    component to change its appearance and behavior. All components have a
    set of predefined properties, some of which control things that are
    visible to users, while others are used behind the scene.

    You drag-and-drop the preset components from the \uicontrol Components view
    to the \l {2D}, \l {3D}, or \l Navigator view to create
    instances of them. You then change the instances to your liking by modifying
    their properties in the \l Properties view. The application code is
    generated for you accordingly. For more information about the code, open
    the developer documentation by pressing \key F1.

    \list

        \if defined(qtdesignstudio)
        \li \l {Designing Application Flows}

            You can design an application in the form of a \e {schematic diagram}
            that shows all significant components of an application UI and their
            interconnections by means of symbols. This results in an
            interactive prototype that can be clicked through to simulate
            the user experience of the application.
        \endif

        \li \l {Using Components}

            \QDS comes with \e {preset components} that you can use in
            wireframes and prototypes by creating instances of them.
            To build your own components, you can modify the \e properties
            of the component instances and combine them. You can import
            designs and assets from other tools as components.

        \li \l {Specifying Component Properties}

            You can specify values for the properties of a component to change
            its appearance and behavior. All components have a set of predefined
            properties. Some properties, such as position, size, and visibility,
            are common to all components, whereas others are specific to the
            component. You can specify values for properties of component
            instances in the \l Properties view.

        \li \l {Scalable Layouts}

            The position of a component in a UI can be either absolute
            or relative to other components. While manual positioning
            is efficient for a static UI, consider the other available
            methods, such as anchors, layouts, positioners, and property
            bindings, for dynamic UIs.

        \if defined(qtcreator)
        \li \l {Using Custom Fonts}

            You can load custom fonts to \QC and use them in your designs.
        \endif

        \li \l {Annotating Designs}

            You can annotate your designs to provide reviewers or developers
            with additional information about them.

        \if defined(qtcreator)
        \li \l {Loading Placeholder Data}

            You can create QML files that contain placeholder data, so that
            you can test grid, list, or path views, even though you don't
            have access to real data.

        \li \l{UI Files}

            Some of the wizards create projects that contain UI files
            (.ui.qml). You should always edit UI files in the \l {2D}
            and \l Properties view, to avoid breaking the code.

        \endif

    \endlist
*/
