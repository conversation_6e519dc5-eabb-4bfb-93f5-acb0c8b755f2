// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-run-settings.html
    \page creator-editor-settings.html
    \nextpage creator-code-style-settings.html

    \title Specifying Editor Settings

    \QC uses the \l{Editing MIME Types}{MIME type} of the file to
    determine which mode and editor to use for opening the file. For example,
    \QC opens .txt files in \uicontrol Edit mode in the text editor.

    You can configure the text editor according to your needs. You can specify
    editor behavior either globally for all projects or separately for each
    project. To specify global editor behavior, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Text Editor} > \uicontrol Behavior.

    To configure the text editor behavior for the current project:

    \list 1

        \li Select \uicontrol Projects > \uicontrol {Project Settings} >
            \uicontrol Editor.

        \li Deselect \uicontrol {Use global settings}.

        \li Specify text editor settings for the project.

    \endlist

    \image qtcreator-editor-settings.png "Editor view"

    Click \uicontrol {Restore Global} to revert to the global settings.

    For more information about the settings, see:

    \list

        \li \l{Indenting Text or Code}

        \li \l{File Encoding}

        \li \l{Selecting Line Ending Style}

        \li \l{Moving to Symbol Definition or Declaration}

        \li \l{Configuring Fonts}

        \li \l{Highlighting and Folding Blocks}

        \li \l{Viewing Function Tooltips}

    \endlist

*/
