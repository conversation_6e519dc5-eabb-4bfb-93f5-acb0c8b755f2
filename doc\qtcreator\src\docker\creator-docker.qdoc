// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

    /*!
    \page creator-adding-docker-devices.html
    \previouspage creator-developing-b2qt.html
    \nextpage creator-developing-ios.html

    \title Adding Docker Devices

    Create Docker devices from \l{ https://docs.docker.com/get-started/overview/}
    {Docker images} and use them to build, run, and debug applications. A Docker
    container operates like a virtual machine but uses less system resources at
    the cost of being less flexible.

    Docker support is experimental. While Linux, \macos, and Windows hosts are
    supported in principle, Linux is the recommended platform.

    Currently, only CMake is supported for building applications in the Docker
    container.

    To build, run, and debug applications on Docker devices, you must install and
    configure \c docker-cli on the development host. \QC automatically detects
    \l{Adding Kits}{build and run kit} items, such \l{Adding Debuggers}
    {debuggers} and \l{Adding Qt Versions}{Qt versions}, in the Docker container
    and creates kits for the devices.

    You can use images that are available locally. You can pull
    images from Docker hub or other registries using the
    \l{https://docs.docker.com/engine/reference/commandline/pull/}{docker pull}
    command. To check that an image is available locally, run the
    \c {docker images} command in a terminal.

    \section1 Enabling Docker Plugin

    To enable the experimental Docker plugin:

    \list 1
        \li In \QC, select \uicontrol Help > \uicontrol {About Plugins} >
            \uicontrol Utilities > \uicontrol {Docker (experimental)}.
        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.
    \endlist

    \section1 Adding Docker Images as Devices

    To add a Docker image as a device:

    \list 1
        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Devices
            > \uicontrol Docker and enter the path to the Docker CLI in
            the \uicontrol Command field.
            \image qtcreator-preferences-devices-docker.webp "Docker tab in Devices preferences"
        \li Select \uicontrol Devices > \uicontrol Add >
            \uicontrol {Docker Device} > \uicontrol {Start Wizard}
            to search for images in your local Docker installation.
        \li Select the Docker image to use, and then select \uicontrol OK.
        \li In \uicontrol Devices, check and modify Docker device preferences.
            \image qtcreator-preferences-devices-docker-device.webp "Docker device preferences"
        \li Select \uicontrol {Run as outside user} to use the user ID
            and group ID of the user running \QC in the Docker container.
        \li Select \uicontrol {Do not modify entry point} to stop \QC from
            modifying the entry point of the image if the image starts into
            a shell.
        \li In \uicontrol {Paths to mount}, specify host directories to
            mount into the container, such as the project directory.
        \li In \uicontrol {Search locations}, select where to search
            for kit items.
        \li Select \uicontrol {Auto-detect Kit Items} to find kit items and to
            create kits for the Docker device.
        \li Select \uicontrol Apply to save your changes.
    \endlist

    The following sections describe the Docker device preferences in more detail.

    \section2 Selecting Docker Images

    The \uicontrol {Docker Image Selection} dialog displays a list of Docker
    images in your local Docker installation. You can sort the images according
    to the repository name or tag or the image ID or size.

    \image qtcreator-docker-image-selection.webp "Docker Image Selection dialog"

    Select \uicontrol {Show unnamed images} to show images that are not tagged.

    Double-click an image to select it.

    \section2 Modifying Entry Points

    The entry point of a Docker container is specified in the container settings
    and started as the main process when starting the container. The entry point
    can be a shell, an SSH server, or another process, for example. By default,
    \QC modifies the entry point before it starts a container to make sure that
    the main process is an \c sh shell.

    To stop \QC from modifying the entry point, select
    \uicontrol {Do not modify entry point}. However, if the entry
    point you specify is not a shell, \QC cannot start the container.

    \section2 Specifying Paths to Mount

    You can either copy your project files into the Docker container or specify
    paths to them in \uicontrol {Paths to mount}. Shared mounts are restricted
    to locations in the host system that can end up in the same absolute location
    in the Docker container. On Windows, network mounts cannot be used as shared
    mounts.

    The paths in \uicontrol {Paths to mount} are mapped one-to-one to the Docker
    container. Select \uicontrol Insert to browse directories to add. Select
    \uicontrol {Delete Line} to delete the selected path or \uicontrol Clear
    to delete all paths.

    \section2 Auto-detecting Kit Items

    Select \uicontrol {Auto-detect Kit Items} to find kit items and create kits
    for the Docker device. You can search for kit items in the device's PATH or
    in the specified directories.

    In \uicontrol {Search locations}, select:

    \list
        \li \uicontrol {Search in PATH} to find kit items in PATH.
        \li \uicontrol {Search in Selected Directories} to find kit items in
            the selected directories.
        \li \uicontrol {Search in PATH and Additional Directories} to find kit
            items in the PATH and in the selected directories.
    \endlist

    When searching in directories, enter a semicolon-separated list of directory
    paths in the Docker image to scan for kit entries.

    To list the automatically detected kit items, select
    \uicontrol {List Auto-Detected Kit Items}. To remove
    them, select \uicontrol {Remove Auto-Detected Kit Items}.

    \section1 Editing Docker Device Kits

    Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits to check
    that the automatically generated kits point to the appropriate kit items.

    To specify build settings:

        \list 1
            \li Open a project for an application you want to develop for the
                device.
            \li Select \uicontrol Projects > \uicontrol {Build & Run} to enable
                the kit that you specified above.
        \endlist

    Select \uicontrol Run to specify run settings. Usually, you can use
    the default settings.
*/
