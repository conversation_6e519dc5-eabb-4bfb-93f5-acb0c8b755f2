// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage sketchqtbridge.html
    \page qtbridge-sketch-setup.html
    \nextpage qtbridge-sketch-using.html

    \title Setting Up \QBSK

    \QBSK is included in the
    \l{https://www.qt.io/pricing}{Qt Design Studio Enterprise license}.
    You can use the Qt Installer to have the \QBSK plugin package copied to the
    following path in your Qt installation folder:

    \list
        \li On Windows:
            \c {Tools\QtDesignStudio\sketch_bridge\io.qt.qtbridge.sketchplugin}
        \li On \macos:
            \c {QtDesignStudio/sketch_bridge/io.qt.qtbridge.sketchplugin}.
    \endlist

    \note Install the Sketch app before installing the plugin.

    To install the \QBSK plugin to Sketch, double-click
    \c io.qt.qtbridge.sketchplugin in the \c sketch_bridge
    folder in the installation directory of \QDS. Sketch
    will automatically install \QBSK.

    You can launch the Sketch plugin from \uicontrol Plugins >
    \uicontrol {\QB} in Sketch.
*/
