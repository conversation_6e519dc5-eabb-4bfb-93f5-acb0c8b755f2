// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page quick-controls.html
    \previouspage quick-user-interaction-methods.html
    \nextpage quick-data-models.html

    \title UI Controls

    You can create instances of preset UI controls to inform users about
    the progress of the application or to gather input from users. They are
    available in \uicontrol Components >
    \uicontrol {Qt Quick Controls} > \uicontrol Controls.

    \image qtquick-designer-qtquickcontrols-types.png "Qt Quick Controls components"

    The following types of controls are available for user interaction:

    \list
        \li \l {Button Controls}
        \li \l {Indicators}
        \li \l {Selectors}
        \li \l {Tab Bar}
        \li \l {Tool Bar}
        \li \l {Summary of UI Controls}
    \endlist

    You can specify values for the properties of component instances in the
    \l Properties view. Some properties are common to all components,
    whereas some are common to particular types of controls. Some properties
    are only available for a particular control. The following sections
    describe the preset UI controls and their properties.

    \section1 General Control Properties

    You can set control properties in the \l Properties view.

    \image qtquick-designer-control-properties.png "Control section in Properties"

    The \uicontrol Enable check box indicates whether the control is enabled.

    The value of the \uicontrol {Focus policy} field determines whether the
    control accepts focus by tabbing, clicking, and using the mouse wheel.

    Select the \uicontrol Hover and \uicontrol Wheel check boxes to enable the
    control to accept mouse events. The hover value is propagated to all child
    components, unless it has been explicitly set for them.

    \note Take care when enabling wheel events for controls within scrollable
    components, such as \l Flickable because the control will consume the
    events, and therefore interrupt scrolling of the flickable.

    \uicontrol Spacing is useful for controls that have multiple or repetitive
    building blocks. For example, some \l{Styling Controls}{styles} use spacing
    to determine the distance between the text and indicator of a
    \l {Check Box}. Spacing is not enforced by the controls, so each style may
    interpret it differently, and some may ignore it altogether.

    \section1 Button Controls

    Qt Quick Controls offer a selection of button-like controls for specific
    use cases. The following sections contain guidelines for choosing the button
    most suitable for a use case and discuss the values you can set for button
    properties in the \uicontrol Properties view.

    \image qtquick-designer-button-types.png "Button controls in the 2D view"

    Recommendations for buttons that contain text:

    \list
        \li Keep labels short and concise.
        \li Use the default font unless you have UI guidelines specifying
            otherwise.
        \li If the text is localized, consider how a longer text affects the
            layout.
    \endlist

    For more information about setting text properties, see
    \l {Character Properties} and \l Padding.

    The properties that are shared by all button controls are described in:

    \list
        \li \l {Displaying Text and Icons}
        \li \l {Checking Buttons}
        \li \l {Button Signals}
    \endlist

    \section2 Button

    You can create an instance of \uicontrol Components >
    \uicontrol {Qt Quick Controls} > \uicontrol Controls > \uicontrol Button:

    \image qtquickcontrols2-button.gif "Qt Quick Controls - Button"

    \if defined(qtdesignstudio)
    Alternatively, you can use a wizard to create a \l{Creating Custom Controls}
    {custom button}:

    \image studio-custom-button.gif "Custom button"
    \endif

    A button can be pushed or clicked by users. Typically, buttons
    are used to perform an action or to answer a question. For example, \e OK,
    \e Apply, \e Cancel, \e Close, \e Yes, \e No, and \e Help.

    The button text should be a verb describing the action, or a noun matching
    the title of the popup that will be opened.

    Don't use a button to set state because a \l Switch is more suitable for
    that purpose.

    \section3 Highlighted Buttons

    Select the \uicontrol Highlight check box in the \uicontrol Button section
    to draw the users' attention towards a button. Highlighting a button has no
    effect on keyboard interaction.

    \image qtquick-designer-button-properties.png "Button properties"

    The following image shows an example of a highlighted button:

    \image qtquickcontrols2-button-highlighted.gif "Highlighted button"

    \section3 Flat Buttons

    A flat button typically does not draw a background unless it is pressed or
    checked. To create a flat button, select the \uicontrol Flat check box in
    the \uicontrol Button section.

    The following image shows an example of a flat button:

    \image qtquickcontrols2-button-flat.gif "Flat button"

    \if defined(qtdesignstudio)
    \section3 Icon Buttons

    To create a button that contains an icon, use the wizard template to
    \l{Creating Custom Controls}{create a custom button} and drag-and-drop
    the icon to the button background component. For an example of using the
    wizard template, see \l{Creating a Push Button}.
    \endif

    \section2 Delay Button

    \image qtquickcontrols2-delaybutton.gif "Delay button"

    The \uicontrol {Delay Button} control incorporates a delay before
    triggering an action. This delay prevents accidental presses.

    Use delay buttons in touch UIs and for actions that must be
    triggered with care.

    You can set the delay in milliseconds in the \uicontrol Delay field.

    \section2 Check Box

    You can create instances of \uicontrol Components >
    \uicontrol {Qt Quick Controls} > \uicontrol Controls >
    \uicontrol {Check Box}:

    \image qtquickcontrols2-checkbox.gif "Check boxes"

    \if defined(qtdesignstudio)
    Alternatively, you can use a wizard to create \l{Creating Custom Controls}
    {custom check boxes}:

    \image studio-custom-check-box.gif "Custom check boxes"
    \endif

    A check box presents an option button that can be toggled on
    (checked) or off (unchecked). Check boxes are typically used to select
    one or more options from a set of options. For larger sets of options,
    such as those in a list, consider creating an instance of the
    \uicontrol {Check Delegate} control instead.

    \image qtquickcontrols2-checkdelegate.gif "Check delegate"

    A delegate can be highlighted in order to draw the user's attention towards
    it. Highlighting has no effect on keyboard interaction. Select the
    \uicontrol Highlight check box in the \uicontrol {Item Delegate} section to
    highlight the delegate.

    Use check boxes to build multi-selection option lists where any number
    of options can be selected, including none, but the options are not
    mutually exclusive.

    Use a single check box for a yes/no choice, such as when users have to
    accept the terms of service agreement in a dialog. For a single yes/no
    choice, you can also use a \l Switch. If users are choosing between options,
    use a check box. If they are choosing between actions to be taken, a switch
    is recommended.

    The value of the \uicontrol Checked check box in the
    \uicontrol {Button Content} section determines the state of the
    check box. However, in addition to the checked and unchecked states, a
    check box has a third state: \e {partially checked}.

    \image qtquick-properties-check-box.png "Check Box properties"

    Select the \uicontrol Tri-state check box in the \uicontrol {Check Box}
    section to enable the check box to cycle between checked, partially
    checked, and unchecked states when users toggle it by using touch, mouse,
    or keyboard.

    \image qtquickcontrols2-checkbox-tristate.gif "Tri-state check box"

    When options can be grouped, you can use a partially checked check box to
    represent the whole group. Select \uicontrol PartiallyChecked in the
    \uicontrol {Check state} field to indicate that users selected some
    sub-components in the group but not all of them.

    The checkable options are often listed vertically.

    The check box label should be a statement that the check mark makes true
    and that the absence of a check mark makes false. Therefore, the check box
    label should not contain a negative statement.

    \section2 Radio Button

    \image qtquickcontrols2-radiobutton.gif "Radio button"

    \uicontrol {Radio Button} is an option button that can be toggled on
    (checked) or off (unchecked). Radio buttons are typically used to select
    one option from a set of options. Selecting an option automatically clears
    the previous selection.

    If there are only two mutually exclusive options, combine them into a single
    \l {Check Box} or a \l Switch.

    \uicontrol {Radio Delegate} is similar to radio button, except that it is
    typically used in views.

    \image qtquickcontrols2-radiodelegate.gif "Radio delegate"

    Select the \uicontrol Highlight check box in the \uicontrol {Radio Delegate}
    section to highlight the delegate.

    Recommendations for radio buttons:

    \list
        \li Limit the label text to one line.
        \li Ensure that a sensible default option is checked.
        \li List radio button options vertically.
        \li Keep the list short.
        \li In order to avoid confusion, do not put two groups of radio buttons
            next to each other.
    \endlist

    \section2 Switch

    You can create an instance of \uicontrol Components >
    \uicontrol {Qt Quick Controls} > \uicontrol Controls > \uicontrol Switch:

    \image qtquickcontrols2-switch.gif "Switch"

    \if defined(qtdesignstudio)
    Alternatively, you can use a wizard to create a \l{Creating Custom Controls}
    {custom switch}:

    \image studio-custom-switch.gif "Custom switch"
    \endif

    A switch is an option button that can be dragged or toggled on
    (checked) or off (unchecked). Switches are typically used to select between
    two states: \e on or \e off. For larger sets of options, such as those in a
    list, consider using \uicontrol {Switch Delegate} instead.

    \image qtquickcontrols2-switchdelegate.gif "Switch delegate"

    Select the \uicontrol Highlight check box in the \uicontrol {Item Delegate}
    section to highlight the delegate.

    Use a switch for binary operations that take effect immediately after the
    switch is toggled. For example, use a switch to turn WiFi on or off.

    \section2 Round Button

    \uicontrol {Round Button} is a clickable control that starts an action,
    or opens or closes a popup. A round button with a square image icon or
    one-letter font icon is circular. A circular round button takes less space
    than a normal button, and can also be used as a floating action button.

    In addition to common \l Button properties, a round button has the
    \uicontrol Radius property, which holds the radius of the button.

    \image qtquick-designer-round-button-properties.png "Round Button properties"

    To create a relatively square button that has slightly rounded corners, use
    a small value, such as 3.

    To create a completely circular button, use a value that is equal to half
    of the width or height of the button, and make the button's width and height
    identical.

    \section2 Displaying Text and Icons

    A button can contain text, an icon, or both. Specify the button text in
    the \uicontrol Text field in the \uicontrol {Button Content} section. The
    value of the \uicontrol Display field determines whether only text or an
    icon is displayed, or when both are visible, whether the text is placed
    beside the icon or under it.

    \image qtquick-designer-abstract-button-properties.png "Button Content properties"

    \section2 Checking Buttons

    A \e checkable button toggles between checked (on) and unchecked (off) when
    users click on it or press the space bar while the button has active
    focus. Select the \uicontrol Checkable check box to make a button checkable.
    To make the button checked, select the \uicontrol Checked check box.

    Buttons that belong to the same parent component can be mutually exclusive.
    Users can click a button to check it, and the previous selection is
    cleared. Users cannot uncheck the currently checked button by clicking
    it. Instead, they must click another button in the group to set the new
    checked button for that group.

    Radio buttons and tab buttons are mutually exclusive by default. To make
    other types of buttons mutually exclusive, select the \uicontrol Exclusive
    check box.

    If the buttons don't belong to the same parent, checking and unchecking
    buttons does not affect the other buttons in the group.

    \section2 Button Signals

    A button emits the \c clicked() signal when it is activated by users.
    \l{Connecting Components to Signals}{Connect to this signal} to perform
    the button's action. Buttons provide the following additional signals:
    \c canceled(), \c doubleClicked(), \c pressed(), \c released(), and
    \c pressAndHold() for long presses.

    Select the \uicontrol Auto-repeat check box to repeat the \c pressed(),
    \c released(), and \c clicked() signals while the button is pressed and
    held down. The \c pressAndHold() signal will not be emitted.

    \section1 Indicators

    Qt Quick Controls offer a selection of indicator-like controls, such as
    busy indicator, page indicator, and progress bar, for specific use cases.
    The following sections contain guidelines for choosing the indicator most
    suitable for a use case.

    \image qtquick-designer-indicator-types.png "Indicator types"

    \section2 Busy Indicator

    \image qtquickcontrols2-busyindicator.gif "Busy indicator"

    \uicontrol {Busy Indicator} indicates that an operation is in progress, and
    that the UI has to wait for the operation to complete.

    A busy indicator is similar to an indeterminate \l {Progress Bar}. Both can
    be used to indicate background activity. The main difference is visual and
    that a progress bar can also present a concrete amount of progress (when it
    can be determined). Due to the visual difference, busy indicators and
    indeterminate progress bars fit in different places in UIs.

    Select the \uicontrol Running check box to make the busy indicator visible.

    \image qtquick-properties-busy-indicator.png "Busy Indicator properties"

    Select the \uicontrol Live check box to provide live updates of the
    progress.

    Typical places for a busy indicator are:

    \list
        \li In the corner of a \uicontrol {Tool Bar}
        \li As an overlay on top of a \uicontrol Page
        \li On the side of an \uicontrol {Item Delegate}
    \endlist

    \section2 Page Indicator

    \uicontrol {Page Indicator} is used to indicate the currently active page
    in a container of multiple pages. Specify the number of pages in the
    \uicontrol Count field. Select the current page in the \uicontrol Current
    field.

    \image qtquick-properties-page-indicator.png "Page Indicator properties"

    Select the \uicontrol Interactive check box to determine that the page
    indicator reacts to presses and automatically changes the value of the
    \uicontrol Current field accordingly.

    Page indicators are typically quite small in order to avoid distracting the
    user from the actual content of the UI. Therefore, they can be hard to
    click, and might not be easily recognized as interactive by users. For these
    reasons, they are best used to complement primary methods of navigation,
    such as a \uicontrol {Swipe View}, not replace them.

    \target progress-bar-control
    \section2 Progress Bar

    \image qtquickcontrols2-progressbar.gif "Progress bar"

    \uicontrol {Progress Bar} indicates the progress of an operation. You
    can specify the initial value in the \uicontrol Value field, but it
    should be updated regularly. Specify the range in the \uicontrol From
    and \uicontrol To fields, which can both contain any value.

    \image qtquick-designer-progressbar-properties.png "Progress Bar properties"

    Select the \uicontrol Indeterminate check box when unable to determine the
    size of the item being downloaded, or if the download progress might get
    interrupted due to a network failure.

    \image qtquickcontrols2-progressbar-indeterminate.gif

    The indeterminate mode is similar to a \l {Busy Indicator} in that both can
    be used to indicate background activity. Due to their visual differences,
    indeterminate progress bars and busy indicators fit in different places in
    UIs.

    Typical places for an indeterminate progress bar are:

    \list
        \li At the bottom of a \uicontrol {Tool Bar}
        \li Inline within the content of a \uicontrol Page
        \li In an \uicontrol {Item Delegate} to show the progress
            of a particular item
    \endlist

    \section1 Selectors

    Qt Quick Controls offer a set of selector-like controls, such as sliders,
    dial, spin box, combo box, and tumbler, for specific use cases. The
    following sections contain guidelines for choosing the selector most
    suitable for a use case.

    \image qtquick-designer-selector-types.png "Selector types"

    \target slider-control
    \section2 Slider and Dial

    You can create an instance of \uicontrol Components >
    \uicontrol {Qt Quick Controls} > \uicontrol Controls > \uicontrol Slider:

    \image qtquickcontrols2-slider.gif "Slider"

    \if defined(qtdesignstudio)
    Alternatively, you can use a wizard to create a \l{Creating Custom Controls}
    {custom slider}:

    \image studio-custom-slider.gif "Custom slider"
    \endif

    A slider is used to select a value by sliding a handle along a
    track, whereas \uicontrol {Range Slider} is used to select a range
    specified by two values, by sliding each handle along a track.

    \image qtquickcontrols2-rangeslider.gif "Range slider"

    \uicontrol Dial is similar to a traditional dial knob that is found on
    devices such as stereos or industrial equipment. It allows users to
    specify a value within a range.

    You can create an instance of \uicontrol Components >
    \uicontrol {Qt Quick Controls} > \uicontrol Controls > \uicontrol Dial:

    \image qtquickcontrols2-dial-no-wrap.gif "Dial"

    \if defined(qtdesignstudio)
    Alternatively, you can use a wizard to create a \l{Creating Custom Controls}
    {custom dial}:

    \image studio-custom-dial.gif "Custom dial"
    \endif

    In the \uicontrol From and \uicontrol To fields, set the range of the
    slider or dial. Set the value of the slide handle or dial in the
    \uicontrol Value field. For a range slider, set the initial positions
    of the first and second handles in the \uicontrol {Value 1} and
    \uicontrol {Value 2} fields. Select the \uicontrol Live check box to
    provide live updates of the value properties.

    \image qtquick-designer-range-slider-properties.png "Range slider properties"

    In the \uicontrol {Snap mode} field, set how the slider handles or dial
    behave with regards to the value of the \uicontrol {Step size} field. By
    default, they do not snap to step size, but you can set them to snap to it
    either while being dragged or after being released.

    You can set slider orientation to horizontal or vertical in the
    \uicontrol Orientation field.

    Modify the \uicontrol {Drag threshold} to determine the threshold at
    which a touch drag event will be initiated.

    For more information, watch the following video:

    \youtube Ed8WS03C-Vk

    A dial supports circular, horizontal, and vertical input modes. For
    applications where fast input is important, the circular input mode is
    useful, as clicking the dial will move it directly to that position.
    For applications where precise input is important, the horizontal and
    vertical input modes are recommended, as these allow small adjustments to
    be made relative to where the dial is clicked. These modes are also better
    for dials where large jumps in values could be unsafe, such as a dial that
    controls audio volume. Set the input mode in the \uicontrol {Input mode}
    field.

    \image qtquick-designer-dial-properties.png "Dial properties"

    \section2 Spin Box

    You can create an instance of \uicontrol Components >
    \uicontrol {Qt Quick Controls} > \uicontrol Controls >
    \uicontrol {Spin Box}:

    \image qtquickcontrols2-spinbox.png "Spin Box"

    \if defined(qtdesignstudio)
    Alternatively, you can use a wizard to create a \l{Creating Custom Controls}
    {custom spin box}:

    \image studio-custom-spinbox.gif "Custom spin box"
    \endif

    A spin box enables users to choose an integer value by clicking
    the up or down indicator buttons, or by pressing up or down on the keyboard.
    Select the \uicontrol Editable check box to enable users to enter a text
    value in the input field.

    The other spin box properties are similar to those of a \uicontrol {Dial}.

    \section2 Combo Box

    \image qtquickcontrols2-combobox.gif "Combo box"

    \uicontrol {Combo Box} is a combined button and popup list. It provides a
    means of presenting a list of options to users in a way that takes up the
    minimum amount of screen space.

    A combo box is used to select a value from a static multiple-line drop-down
    list. Users cannot add new values, and only one option can be selected.

    Combo box values are provided by a \l{Lists and Other Data Models}
    {data model}. The data model is usually a JavaScript array, a \l ListModel,
    or an integer, but other types of data models are also supported.

    Select the \uicontrol Editable check box to auto-complete combo box text
    based on what is available in the model.

    \image qtquick-designer-combobox-properties.png "Combo box properties"

    When using models that have multiple named roles, specify the role of
    the \uicontrol {Display text} property in the \uicontrol {Text role} field.
    To use a role of the model item that corresponds to the text role, enter
    \c valueRole in the field.

    The \uicontrol {Current index} field contains the index of the item that
    is displayed in the combo box. The default value is \c -1 when the combo
    box is empty and \c 0 otherwise.

    A flat combo box does not draw a background unless it is
    interacted with, which makes it blend into the UI. Use flat combo
    boxes on a toolbar, for example, to match the flat look of tool
    buttons. To create a flat combo box, select the \uicontrol Flat
    check box.

    Recommendations for combo boxes:

    \list
        \li If the number of values is very large, consider applying a filter.
        \li If the number of values is small, consider using \l {Radio Button},
            so that users can see all options at the same time.
        \li Set a default value, which should be the value that you expect
            to be chosen most often.
    \endlist

    \section2 Tumbler

    \image qtquickcontrols2-tumbler-wrap.gif

    \uicontrol Tumbler allows users to select an option from a spinnable
    \e wheel of items. It is useful when there are too many options to use, for
    example, a \l {Radio Button}, and too few options to require the use of an
    editable \l {Spin Box}. It is convenient in that it requires no keyboard
    usage and wraps around at each end when there are a large number of items.

    Specify the number of visible options in the \uicontrol {Visible count}
    field. Select the index of the current option in the
    \uicontrol {Current index} field.

    \image qtquick-designer-tumbler-properties.png "Tumbler properties"

    To enable wrapping, select the \uicontrol Wrap check box.

    \section1 Tab Bar

    \image qtquickcontrols2-tabbar.gif "Tab Bar"

    \uicontrol {Tab Bar} provides a tab-based navigation model, where users
    can switch between different views or subtasks. A tab bar is commonly
    used as a header or footer of an \l ApplicationWindow. Select the toolbar
    position in the \uicontrol Position field.

    Typically, a tab bar contains a static set of \uicontrol {Tab Button}
    controls that are defined as its children. The \uicontrol {Current index}
    field in the \uicontrol Container section shows the index of the current
    tab button. The default value is \c -1 when the tab bar is empty and
    \c 0 otherwise.

    \image qtquick-designer-tabbar-properties.png "Tab Bar properties"

    You can specify content width (\uicontrol W) and height (\uicontrol H)
    in the \uicontrol {Content size} field.

    If the total width of the buttons exceeds the available width of the tab
    bar, it automatically becomes \l{Flickable}{flickable}.

    \image qtquickcontrols2-tabbar-flickable.png

    \section1 Tool Bar

    \image qtquickcontrols2-toolbar.png

    \uicontrol {Tool Bar} contains application-wide and context-sensitive
    actions and controls, such as navigation buttons and search fields. A
    toolbar is commonly used as a header or footer of an \l ApplicationWindow.
    Select the toolbar position in the \uicontrol Position field.

    \image qtquick-designer-toolbar-properties.png "Tool Bar properties"

    \uicontrol {Tool Button} is nearly identical to \l Button, but it has a
    graphical appearance that makes it more suitable for insertion into a
    toolbar.

    A toolbar does not provide a layout of its own, but requires you to
    position its contents, for instance by creating a \l RowLayout. If the
    toolbar contains only one item, it will resize to fit the implicit item
    size. This makes a toolbar particularly suitable for use together with
    \l{Using Layouts}{layouts}. However, you can specify content width
    (\uicontrol W) and height (\uicontrol H) in the \uicontrol {Content size}
    field in the \uicontrol Pane section.

    \uicontrol {Tool Separator} is used to visually distinguish between
    groups of items on a toolbar by separating them with a line. It can
    be used in horizontal or vertical toolbars by setting the value of
    the \uicontrol Orientation field.

    \section1 Styling Controls

    The preset UI controls can be \l {Styling Qt Quick Controls}{styled}.
    The \uicontrol {2D} view reads the preferred style from a
    configuration file (\c qtquickcontrols2.conf). To change the
    style, select another style from the list on the main toolbar. This
    enables you to check how your UI looks when using the available
    styles.

    \image qtquick-designer-style-list.png "Style menu on the toolbar"

    For an example of defining your own style and using it in the Design mode,
    see \l {Qt Quick Controls 2 - Flat Style}.

    For more information about how to customize a particular control, see
    \l{Customization Reference}.

    \if defined(qtcreator)
    \section1 History of Qt Quick Controls

    In Qt 4, ready-made Qt Quick 1 Components were provided for creating
    UIs with a native look and feel for a particular target platform.
    In Qt 5.1, Qt Quick Controls, Dialogs, and Layouts were added for
    creating classic desktop-style user interfaces using Qt Quick 2.1. The
    Qt Quick Controls Styles could be used to customize Qt Quick Controls.

    Since Qt 5.7, \l {Qt Quick Controls 2} replace Qt Quick Controls 1 and
    Qt Labs Controls. They provide lightweight components for creating performant
    user interfaces for \l{glossary-device}{devices}.

    Qt Quick Controls 2 work in conjunction with Qt Quick and Qt Quick Layouts.

    The \QC project wizards create Qt Quick applications that use Qt Quick
    2 types or Qt Quick Controls 2 types.

    Even if you use Qt Quick Controls 2, you can still write cross-platform
    applications, by using different sets of QML files for each platform.

    Some ready-made controls, such as a gauge, dial, status indicator, and
    tumbler, are provided by the \l {Qt Quick Extras} module.
    \endif

    \section1 Summary of UI Controls

    The following table lists preset UI controls with links to their developer
    documentation. They are available in \uicontrol Components >
    \uicontrol {Qt Quick Controls}. The \e MCU column indicates which controls
    are supported on MCUs.

    \table
    \header
        \li Icon
        \li Name
        \li MCU
        \li Purpose
    \row
        \li \inlineimage icons/busyindicator-icon16.png
        \li \l [QtQuickControls]{BusyIndicator}{Busy Indicator}
        \li
        \li Indicates activity while content is being loaded.
    \row
        \li \inlineimage icons/button-icon16.png
        \li \l [QtQuickControls]{Button}
        \li \inlineimage ok.png
        \li A push button that you can associate with an action.
    \row
        \li \inlineimage icons/checkbox-icon16.png
        \li \l [QtQuickControls]{CheckBox}{Check Box}
        \li \inlineimage ok.png
        \li An option button that can be toggled on (checked) or off
            (unchecked).
    \row
        \li \inlineimage icons/checkbox-icon16.png
        \li \l [QtQuickControls]{CheckDelegate}{Check Delegate}
        \li
        \li An item delegate that can be toggled on (checked) or off
            (unchecked).
    \row
        \li \inlineimage icons/combobox-icon16.png
        \li \l [QtQuickControls]{ComboBox}{Combo Box}
        \li
        \li A combined button and popup list that is populated by using a data
            model.
    \row
        \li \inlineimage icons/delaybutton-icon16.png
        \li \l [QtQuickControls]{DelayButton}{Delay Button}
        \li
        \li An option button that is triggered when held down long enough.
    \row
        \li \inlineimage icons/dial-icon16.png
        \li \l [QtQuickControls]{Dial}
        \li \inlineimage ok.png
        \li A circular dial that is rotated to set a value.
    \row
        \li \inlineimage icons/pageindicator-icon16.png
        \li \l [QtQuickControls]{PageIndicator}{Page Indicator}
        \li
        \li Indicates the indicate the currently active page in a container of
            multiple pages.
    \row
        \li \inlineimage icons/progressbar-icon16.png
        \li \l [QtQuickControls]{ProgressBar}{Progress Bar}
        \li \inlineimage ok.png
        \li Indicates the progress of an operation.
    \row
        \li \inlineimage icons/radiobutton-icon16.png
        \li \l [QtQuickControls]{RadioButton}{Radio Button}
        \li \inlineimage ok.png
        \li An option button that can be switched on (checked) or off
            (unchecked).
    \row
        \li \inlineimage icons/radiobutton-icon16.png
        \li \l [QtQuickControls]{RadioDelegate}{Radio Delegate}
        \li
        \li An item delegate that can be toggled on (checked) or off
            (unchecked).
    \row
        \li \inlineimage icons/rangeslider-icon16.png
        \li \l [QtQuickControls]{RangeSlider}{Range Slider}
        \li
        \li Enables users to select a range of values by sliding two handles
            along a track.
    \row
        \li \inlineimage icons/roundbutton-icon16.png
        \li \l [QtQuickControls]{RoundButton}{Round Button}
        \li
        \li A push button with rounded corners that you can associate with an
            action.
    \row
        \li \inlineimage icons/slider-icon16.png
        \li \l [QtQuickControls]{Slider}
        \li \inlineimage ok.png
        \li Enables users to select a value by sliding a handle along a track.
    \row
        \li \inlineimage icons/spinbox-icon16.png
        \li \l [QtQuickControls]{SpinBox}{Spin Box}
        \li
        \li Enables users to specify a value by clicking the up or down buttons,
            by pressing up or down on the keyboard, or by entering a value in
            the box.
    \row
        \li \inlineimage icons/switch-icon16.png
        \li \l [QtQuickControls]{Switch}
        \li \inlineimage ok.png
        \li An option button that can be toggled on or off.
    \row
        \li \inlineimage icons/switch-icon16.png
        \li \l [QtQuickControls]{SwitchDelegate}{Switch Delegate}
        \li
        \li An item delegate with a switch indicator that can be toggled on or
            off.
    \row
        \li \inlineimage icons/toolbar-icon16.png
        \li \l [QtQuickControls] {TabBar}{Tab Bar}
        \li
        \li Enables users to switch between different views or subtasks.
    \row
        \li \inlineimage icons/toolbutton-icon16.png
        \li \l [QtQuickControls]{TabButton}{Tab Button}
        \li
        \li A button that is functionally similar to \uicontrol Button, but
            provides a look that is more suitable for a \uicontrol {Tab Bar}.
    \row
        \li \inlineimage icons/toolbar-icon16.png
        \li \l [QtQuickControls]{ToolBar}{Tool Bar}
        \li
        \li A container of application-wide and context sensitive actions and
            controls, such as navigation buttons and search fields.
    \row
        \li \inlineimage icons/toolbutton-icon16.png
        \li \l [QtQuickControls]{ToolButton}{Tool Button}
        \li
        \li A button that is functionally similar to \uicontrol Button, but
            provides a look that is more suitable for a \uicontrol {Tool Bar}.
    \row
        \li \inlineimage icons/toolseparator-icon16.png
        \li \l [QtQuickControls]{ToolSeparator}{Tool Separator}
        \li
        \li Separates a group of items from adjacent items on a
            \uicontrol {Tool Bar}.
    \row
        \li \inlineimage icons/tumbler-icon16.png
        \li \l [QtQuickControls]{Tumbler}
        \li
        \li A spinnable wheel of items that can be selected.
    \endtable
*/
