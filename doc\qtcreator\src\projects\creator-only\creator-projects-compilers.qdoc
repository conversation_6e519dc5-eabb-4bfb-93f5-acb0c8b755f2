// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-qmake.html
    \page creator-tool-chains.html
    \nextpage creator-debuggers.html

    \title Adding Compilers

    Qt is supported on a variety of 32-bit and 64-bit platforms, and can
    usually be built on each platform with GCC, a vendor-supplied compiler, or
    a third party compiler. In \QC, a \l{glossary-buildandrun-kit}{kit}
    specifies the compiler and other necessary tools for building an application
    for and running it on a particular platform.

    \QC automatically detects the compilers that your system or the Qt Installer
    registers and lists them in \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Kits > \uicontrol Compilers.

    \image qtcreator-toolchains.png

    \note On \macos, the \c ccache C/C++ compiler cache is detected
    automatically only if you installed it using Homebrew or MacPorts.
    When using MacPorts, you also need to create symlinks, as instructed in
    \l{https://trac.macports.org/wiki/howto/ccache}{How to enable ccache}
    in the MacPorts wiki.

    You can add the following compilers to build applications by using other
    compilers or by using additional versions of the automatically detected
    compilers:

    \list

        \li Clang is a C, C++, Objective C, and Objective C++ front-end for the
            LLVM compiler for Windows, Linux, and \macos.

        \li \l{https://clang.llvm.org/docs/UsersManual.html#clang-cl}{clang-cl}
            is an alternative command-line interface to Clang that is compatible
            with the Visual C++ compiler, \c cl.exe.

        \li GNU Compiler Collection (GCC) is a compiler for Linux and
            \macos.

        \li ICC (Intel C++ Compiler) is a group of C and C++ compilers.
            Only the GCC-compatible variant, available for Linux and \macos,
            is currently supported by \QC.

        \li \MinGW (Minimalist GNU for Windows) is a native software port of GCC
            and GNU Binutils for use in the development of native Microsoft
            Windows applications on Windows. \MinGW is distributed together with
            \QC and Qt for Windows.

        \li MSVC (Microsoft Visual C++ Compiler) is a C++ compiler that is
            installed with Microsoft Visual Studio.

        \li Nim is the Nim Compiler for Windows, Linux, and \macos.

        \li QCC is the interface for compiling C++ applications for QNX.

    \endlist

    In addition, the \QC Bare Metal Device plugin provides support for the
    following compilers:

    \list

        \li \l{https://www.iar.com/iar-embedded-workbench/}{IAREW} is a group of
            C and C++ bare-metal compilers from the various IAR Embedded Workbench
            development environments.
            Currently supported architectures are \c 8051, \c AVR, \c ARM,
            \c STM8, and \c MSP430.

        \li \l{https://www.keil.com}{KEIL} is a group of C and C++ bare-metal
            compilers from the various KEIL development environments.
            Currently supported architectures are \c 8051 and \c ARM.

        \li \l{http://sdcc.sourceforge.net}{SDCC} is an optimizing
            C bare-metal compiler for various architectures.
            Currently supported architectures are \c 8051 and \c STM8.

    \endlist

    The emscripten compiler is tool chain for compiling to
    \l{Building Applications for the Web}{WebAssembly}.

    \section1 Re-detecting Compilers

    When \QC finds an x86_64 GCC compiler, it sets up an instance for the native
    x86_64 target. If you plan to create also 32-bit x86 binaries without using
    a dedicated cross-compiler, select \uicontrol {Auto-detection Settings} >
    \uicontrol {Detect x86_64 GCC compilers as x86_64 and x86}. Then select
    \uicontrol Re-detect to refresh the list of automatically detected
    compilers.

    To remove manually added compilers, select \uicontrol Remove or
    \uicontrol {Remove All}.

    \section1 Specifying Compiler Settings

    To build an application using GCC, \MinGW, Clang, or QCC, specify the path
    to the directory where the compiler is located and select
    the application binary interface (ABI) version from the list of available
    versions. You can also create a custom ABI definition.
    For QCC, also specify the path to the QNX Software Development Platform
    (SDP) in the \uicontrol {SPD path} field.

    To enable Microsoft Visual C++ Compilers (MSVC) and clang-cl to find system
    headers, libraries, and the linker, \QC executes them inside a command
    prompt where you set up the environment using \c {vcvarsall.bat}. For
    these compilers, you also specify the path to the script that sets up the
    command prompt in the \uicontrol Initialization field.

    You specify the compiler to use for each kit in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Kits.

    To add a C or C++ compiler, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Kits > \uicontrol Compilers > \uicontrol Add. Select a compiler
    in the list, and then select \uicontrol C or \uicontrol C++.

    To clone the selected compiler, select \uicontrol Clone.

    The settings to specify depend on the compiler:

    \list

        \li In the \uicontrol Name field, enter a name for the compiler to
            identify it in \QC.

            \image qtcreator-options-cpp-compilers.png "Adding a clang-cl compiler"

        \li In the \uicontrol Initialization field, select the
            \c {vcvarsall.bat} file for setting up the command
            prompt to use.

        \li In the \uicontrol {Compiler path} field, enter the path to the
            directory where the compiler is located.

        \li In the \uicontrol {Platform codegen flags} field, check the flags passed
            to the compiler that specify the architecture on the target
            platform.

        \li In the \uicontrol {Platform linker flags} field, check the flags passed to
            the linker that specify the architecture on the target platform.
            The linker flags are used only when building with Qbs.

            \image qtcreator-options-clang-compilers.png "Adding a Clang compiler"

        \li In the \uicontrol {Parent toolchain} field, select a \MinGW
            compiler, which is needed because Clang does not have its own
            standard library.

        \li In the \uicontrol {SPD path} field, specify the path to the QNX
            Software Development Platform (SDP).

            \image qtcreator-options-qcc-compilers.png "Adding a QCC compiler"

        \li In the \uicontrol ABI field, provide an identification for the
            target architecture. This is used to warn about ABI mismatches
            within the kits.

        \li In the \uicontrol {Target triple} field, specify the GCC target
            architecture. If services provided by the code model fail because
            Clang does not understand the target architecture, select
            \uicontrol {Override for code model}.

            \image qtcreator-compilers-target-triple.png "Target triple field"

    \endlist

    \section1 Adding Nim Compilers

    To build an application using the Nim Compiler, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Kits > \uicontrol Compilers >
    \uicontrol Add > \uicontrol Nim, and specify the path to the directory where
    the compiler is located.

    \section1 Adding Custom Compilers

    To add a compiler that is not listed above or a remote compiler, use the
    \uicontrol Custom option and specify the paths to the directories where the
    compiler and make tool are located and options for the compiler.

    \image creator-compilers-custom.png

    To add other compilers:

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Kits >
            \uicontrol Compilers > \uicontrol Add > \uicontrol Custom >
            \uicontrol C or \uicontrol C++.

        \li In the \uicontrol Name field, enter a name for the compiler.

        \li In the \uicontrol {Compiler path} field, enter the path to the directory
            where the compiler is located.

        \li In the \uicontrol {Make path} field, enter the path to the directory where
            the make tool is located.

       \li  In the \uicontrol ABI field, specify the ABI version.

        \li In the \uicontrol {Predefined macros} field, specify the macros that the
            compiler enables by default. Specify each macro on a separate line,
            in the following format: MACRO[=value].

        \li In the \uicontrol {Header paths} field, specify the paths to directories
            that the compiler checks for headers. Specify each path on a
            separate line.

        \li In the \uicontrol {C++11 flags} field, specify the flags that turn on
            C++11 support in the compiler.

        \li In the \uicontrol {Qt mkspecs} field, specify the path to the directory
            where mkspecs are located. Usually, the path is specified relative
            to the Qt mkspecs directory.

        \li In the \uicontrol {Error parser} field, select the error parser to use.
            You can add custom output parsers to the list. For more information,
            see \l{Using Custom Output Parsers}.
    \endlist

    \section1 Troubleshooting \MinGW Compilation Errors

    If error messages displayed in \l {Compile Output} contain
    paths where slashes are missing (for example, \c {C:QtSDK}),
    check your PATH variable. At the command line, enter the following commands:

    \code
        where sh.exe
        where make.exe
        where mingw32-make.exe
    \endcode

    If these commands show paths, they have been added to the global PATH
    variable during the installation of a tool chain based on Cygwin or \MinGW,
    even though this is against Windows conventions.

    To keep working with the third-party tool chain, create a new shell link
    that adds the required paths (as Visual Studio and Qt do). The shell link
    must point to cmd.exe:

    \c {C:\Windows\System32\cmd.exe /K C:\path_to\myenv.bat}

    where the /K parameter carries out the command specified in the bat file.

    Create the myenv.bat file at \e path_to, which should be in a convenient
    location. In the file, specify the paths to the tool chains. For example,

    \c  {set PATH=C:\path1;C:\path2;%PATH%}

    where \e path1 and \e path2 are paths to the tool chains.

    Finally, remove the paths from the global PATH, reboot the computer, and
    run the \c where commands again to verify that the global PATH is now clean.

    You can use the shell link to run the tools in the third-party tool chains.

*/
