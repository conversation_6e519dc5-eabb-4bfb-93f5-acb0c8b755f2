// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage adding-plugins.html
    \page creator-usability.html
    \nextpage creator-coding.html

    \title Optimizing Applications for Mobile Devices

    Before starting application development, analyze and define the
    requirements, scope, and functionality of the application to ensure
    efficient functionality and a smooth user experience. Design the application
    for a single purpose and analyze how it can best serve its users.

    The following guidelines help you design and develop usable applications for
    mobile devices with varying characteristics, such as screen size and support
    for input methods:

    \list

        \li Know your users

            Find out who will use the application, what they will use it for,
            and which mobile devices they have. Then design the application to
            fit a specific context of use.

        \li Design for small screens

            The screen size of mobile devices is significantly smaller than that
            available on desktop devices. Carefully consider what is the most
            relevant content to present on the application UI, as it might not
            be reasonable to try and fit as much content into the screen as you
            might have in a desktop application.

        \li Design for multiple screen sizes

            Relate the position and size of each control to the dimensions of
            the display. This enables the same set of information to be
            presented on the screen in all resolutions; higher resolution
            devices just display finer graphics.

        \li Design for changing screen orientation

            Some devices support screen rotation. On these devices, applications
            can be displayed in portrait or landscape orientation. Account for
            orientation and dynamically adjust the display when the screen is
            rotated.

        \li Design intuitive ways of moving within applications

            Mobile devices lack a mouse and full-size keyboard, so users must
            use the touch screen or five way navigation pad to move within
            applications. In addition, many users control the devices with one
            hand. To create an optimized user experience, allow users to access
            information with one click; do not make them scroll and type.

        \li Design for limited input methods

            Applications collect information from users on the task at hand. In
            addition to touch screen input, some devices contain physical keys
            such as a five way navigation pad, a keypad, and a keyboard. Users
            enter information by using screen controls, such as lists, check
            boxes, radio buttons, and text fields.

        \li Keep response times short

            Latency can cause delays in user interaction. If users perceive
            an application as being slow, they are likely to get frustrated and
            stop using it.

        \li Save battery time

            Mobile devices are not constantly connected to a power source but
            run on battery power. Optimize power consumption to keep the total
            consumption at an acceptable level and to prevent users from running
            out of battery time.

        \li Consider network issues

            If users do not have a flat-rate data plan or WLAN support, mobile
            network connections cost them money. Also, when users move around
            with the devices, the networks available for connections constantly
            change.

        \li Remember the processing limits of the device

            The memory available on devices is limited and you should use it
            carefully. Although all mobile devices have common functionality,
            each device is individual in terms of both the resources available
            and extra features. Therefore, you must consider the constraints of
            all the target devices.

    \endlist

 */
