type: Group
instructions:
  - type: MakeDirectory
    directory: ".git"
  - type: SetBuildDirectory
    directory: "{{.SourceDir}}"
    disable_if:
      condition: property
      property: features
      contains_value: OutOfSourceBuild
  - type: Group
    instructions:
      - type: SetBuildDirectory
        directory: "{{.AgentWorkingDir}}/build"
      - type: MakeDirectory
        directory: "{{.BuildDir}}"
  - type: ChangeDirectory
    directory: "{{.BuildDir}}"
  - type: InstallSourceArchive
    maxTimeInSeconds: 600
    maxTimeBetweenOutput: 600
    project: qtsdk/qtsdk
    ref: master
    directory: "build/qtsdk"
    userMessageOnFailure: "Failed to install qtsdk, check logs"
  - type: ExecuteCommand
    command: "python3 -m pip install pipenv==2022.4.8 --user"
    maxTimeInSeconds: 1200
    maxTimeBetweenOutput: 120
    userMessageOnFailure: "Failed to install Pipenv"
    enable_if:
      condition: property
      property: host.os
      in_values: [MacOS, Linux]
  - type: ExecuteCommand
    command: "python -m pip install pipenv==2022.4.8 --user"
    maxTimeInSeconds: 1200
    maxTimeBetweenOutput: 120
    userMessageOnFailure: "Failed to install Pipenv"
    enable_if:
      condition: property
      property: host.os
      equals_value: Windows
  - type: ChangeDirectory
    directory: "{{.AgentWorkingDir}}/build/qtsdk/packaging-tools"
  - type: ExecuteCommand
    command: "python3 -m pipenv install --skip-lock"
    maxTimeInSeconds: 1200
    maxTimeBetweenOutput: 120
    userMessageOnFailure: "Failed to install requirements from Pipfile"
    enable_if:
      condition: property
      property: host.os
      in_values: [MacOS, Linux]
  - type: ExecuteCommand
    command: "python -m pipenv install --skip-lock"
    maxTimeInSeconds: 1200
    maxTimeBetweenOutput: 120
    userMessageOnFailure: "Failed to install requirements from Pipfile"
    enable_if:
      condition: property
      property: host.os
      equals_value: Windows
  - type: ExecuteCommand
    command: "python3 -m pipenv run python -u install_qt.py --qt-path {{.BuildDir}}/qt_install_dir --base-url {{.Env.QTC_QT_BASE_URL}} --base-url-postfix={{.Env.QTC_QT_POSTFIX}} --icu7z http://master.qt.io/development_releases/prebuilt/icu/prebuilt/56.1/icu-linux-g++-Rhel7.2-x64.7z {{.Env.QTC_QT_MODULES}}"
    executeCommandArgumentSplitingBehavior: SplitAfterVariableSubstitution
    maxTimeInSeconds: 3600
    maxTimeBetweenOutput: 360
    userMessageOnFailure: "Failed to install qt, check logs."
    enable_if:
      condition: property
      property: host.os
      equals_value: Linux
  - type: ExecuteCommand
    command: "python3 -m pipenv run python -u install_qt.py --qt-path {{.BuildDir}}/qt_install_dir --base-url {{.Env.QTC_QT_BASE_URL}} --base-url-postfix={{.Env.QTC_QT_POSTFIX}} {{.Env.QTC_QT_MODULES}}"
    executeCommandArgumentSplitingBehavior: SplitAfterVariableSubstitution
    maxTimeInSeconds: 3600
    maxTimeBetweenOutput: 360
    userMessageOnFailure: "Failed to install qt, check logs."
    enable_if:
      condition: property
      property: host.os
      equals_value: MacOS
  - type: ExecuteCommand
    command: "python -m pipenv run python -u install_qt.py --qt-path {{.BuildDir}}/qt_install_dir --base-url {{.Env.QTC_QT_BASE_URL}} --base-url-postfix={{.Env.QTC_QT_POSTFIX}} --opengl32sw7z http://master.qt.io/development_releases/prebuilt/llvmpipe/windows/opengl32sw-64.7z --d3dcompiler7z http://master.qt.io/development_releases/prebuilt/d3dcompiler/msvc2013/d3dcompiler_47-x64.7z --openssl7z http://ci-files02-hki.intra.qt.io/packages/jenkins/openssl/openssl_1.1.1d_prebuild_x64.7z {{.Env.QTC_QT_MODULES}}"
    executeCommandArgumentSplitingBehavior: SplitAfterVariableSubstitution
    maxTimeInSeconds: 3600
    maxTimeBetweenOutput: 360
    userMessageOnFailure: "Failed to install qt, check logs."
    enable_if:
      condition: and
      conditions:
        - condition: property
          property: host.os
          equals_value: Windows
        - condition: property
          property: target.arch
          equals_value: X86_64

enable_if:
  condition: property
  property: features
  not_contains_value: "Qt5"
