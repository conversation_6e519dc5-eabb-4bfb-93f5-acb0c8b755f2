// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-running-targets.html
    \page creator-deployment.html
    \nextpage creator-deploying-android.html

    \title Deploying to Devices

    Deploy configurations in the \uicontrol Projects mode
    \uicontrol {Run Settings} handle the packaging of the application as an
    executable and copying it to a location you want to run the executable at.
    The files can be copied to a location in the file system of the development
    PC or a \l{glossary-device}{device}.

    \list

        \li \l{Deploying to Android}

            When you deploy the application to an Android device, \QC copies
            the application files to the device. In addition, you can determine
            the Qt libraries to use.

        \li \l {Deploying to Boot2Qt}

            When you deploy the application to a Boot2Qt device, \QC copies
            the application files to the connected device. You can then test
            and debug the application on the device with \QC.

        \li  \l{Deploying to QNX Neutrino}

            When you deploy the application to a QNX Neutrino device, \QC copies
            the application files to the connected device. You can then test and
            debug the application on the device with \QC.

        \li \l{Deploying to Remote Linux}

            When you deploy the application to a generic Linux-based device, \QC
            copies the application files to the connected device. You can then
            test and debug the application on the device with \QC.
    \endlist
*/
