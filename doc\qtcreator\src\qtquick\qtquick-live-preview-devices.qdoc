// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-live-preview-desktop.html
    \page creator-live-preview-devices.html
    \if defined(qtdesignstudio)
    \nextpage creator-live-preview-android.html
    \else
    \nextpage qt-design-viewer.html
    \endif
    \title Previewing on Devices

    To preview UIs on Android devices, you need to enable USB debugging on them
    and connect them to your system with a USB cable.

    To preview UIs on Boot2Qt devices, you need to connect the devices to your
    system with a USB cable, or a wired or wireless connection, depending on
    the device, and configure connections to them. The necessary kits have been
    predefined and you only need to enable them for your current project.

    \e {Deploy configurations} handle the packaging and copying of the
    necessary files to a location in a device where you want to run the
    executable at.

    To preview a UI on a device:

    \list 1
        \li In \uicontrol Projects > \uicontrol {Build & Run}, enable the kit
            predefined for the device type (1).
        \li Select the kit for the device in the kit selector (2).
            \if defined(qtcreator)
            \image qtcreator-live-preview-kit.png
            \else
            \image studio-kit-selector.png "Kit selector"
            \endif
        \li Select \uicontrol Build > \uicontrol {QML Preview} or
            press \key {Alt+P}.
    \endlist

    \section2 Previewing on Android Devices

    The USB debugging feature on Android devices enables creating connections
    to the devices from \QDS and running the preview utility on them.

    Debugging is enabled in different ways on different Android devices.
    Look for \uicontrol {USB Debugging} under \uicontrol {Developer Options}.
    On some devices \uicontrol {Developer Options} is hidden and becomes visible
    when you tap the \uicontrol {Build number} field in \uicontrol Settings >
    \uicontrol About several times.

    After you have enabled debugging, connect the Android device to the system
    with a USB cable.

    The first time you preview a UI on devices, the preview utility
    is copied to them. This might take some time. Thereafter, previewing will
    get faster because only the UI files need to be copied to the
    device.

    \section2 Previewing on Boot2Qt Devices

    You can preview UIs on Boot2Qt devices. For a list of supported devices, see
    \l{https://doc.qt.io/Boot2Qt/qtdc-supported-platforms.html}
    {Boot2Qt: Supported Target Devices and Development Hosts}.

    You must configure the device as instructed in the
    \l{https://doc.qt.io/Boot2Qt/b2qt-installation-guides.html}
    {Boot2Qt: Installation Guides}.

    \note At the time of this writing, \macos is not supported as a development
    host for Boot2Qt. This means that you cannot preview UIs on
    devices if you are using \QC on \macos. For more information, see
    \l {https://doc.qt.io/Boot2Qt/qtdc-supported-platforms.html#supported-development-hosts}
    {Boot2Qt: Supported Development Hosts}.
*/
