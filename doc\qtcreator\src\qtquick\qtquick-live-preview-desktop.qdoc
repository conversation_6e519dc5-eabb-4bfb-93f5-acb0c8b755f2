// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-live-preview.html
    \page creator-live-preview-desktop.html
    \nextpage creator-live-preview-devices.html

    \title Previewing on Desktop

    \if defined(qtcreator)
    To preview the currently active QML file on the desktop, select
    \uicontrol Build > \uicontrol {QML Preview}.

    \image qtcreator-live-preview.png
    \else
    To preview the currently active QML file on the desktop:

    \list
        \li Select \uicontrol Build > \uicontrol {QML Preview}.
        \li Select the \inlineimage icons/live_preview.png
            (\uicontrol {Show Live Preview}) button.
        \li Press \key {Alt+P}.
    \endlist

    \image studio-live-preview.png
    \endif

    To preview any QML file that belongs to the project, right-click the project
    name in the \l Projects view, and select \uicontrol {Preview File}.

    \if defined(qtdesignstudio)
    To preview the whole UI, select \uicontrol {Show Live Preview}
    when viewing the main QML file of the project.

    To view the UI in different sizes, select the zooming level on the toolbar.

    The frames-per-second (FPS) refresh rate of animations is displayed in the
    \uicontrol FPS field.

    \section1 Selecting the Preview Tool

    By default, the QML runtime is used for previewing. To use some
    other tool, specify it in the \uicontrol {QML viewer} field in the run
    settings of the project in the Projects mode.

    \image studio-run-settings.png "Run settings"
    \endif
*/
