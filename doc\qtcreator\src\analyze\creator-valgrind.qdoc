// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-valgrind-overview.html
    \page creator-analyzer.html
    \nextpage creator-cache-profiler.html

    \title Detecting Memory Leaks with Memcheck

    You can use the Memcheck tool included in the Valgrind tool suite to detect
    problems that are related to memory management in applications. You can use
    the tool together with the GDB debugger. When a problem is detected, the
    application is interrupted and you can debug it.

    \note You can install and run Memcheck locally on Linux. You can run
    it on a remote host or device from any development machine. On Windows,
    you can use the \l{Detecting Memory Leaks with Heob}{Heob} heap observer to
    receive similar results.

    After you download and install Valgrind tools, you can use Memcheck from
    \QC.

    To analyze applications:

    \list 1

        \li In the \uicontrol Projects mode, select a debug build configuration.

        \li Select \uicontrol Debug to open the \uicontrol Debug mode, and then
            select \uicontrol Memcheck on the toolbar.

        \li Select the
            \inlineimage icons/qtcreator-analyze-start-button.png "Start button"
            button to start the application.

        \li Use the application to analyze it.

        \li Select the \inlineimage icons/stop_small.png "Stop button"
            button to view the results of the analysis in the
            \uicontrol {Analysis} view.

    \endlist

    While the application is running, Memcheck checks all reads and writes of
    memory and intercepts calls that allocate or free memory or create or
    delete memory blocks. The results are displayed when you stop Memcheck.
    Click a line to view where a memory leak
    occurred and a stack trace that shows what caused it.

    As an alternative to collecting data, you can select \inlineimage icons/open.png
    to load an external log file in XML format into the \uicontrol Memcheck
    view.

    \image qtcreator-valgrind-memcheck.png "Memcheck view"

    Move the mouse on a row to view more information about the function.

    To move between rows, select \inlineimage icons/prev.png
    or \inlineimage icons/next.png
    .

    To filter the results, select \inlineimage icons/filtericon.png
    , and then select the types of issues to display in the view. You
    can view and hide definite and possible memory leaks, uninitialized
    values, invalid calls to \c free(), and external errors.

    For more information about using Memcheck, see
    \l{http://valgrind.org/docs/manual/quick-start.html#quick-start.mcrun}
    {Interpreting Memcheck's Output} in the Valgrind documentation.

    \section1 Selecting Options for Memory Analysis

    You can specify analyzer settings either globally for all projects or
    separately for each project in the \l{Specifying Run Settings}{run settings}
    of the project.

    To specify global settings for Valgrind, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Analyzer. The \uicontrol
    {Memcheck Memory Analysis Options} group contains Memcheck options.

    In \uicontrol {Extra Memcheck arguments}, specify additional arguments
    for launching the executable.

    Stack traces can get quite large and confusing, and therefore, reading them
    from the bottom up can help. If the stack trace is not big enough or it is
    too big, select \uicontrol Edit > \uicontrol Preferences > \uicontrol Analyzer
    and define the length of the stack trace in the
    \uicontrol {Backtrace frame count} field.

    \image qtcreator-valgrind-memcheck-options.png "Memory Analysis options"

    Memcheck also reports uses of uninitialised values, most commonly with the
    message \uicontrol {Conditional jump or move depends on uninitialised value(s).}
    To determine the root cause of these errors, the \uicontrol {Track origins of
    uninitialized memory} check box is selected by default. You can deselect it
    to make Memcheck run faster.

    Memcheck searches for memory leaks when the client application finishes. To
    view the amount of leaks that occurred, select \uicontrol {Summary Only} in the
    \uicontrol {Check for leaks on finish} field. To also view details of each leak,
    select \uicontrol Full.

    \section2 Showing Reachable and Indirectly Lost Blocks

    \e Reachable blocks are blocks that are pointed at by a pointer or chain
    of pointers and that might have been freed before the application exited.
    \e {Indirectly lost} blocks are considered lost because all the blocks that
    point to them are themselves lost. For example, all the children of a lost
    root node are indirectly lost.

    By default, Memcheck does not report reachable and indirectly lost blocks.
    To have them reported, select the \uicontrol {Show reachable and indirectly lost
    blocks}.

    \section2 Suppressing Errors

    Memcheck detects numerous problems in the system libraries, such as the C
    library, which come pre-installed with your OS. As you cannot easily fix
    them, you want to suppress them. Valgrind reads a list of errors to suppress
    at startup. A default suppression file is created by the ./configure script
    when the system is built.

    You can write your own suppression files if parts of your project contain
    errors you cannot fix and you do not want to be reminded of them. Click
    \uicontrol Add in the \uicontrol {MemCheck Memory Analysis} dialog to add
    the suppression files.
    For more information about writing suppression files, see
    \l{http://valgrind.org/docs/manual/manual-core.html#manual-core.suppress}
    {Suppressing Errors} in the Valgrind documentation.

*/

/*!
    \previouspage creator-analyzer.html
    \page creator-cache-profiler.html
    \nextpage creator-running-valgrind-remotely.html

    \title Profiling Function Execution

    You can use the Callgrind tool included in the
    \l{http://valgrind.org/info/tools.html}{Valgrind tool suite} to detect
    problems that are related to executing functions. In addition, you
    can load the data files generated by Callgrind into the
    \l{https://kcachegrind.github.io/html/Home.html}{KCachegrind}
    profile data visualization tool for browsing the performance results.

    After you download and install Valgrind tools and KCachegrind, you can use
    Callgrind and KCachegrind from \QC.

    \note You can install and run Callgrind and KCachegrind locally on Linux.
    You can run Callgrind on a remote Linux machine or device from any
    development machine.

    \section1 Building Apps for Profiling

    Callgrind records the call history of functions that are executed when the
    application is run. It collects the number of instructions that are
    executed, their relationship to source lines, the relationships of the
    caller and callee between functions, and the numbers of such calls. You can
    also use cache simulation or branch prediction to gather information about
    the runtime behavior of an application.

    Since the run-time characteristics of debug and release
    \l{glossary-build-config}{build configurations}
    differ significantly, analytical findings for one build configuration may
    not be relevant for the other. Profiling a debug build
    often finds a major part of the time being spent in low-level
    code, such as container implementations, while the same code
    does not show up in the profile of a release build of the
    same application due to inlining and other optimizations
    typically done there.

    Many recent compilers allow you to build an optimized application
    with debug information present at the same time. For example, typical
    options for GCC are: \c{-g -O2}. It is
    advisable to use such a setup for Callgrind profiling.

    \section1 Collecting Data

    To analyze applications:

    \list 1

        \li In the \uicontrol Projects mode, select a release build configuration.

        \li Select \uicontrol Debug to open the \uicontrol Debug mode, and then
            select \uicontrol Callgrind on the toolbar.

            \image qtcreator-valgrind-callgrind-toolbar.png "Callgrind view toolbar"

        \li Select the
            \inlineimage icons/qtcreator-analyze-start-button.png "Start button"
            button to start the application.

        \li Use the application to analyze it.

        \li Select the \inlineimage icons/stop_small.png "Stop button"
            button to view the results of the analysis in the
            \uicontrol Functions view.

    \endlist

    Select \inlineimage icons/pause-icon.png
    to speed up program execution during profiling by pausing event
    logging. No events are counted while logging is paused.

    Select \inlineimage icons/reload_gray.png
    to reset all event counters.

    Select \inlineimage icons/clean_pane_small.png
    to discard all collected data.

    Select \inlineimage icons/kcachegrind.png
    to view the data in KCachegrind. \QC launches KCachegrind
    and loads the data into it for visualization.

    \section1 Viewing Collected Data

    The results of the analysis are displayed in the \uicontrol Callgrind views.
    You can detach views and move them around. To revert the changes, select
    \uicontrol Views > \uicontrol {Reset to Default Layout}.

    Select \uicontrol Views to show and hide views and view
    titles. The \uicontrol Visualization view is hidden by
    default. Select \inlineimage icons/redo.png
    to refresh the data displayed in it when it is shown.

    As an alternative to collecting data, you can select
    \inlineimage icons/open.png
    to load an external log file into the \uicontrol Callgrind views.

    \image qtcreator-valgrind-callgrind.png "Callgrind views"

    Enter a string in the \uicontrol Filter field to filter the results.

    Move the cursor on a function in the \uicontrol Functions view for more
    information about it.

    Double-click a function to view information about the calling functions in
    the \uicontrol Callers view and about the called functions in the
    \uicontrol Callees view.

    Select \inlineimage icons/prev.png
    or \inlineimage icons/next.png
    To move between functions in the \uicontrol Callee view.

    To set the cost format, select \uicontrol $. You can view absolute
    or relative costs, as well as relative costs to parent. Select
    \inlineimage icons/filtericon.png
    to view only profiling info that originated from the project.

    To properly handle recursive or circular function calls, enable cycle
    detection by selecting \uicontrol O.

    To remove template parameter lists when displaying function names, select
    \uicontrol <>.

    \section1 Selecting Profiling Options

    You can specify analyzer settings either globally for all projects or
    separately for each project in the \l{Specifying Run Settings}{run settings}
    of the project.

    To specify global settings for Valgrind, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Analyzer. The \uicontrol
    {Callgrind Profiling Options} group contains Callgrind options.

    \image qtcreator-valgrind-callgrind-options.png "Valgrind options"

    In the \uicontrol {KCachegrind executable} field, enter the path to the
    KCachegrind executable to launch.

    In \uicontrol {Extra Callgrind arguments}, specify additional arguments
    for launching the executable.

    In the \uicontrol {Result view: Minimum event cost} and
    \uicontrol {Visualization: Minimum event cost} fields,
    limit the amount of results the profiler presents and
    visualizes to increase profiler performance.

    To show additional information about events in tooltips, select
    \uicontrol {Show additional information for events in tooltips}.

    To collect information about the system call times, select
    \uicontrol {Collect system call time}. To collect the number of
    global bus events of the event type \c Ge that are executed,
    select \uicontrol {Collect global bus events}.

    \section2 Enabling Full Cache Simulation

    By default, only instruction read accesses (Ir) are counted. To fully
    simulate the cache, select the \uicontrol {Enable cache simulation} check box.
    This enables the following additional event counters:

    \list

        \li Cache misses on instruction reads (I1mr/I2mr)

        \li Data read accesses (Dr) and related cache misses (D1mr/D2mr)

        \li Data write accesses (Dw) and related cache misses (D1mw/D2mw)

    \endlist

    \section2 Enabling Branch Prediction Simulation

    To enable the following additional event counters, select the
    \uicontrol {Enable branch prediction simulation} check box:

    \list

        \li Number of conditional branches executed and related predictor misses
            (Bc/Bcm)

        \li Executed indirect jumps and related misses of the jump address
            predictor (Bi/Bim)

    \endlist

*/

/*!
    \previouspage creator-cache-profiler.html
    \page creator-running-valgrind-remotely.html
    \nextpage creator-clang-tools.html

    \title Running Valgrind Tools on External Applications

    \QC integrates Valgrind code analysis tools for detecting memory
    leaks and profiling function execution.

    To run the Valgrind tools to analyze external applications for which
    you do not have a \QC project:

    \list 1

        \li Select \uicontrol Analyze > \uicontrol {Valgrind Memory Analyzer (External
            Application)} or \uicontrol {Valgrind Function Profiler (External Application)}.

            \image qtcreator-valgrind-remote-settings.png "Start Remote Analysis dialog"

        \li Specify the application to run and analyze, and the \l{glossary-buildandrun-kit}{kit}
            to use.

    \endlist

*/
