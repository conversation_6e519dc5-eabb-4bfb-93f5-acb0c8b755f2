// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage creator-developing-ios.html
    \page creator-developing-mcu.html
    \nextpage creator-developing-qnx.html

    \title Connecting MCUs

    Qt for MCU enables you to use subsets of QML and Qt Quick Controls
    to create user interfaces for devices that are powered by microcontroller
    units (MCU). It includes a new graphics rendering engine that has a low
    memory footprint and is optimized for MCUs and other resource-constrained
    devices.

    You can connect MCU boards to a development host to build applications for
    them using the GNU Arm Embedded GCC compiler, libraries, and other GNU tools
    necessary for bare metal software development on devices based on the Arm
    Cortex-M processors. You can deploy the applications on MCUs to run and
    debug them using \QC.

    The toolchains are available for cross-compilation on Microsoft Windows,
    Linux, and macOS. However, the Qt for MCU SDK is currently only available
    for Windows and Linux.

    For a list of Qt for MCU reference implementations, see the
    \l{Qt for MCUs - Supported Target Platforms}{Qt for MCUs} documentation.

    \section1 Requirements

    To use \QC to develop QML applications for MCUs, you need the following:

    \list
        \li Qt for MCU SDK (only available for Windows and Linux)
        \li \l{https://developer.arm.com/tools-and-software/open-source-software/developer-tools/gnu-toolchain/gnu-rm}
            {GNU ARM Embedded Toolchain}
    \endlist

    The hardware-specific requirements vary depending on the hardware platform you are developing for.
    For more information see:
    \list
        \li \l{Getting Started on NXP}
        \li \l{Getting Started on STM}
        \li \l{Getting Started on Renesas}
     \endlist

    \section1 Setting Up the Development Environment

    You must download and install the required software and create connections
    between \QC and MCUs. The following subsections guide you through the
    setup process.

    \section2 MCU Plugin

    To be able to develop applications for MCUs, you need the MCU plugin.
    This plugin is enabled automatically by the Qt Installer when you
    install Qt for MCUs.

    \section2 Specifying MCU Settings

    To configure a connection between \QC and your MCU board, select
    \uicontrol Edit > \uicontrol Preferences > \uicontrol Devices >
    \uicontrol MCU:

    \image qtcreator-mcu-options.png "MCU preferences"

    \list 1
        \li In the \uicontrol {Qt for MCUs SDK} field, specify the path
            to the directory where you installed Qt for MCUs SDK.
        \li In the \uicontrol {Targets supported by the Qt for MCUs SDK}
            field, select your MCU board.
        \li In the \uicontrol Requirements section, ensure that the
            platform-specific requirements are met. This varies depending
            on the target chosen:
            \list
                \li For STM32 targets:
                    \list
                        \li The \uicontrol {GNU ARM Embedded Toolchain} or
                            \uicontrol {IAR ARM Compiler} path.
                        \li The \uicontrol {STM32CubeProgrammer} install path.
                        \li The \uicontrol {MCU SDK} for the chosen target.
                        \li The \uicontrol {FreeRTOS Sources} for the chosen target.
                    \endlist
                 \li For NXP targets:
                    \list
                        \li The \uicontrol {GNU ARM Embedded Toolchain} or
                            \uicontrol {IAR ARM Compiler} path.
                        \li The \uicontrol {MCUXpresso IDE} install path.
                        \li The \uicontrol {MCU SDK} for the chosen target.
                        \li The \uicontrol {FreeRTOS Sources} for the chosen target.
                    \endlist
                 \li For Renesas targets:
                    \list
                        \li The \uicontrol {Green Hills Compiler} path.
                        \li The \uicontrol {Renesas Graphics Library} path.
                    \endlist
            \endlist
        \li Select the
            \uicontrol {Automatically create kits for all available targets on start}
            option to create kits automatically the next time Qt Creator is
            started.
            \note You could also use \uicontrol {Create Kit} to manually
            create kits for the chosen target.
        \li Select \uicontrol Apply to save the settings.
    \endlist

    \note When updating to other versions of the Qt for MCUs SDK, \QC will
    ask you if you want to replace the existing kits, or create new ones
    alongside. This can also be done manually, for each individual target,
    via \uicontrol {Update Kit} and \uicontrol {Create Kit}, respectively.

    \section2 Adding MCU Devices

    \note This optional step is not necessary if you have already
    set up the MCU SDK as outlined in \l{Specifying MCU Settings}.

    \QC automatically adds a default MCU device when you select
    \uicontrol Apply in the \uicontrol MCU tab after configuring the
    MCU tool chain.

    \image qtcreator-mcu-device.png "MCU devices"

    To add MCU devices, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol Devices > \uicontrol Add > \uicontrol {MCU Device} >
    \uicontrol {Start Wizard}:

       \list 1
           \li In the \uicontrol Name field, give the device a name.
           \li In the \uicontrol Type field, select the board type.
           \li Select \uicontrol Apply to add the device.
       \endlist

    \section2 Managing MCU Kits

    \QC automatically adds kits for all the available targets, if the
    \uicontrol {Automatically create kits for all available targets on start}
    option is enabled under the \uicontrol MCU settings tab. You can also
    create kits for individual targets manually, as outlined
    in \l{Specifying MCU Settings}.

    \image qtcreator-mcu-kit.png "MCU kits"

    You can edit and/or remove individual kits in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Kits.

    However, for adding new kits you should use the \uicontrol {Create Kit}
    button in the {Qt for MCUs} settings tab. This method adds the paths to
    the kit's toolkits and SDKs, and keeps them synchronized when selecting
    \uicontrol Apply or \uicontrol OK.

    The \uicontrol {MCU dependencies} field displays paths to 3rd party
    software required for MCU development with the current kit.

    \section1 Running Applications on MCUs

    You can use a wizard to set up a project for developing an application that
    you can run on MCUs. The project uses a subset of QML and Qt Quick Controls
    that are supported by Qt for MCU. For more information about developing
    applications for MCUs, see the Qt for MCU documentation.

    To create an application and run it on a MCU board:

    \list 1
        \li Select \uicontrol File > \uicontrol {New Project} >
            \uicontrol {Application (Qt for MCU)} >
            \uicontrol {MCU Support Application} > \uicontrol Choose.
        \li Follow the instructions of the wizard to create the project.
        \li Select \uicontrol Projects > \uicontrol {Build & Run}, and then
            select the kit for building the application and running it on the
            MCU board specified in the kit.
        \li Select \uicontrol Run to specify run settings.
            Usually, you can use the default settings.
    \endlist

    \section1 Supported Qt for MCUs SDKs

    \note The Qt for MCUs SDK 2.3 requires \QC 9.0.0, or later.

    The following table lists the \QC versions you can use to develop
    applications with particular Qt for MCUs SDK versions.

    \table
    \header
        \li \QC version
        \li Qt for MCUs SDK version
    \row
        \li 9.0.0 or later
        \li 2.0 or later
    \row
        \li 7.0.0 up to 8.0.2
        \li 2.0 up to 2.2
    \row
        \li 6.0.x
        \li 1.3 up to 2.2
    \row
        \li 4.12.4 up to 5.0.3
        \li 1.3 up to 1.9
    \row
        \li 4.12.2 or 4.12.3
        \li 1.2
    \row
        \li 4.12.0 or 4.12.1
        \li 1.1
    \row
        \li 4.11.x
        \li 1.0
    \endtable
*/
