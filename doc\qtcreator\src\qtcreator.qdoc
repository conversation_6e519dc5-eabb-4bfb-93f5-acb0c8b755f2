// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page index.html
    \nextpage creator-getting-started.html

    \title Qt Creator Manual

    \QC provides a cross-platform, complete integrated development environment
    (IDE) for application developers to create applications for multiple
    \l{Desktop Platforms}{desktop}, \l {Embedded Platforms}{embedded}, and
    \l{Mobile Platforms}{mobile device} platforms, such as \l Android and
    \l iOS. It is available for \l Linux, \l \macos and \l Windows
    operating systems. For more information, see \l{Supported Platforms}.

    In addition, you can use the experimental
    \l{Building Applications for the Web}{WebAssembly plugin}
    to build applications in web format and run them in web
    browsers.

    This manual also describes features that are only available if you have the
    appropriate \l{http://qt.io/licensing/}{Qt license}. For more information,
    see \l{Commercial Features}.

    \table
        \row
            \li {4,1} \b {\l{All Topics}}
        \row
            \li \inlineimage front-gs.png
            \li \inlineimage front-projects.png
            \li \inlineimage front-ui.png
            \li \inlineimage front-coding.png
        \row
            \li \b {\l{Getting Started}}
                \list
                    \li \l{IDE Overview}
                    \li \l{User Interface}
                    \li \l{Configuring Qt Creator}
                    \li \l{Building and Running an Example}
                    \li \l{Tutorials}
                \endlist
            \li \b {\l{Managing Projects}}
                \list
                    \li \l{Creating Projects}
                    \li \l{Using Version Control Systems}
                    \li \l{Configuring Projects}
                    \li \l{Managing Sessions}
                \endlist
            \li \b {\l{Designing User Interfaces}}
                \list
                    \li \l{Developing Qt Quick Applications}
                    \li \l{Developing Widget Based Applications}
                    \li \l{Optimizing Applications for Mobile Devices}
                \endlist
            \li \b {\l{Coding}}
                \list
                    \li \l{Writing Code}
                    \li \l{Finding}
                    \li \l{Refactoring}
                    \li \l{Configuring the Editor}
                    \li \l{Modeling}
                    \li \l{Editing State Charts}
                \endlist
        \row
            \li \inlineimage front-preview.png
            \li \inlineimage front-testing.png
            \li \inlineimage front-advanced.png
            \li \inlineimage front-help.png
        \row
            \li \b {\l{Building and Running}}
                \list
                    \li \l{Validating with Target Hardware}
                    \li \l{Building for Multiple Platforms}
                    \li \l{Running on Multiple Platforms}
                    \li \l{Deploying to Devices}
                    \li \l{Connecting Devices}
                \endlist
            \li \b {\l{Testing}}
                \list
                    \li \l{Debugging}
                    \li \l{Analyzing Code}
                    \li \l{Running Autotests}
                    \li \l{Using Squish}
                \endlist
            \li \b {\l{Advanced Use}}
                \list
                    \li \l{Supported Platforms}
                    \li \l{Build Systems}
                    \li \l{Using Command Line Options}
                    \li \l{Keyboard Shortcuts}
                    \li \l{Using External Tools}
                \endlist
            \li \b {\l{Getting Help}}
                \list
                    \li \l{Using the Help Mode}
                    \li \l{FAQ}
                    \li \l{How-tos}
                    \li \l{Known Issues}
                    \li \l{Glossary}
                \endlist
        \row
            \li {4,1} \b {Contact Us}
                \list
                    \li To report bugs and suggestions to the
                        \l{https://bugreports.qt.io/}{Qt Project Bug Tracker},
                        select \uicontrol Help > \uicontrol {Report Bug}.
                    \li To copy and paste detailed information about your
                        system to the bug report, select \uicontrol Help >
                        \uicontrol {System Information}.
                    \li To join the \l{https://lists.qt-project.org/listinfo/qt-creator}
                        {\QC mailing list} or \l{https://web.libera.chat/#qt-creator}
                        {#qt-creator} channel on Libera.Chat IRC, select
                        \uicontrol Help > \uicontrol Contact.
                    \li For credits and a list of third-party libraries, see
                        \l {Acknowledgements}.
            \endlist
    \endtable
*/
