// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-editor-external.html
    \if defined(qtdesignstudio)
    \previouspage quick-converting-ui-projects.html
    \nextpage studio-help.html
    \else
    \previouspage creator-keyboard-shortcuts.html
    \nextpage creator-task-lists.html
    \endif

    \title Using External Tools

    You can use external tools directly from \QC. Qt Linguist, QML preview
    tools, and the default text editor for your system are preconfigured
    for use. You can change their default configurations and configure new
    tools.

    To run the tools, select \uicontrol Tools > \uicontrol External, or use the
    \c x filter in the locator.

    \section1 Using Qt Linguist

    When you \l{Creating Projects}{create a new project}, you can automatically
    generate a translation source file (TS) for one language. You can add other
    languages later by editing the project file.

    You can use the Qt Linguist release manager tools, lupdate and lrelease,
    directly from \QC. The lupdate tool is used to synchronize source
    code and translations. The lrelease tool is used to create run-time
    translation files for use by the released application.

    \note Running the tools from \QC is supported only when using qmake as the
    build system. For more information about using the tools with CMake, see
    \l{https://doc.qt.io/qt-6/cmake-command-reference.html#qt6-linguisttools}
    {Qt6::LinguistTools}.

    To synchronize TS files from a translator with the
    application code, select \uicontrol Tools > \uicontrol External >
    \uicontrol Linguist > \uicontrol {Update Translations (lupdate)}.

    To generate from the TS files Qt message (QM) files that can be used by an
    application, select \uicontrol Tools > \uicontrol External >
    \uicontrol Linguist > \uicontrol {Release Translations (lrelease)}.

    By default, the project .pro file is passed to the tools as an argument. To
    specify other command line arguments for the tools, select \uicontrol Tools >
    \uicontrol External > \uicontrol Configure.

    To open TS files in Qt Linguist, right-click a TS file in the
    \uicontrol Projects or \uicontrol {File System} view and select
    \uicontrol {Open With} > \uicontrol {Qt Linguist} in the context menu.
    For more information about Qt Linguist, see \l{Qt Linguist Manual}.

    \section1 Running QML Files

    You can test the current QML document while you are developing an application.

    To run the currently active QML file, select \uicontrol Tools >
    \uicontrol External > \uicontrol {Qt Quick} > \uicontrol {QML Runtime}.

    \section1 Using External Text Editors

    You can open files for editing in the default text editor for your system:
    Notepad on Windows and vi on Linux and \macos.
    To open the file you are currently viewing in an external editor, select
    \uicontrol Tools > \uicontrol External > \uicontrol Text >
    \uicontrol {Edit with Notepad} or \uicontrol {Edit with vi}, depending on
    your system.

    \QC looks for the editor path in the PATH environment variable
    of your operating system.

    \section1 Configuring External Tools

    You can change the configuration of preconfigured tools and configure
    additional tools in \QC \uicontrol Preferences.

    \image qtcreator-external-tools.png

    Select the \inlineimage icons/replace.png
    (\uicontrol {Variables}) button in a field to select from a list of
    \l{Using Qt Creator Variables}{variables} that are available in a
    particular context.

    To configure external tools:

    \list 1

        \li Select \uicontrol Tools > \uicontrol External >
            \uicontrol Configure.

        \li Select \uicontrol Add > \uicontrol {Add Tool}
            to add a new tool. You can also select \uicontrol {Add Category} to add a
            new category.

        \li In the \uicontrol Executable field, specify the executable to run. If the
            executable is found in your system PATH variable, do not specify
            the path to it.

        \li In the \uicontrol Arguments field, specify optional arguments for running
            the executable.

        \li In the \uicontrol {Working directory} field, specify the path to the
            working directory.

        \li In the \uicontrol Output field, select how to handle output from the
            tool. You can ignore the output, view it in \l{Viewing Output}
            {General Messages}, or replace the selected text with the
            output in the code editor.

        \li In the \uicontrol {Error output} field, select how to handle error
            messages from the tool.

        \if defined(qtcreator)
        \li In the \uicontrol {Base environment} field, select whether to run
            the tool in the system environment or the \l{Build Environment}
            {build environment} or \l {Selecting the Run Environment}
            {run environment} of the active project. Select the build or run
            environment if the system environment does not contain the necessary
            PATH settings to find the tool chain, for example.
        \else
        \li In the \uicontrol {Base environment} field, use the default settings.
        \endif

        \li In the \uicontrol Environment field, select \uicontrol Change to modify
            environment variable values for build and run environments in
            the \uicontrol {Edit Environment Changes} dialog.
            \if defined(qtcreator)
            For more information about how to add and remove variable values,
            see \l{Batch Editing}.
            \endif


            To globally change the system environment from the one in which
            \QC is started, select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol Environment > \uicontrol System, and then select
            \uicontrol Change in the \uicontrol Environment field.
            \if defined(qtcreator)
            For more information, see \l{Specifying Environment Settings}.
            \endif

        \li Select the \uicontrol {Modifies current document} check box to make sure
            that if the current document is modified by the tool, it is saved
            before the tool is run and reloaded after the tool finishes.

        \li In the \uicontrol Input field, specify text that is passed as standard
            input to the tool.

    \endlist

    The category and tool are added to the \uicontrol Tools >
    \uicontrol External menu.

    If you change the configuration of preconfigured tools, you can later
    revert the changes by selecting the \uicontrol Reset button.

    The tool configurations that you add and modify are stored in XML format in
    the user configuration folder. For example,
    \c {~/config/QtProject/qtcreator/externaltools}
    on Linux and \macos and
    \c {C:\Users\<USER>\AppData\Roaming\QtProject\qtcreator\externaltools}
    in Windows. To share a configuration with other users, copy an XML
    configuration file to the folder.

*/
