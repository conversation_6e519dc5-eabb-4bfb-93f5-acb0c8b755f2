// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page creator-file-system-view.html
    \previouspage creator-projects-view.html
    \nextpage creator-open-documents-view.html

    \title File System

    If you cannot see a file in the \l Projects view, switch to the
    \uicontrol {File System} view, which shows all the files in the file system.

    \if defined(qtdesignstudio)
    The following image displays the \uicontrol {File System} view in the
    \uicontrol Design mode:

    \image qtcreator-filesystem-view-design.png "File System view in the Design mode"
    \else
    \image qtcreator-filesystem-view.webp "File System view in the sidebar"
    \endif

    To move to the root directory of the file system, select \uicontrol Computer
    in the menu (1). Select \uicontrol Home to move to the user's home
    directory. Further, you can select a project to move to an open project
    or \uicontrol Projects to move to the directory specified in the
    \uicontrol {Projects directory} field in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Build & Run} > \uicontrol General.

    The file that is currently active in the editor determines which folder
    to display in the \uicontrol {File System} view:

    \list
        \li \uicontrol Projects if the file is located in a subdirectory of the
            \uicontrol {Projects directory}
        \li \uicontrol Home if the file is located in the user's home directory
        \li \uicontrol Computer in all other cases
    \endlist

    To stop the synchronization between the editor and the
    \uicontrol {File System} view, deselect the \inlineimage icons/linkicon.png
    (\uicontrol {Synchronize Root Directory with Editor}) button.

    The view displays the path to the active file as bread crumbs. You can move
    to any directory along the path by clicking it.

    \section1 File System Context Menu

    Use the context menu functions to:

    \list
        \li Open files with the default editor or some other editor.
        \li Open a project located in the selected directory.
        \li Show the file or directory in the file explorer.
        \li Open a terminal window in the selected directory or in the directory
            that contains the file. To specify the terminal to use on Linux and
            \macos, select \uicontrol Edit > \uicontrol Preferences >
            \uicontrol Environment > \uicontrol System.
        \li Search from the selected directory.
        \li View file properties, such as name, path, MIME type, default editor,
            line endings, indentation, owner, size, last read and modified
            dates, and permissions.
        \li Create new files. For more information, see
            \if defined(qtdesignstudio)
            \l{Adding Files to Projects}.
            \else
            \l{Creating Files}.
            \endif
        \li Rename or remove existing files.
        \li Create new folders.
        \li Compare the selected file with the currently open file in the diff
            editor. For more information, see \l{Comparing Files}.
        \li Display the contents of a particular directory in the view.
        \li Collapse all open folders.
    \endlist

    \section1 File System View Toolbar

    \if defined(qtdesignstudio)
    In the \uicontrol Edit and \uicontrol Debug mode, the
    \uicontrol {File System} view is displayed in the \l{Working with Sidebars}
    {sidebar}. It has a toolbar with additional options.

    \image qtcreator-filesystem-view.webp "File System view in the sidebar"
    \else
    The toolbar in the \uicontrol {File System} view contains additional
    options.
    \endif


    To manage view contents, select \inlineimage icons/filtericon.png
    (\uicontrol Options):

    \list
        \li To hide the bread crumbs, deselect the
            \uicontrol {Show Bread Crumbs} check box.
        \li By default, the view separates folders from files and lists them
            first. To list all items in alphabetic order, deselect the
            \uicontrol {Show Folders on Top} check box.
        \li To also show hidden files, select \uicontrol {Show Hidden Files}.
    \endlist

    To stop the synchronization with the file currently open in the
    editor, deselect \inlineimage icons/linkicon.png
    (\uicontrol {Synchronize with Editor}).
*/
