// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!

//! [configuring ssh]
    \section2 Configuring SSH Connections

    SSH connections are established via an OpenSSH client running in master
    mode, if possible. Connection sharing is enabled by default to allow
    sharing multiple sessions over a single SSH connection. This way, a
    connection is only established once and then re-used by subsequent run
    and deploy procedures, saving connection setup overhead particularly
    with embedded devices. Because connection sharing is not supported on
    Windows, a new SSH connection is created for each deploy or run procedure.

    To create SSH connections, you must install the \l{https://www.openssh.com/}
    {OpenSSH} suite, which includes the ssh, sftp, and ssh-keygen tools on the
    development PC.

    To tell \QC where it can find the tools, specify the paths to the
    directories where the tools are installed in \uicontrol Edit >
    \uicontrol Preferences > \uicontrol Devices > \uicontrol SSH:

    \image qtcreator-ssh-options.png "SSH preferences"

    \list
        \li Deselect the \uicontrol {Enable connection sharing} check box to
            create a new SSH connection for each deploy and run procedure. This
            option is grayed on Windows, where connection sharing is not
            supported.
        \li In the \uicontrol {Connection sharing timeout} field, specify the
            timeout for reusing the SSH connection in minutes.
        \li In the \uicontrol {Path to ssh executable} field, enter the path
            to the directory where the OpenSSH executable is installed.
        \li In the \uicontrol {Path to sftp executable} field, enter the path
            to the directory where the SFTP executable is installed.
        \li In the \uicontrol {Path to ssh-askpass executable} field, enter the
            path to the directory where the ssh-askpass executable is installed.
            Usually, you can use the default path that points to the
            implementation of the tool delivered with \QC, qtc-askpass.
        \li In the \uicontrol {Path to ssh-keygen executable} field, enter the
            path to the directory where the ssh-keygen executable is installed.
    \endlist

//! [configuring ssh]


//! [generating ssh keys]

    \section2 Generating SSH Keys

    If you do not have an SSH public and private key pair, you can generate it
    in \QC. The connection wizard can create the key pair for you, or you can
    create it separately.

    You can specify key length and the key algorithm, RSA or ECDSA.
    If you only use the keys to protect connections to the emulator or
    device, you can use the default values.

    \list 1

        \li Select \uicontrol Edit > \uicontrol Preferences > \uicontrol Devices
            > \uicontrol Devices > \uicontrol {Create New}.

            \image qtcreator-ssh-key-configuration.png "SSH Key Configuration dialog"

        \li In the \uicontrol {Private key file} field, select the location to save
            the private key.

            The \uicontrol {Public key file} field displays the location to save the
            corresponding public key.

        \li Select \uicontrol {Generate And Save Key Pair} to generate and save the
            keys at the specified locations.

    \endlist

//! [generating ssh keys]
*/
