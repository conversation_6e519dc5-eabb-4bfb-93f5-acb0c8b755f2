// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtdesignstudio-toc.html

    \title All Topics
    \list
        \li \l{Getting Started}
            \list
                \li \l{Installation}
                \li \l{Tutorials}
                \li \l{User Interface}
                    \list
                        \li \l{Selecting Modes}
                        \li \l{Design Views}
                            \list
                                \li \l{2D}
                                \li \l{3D}
                                \li \l{Material Editor and Browser}
                                \li \l{Components}
                                \li \l{Assets}
                                \li \l{Navigator}
                                \li \l{Properties}
                                \li \l{Connections}
                                \li \l{States}
                                \li \l{Translations}
                                \li \l{Transitions}
                                \li \l{Timeline}
                                \li \l{Curves}
                                \li \l{Code}
                                \li \l{Projects}
                                \li \l{File System}
                                \li \l{Open Documents}
                           \endlist
                        \li \l{Managing Workspaces}
                        \li \l{Managing Sessions}
                        \li \l{Keyboard Shortcuts}
                    \endlist
                \li \l{Creating Projects}
                \li \l{Use Cases}
                \li \l{Concepts and Terms}
                \li \l{Examples}
            \endlist
        \li \l{Wireframing}
            \list
                \li \l{Designing Application Flows}
                    \list
                        \li \l{Adding Flow Views}
                        \li \l{Adding Flow Items}
                        \li \l{Adding Action Areas and Transitions}
                        \li \l{Applying Effects to Transitions}
                        \li \l{Simulating Events}
                        \li \l{Simulating Conditions}
                        \li \l{Applying States in Flows}
                        \li \l{Reacting to External Events}
                    \endlist
                \li \l {Using Components}
                    \list
                        \li \l{Preset Components}
                            \list
                                \li \l{Shapes}
                                \li \l{Text}
                                \li \l{Images}
                                \li \l{User Interaction Methods}
                                \li \l{UI Controls}
                                \li \l{Lists and Other Data Models}
                                \li \l{2D Effects}
                                \li \l{Logic Helpers}
                                \li \l Animations
                                \li \l{3D Views}
                                \li \l{Node}
                                \li \l{Group}
                                \li \l{Instanced Rendering}
                                \li \l{Skeletal Animation}
                                \li \l{3D Models}
                                \li \l{Materials and Shaders}
                                \li \l{Textures}
                                \li \l{3D Materials}
                                \li \l{3D Effects}
                                \li \l{Custom Shaders}
                                \li \l{Custom Effects and Materials}
                                \li \l{Lights}
                                \li \l{Cameras}
                                \li \l{Scene Environment}
                                \li \l{Morph Target}
                                \li \l{Repeater3D}
                                \li \l{Loader3D}
                                \li \l{Particles}
                                        \list
                                            \li \l {Particle System}
                                            \li \l {Logical Particles}
                                            \li \l {Particle Emitters}
                                            \li \l {Particle Affectors}
                                            \li \l {Particle Directions}
                                        \endlist
                            \endlist
                        \li \l {Creating Component Instances}
                        \li \l {Creating Custom Components}
                            \list
                                \li \l{Creating Buttons}
                                \li \l{Creating Scalable Buttons and Borders}
                            \endlist
                    \endlist
                \li \l{Specifying Component Properties}
                \li \l{Scalable Layouts}
                \li \l{Annotating Designs}
            \endlist
        \li \l{Prototyping}
            \list
                \li \l{Creating UI Logic}
                \li \l{Simulating Complex Experiences}
                    \list
                        \li \l{Loading Placeholder Data}
                        \li \l{Simulating Application Logic}
                        \li \l{Simulating Dynamic Systems}
                        \li \l{Using QML Modules with Plugins}
                    \endlist
                \li \l{Dynamic Behaviors}
                    \list
                        \li \l{Working with Connections}
                            \list
                                \li\l{Connecting Components to Signals}
                                \li\l{Adding Bindings Between Properties}
                                \li\l{Specifying Custom Properties}
                            \endlist
                        \li \l{Working with States}
                    \endlist
                \li \l{Validating with Target Hardware}
                    \list
                        \li \l{Previewing on Desktop}
                        \li \l{Previewing on Devices}
                        \li \l{Previewing Android Applications}
                        \li \l{Sharing Applications Online}
                    \endlist
                \li \l {Asset Creation with Other Tools}
                    \list
                        \li \l{Exporting from Design Tools}
                        \list
                            \li \l{Exporting Designs from Adobe Illustrator}
                            \li \l{Exporting Designs from Adobe Photoshop}
                                \list
                                    \li \l{Setting Up Qt Bridge for Adobe Photoshop}
                                    \li \l{Using Qt Bridge for Adobe Photoshop}
                                \endlist
                            \li \l{Exporting Designs from Adobe XD}
                                \list
                                    \li \l{Setting Up Qt Bridge for Adobe XD}
                                    \li \l{Using Qt Bridge for Adobe XD}
                                \endlist
                            \li \l{Exporting Designs from Sketch}
                                \list
                                    \li \l{Setting Up Qt Bridge for Sketch}
                                    \li \l{Using Qt Bridge for Sketch}
                                \endlist
                            \li \l{Exporting Designs from Figma}
                                \list
                                    \li \l{Setting Up Qt Bridge for Figma}
                                    \li \l{Using Qt Bridge for Figma}
                                \endlist
                        \endlist
                        \li \l {Exporting 3D Assets}
                            \list
                                \li \l{Exporting from Blender}
                                \li \l{Exporting from Maya}
                                \li \l{Exporting from Qt 3D Studio}
                            \endlist
                        \li \l{Importing Designs}
                            \list
                                \li \l{Importing 2D Assets}
                                    \list
                                        \li \l{Using Custom Fonts}
                                    \endlist
                                \li \l{Importing 3D Assets}
                            \endlist
                        \li \l{Exporting Components}
                        \li Qt Quick Effect Maker
                            \list
                                \li \l{Creating Qt Quick Effect Maker Files}
                                \li \l{Working with Effects in Qt Quick Effect Maker}
                            \endlist
                    \endlist
            \endlist
        \li \l{Motion Design}
            \list
                \li \l{Introduction to Animation Techniques}
                \li \l{Creating Timeline Animations}
                \li \l{Editing Easing Curves}
                \li \l{Production Quality}
                \li \l{Optimizing Designs}
                    \list
                        \li \l{Creating Optimized 3D Scenes}
                    \endlist
            \endlist
        \li \l{Implementing Applications}
            \list
                \li \l{Designer-Developer Workflow}
                \li \l{Coding}{Cross-Platform Development}
                    \list
                        \li \l{Writing Code}
                            \list
                                \li \l{Working in Edit Mode}
                                    \list
                                        \li \l{Working with Sidebars}
                                        \li \l{Browsing Project Contents}
                                        \li \l{Viewing Output}
                                    \endlist
                                \li \l{Semantic Highlighting}
                                \li \l{Checking Code Syntax}
                                \li \l{Completing Code}
                                \li \l{Indenting Text or Code}
                                \li \l{Using Qt Quick Toolbars}
                                \li \l{Comparing Files}
                            \endlist
                        \li \l{Finding}
                            \list
                                \li \l{Finding and Replacing}
                                \li \l{Searching with the Locator}
                            \endlist
                        \li \l{Refactoring}
                        \li \l{Applying Refactoring Actions}
                        \li \l{Configuring the Editor}
                            \list
                                \li \l{Specifying Code View Settings}
                            \endlist
                    \endlist
                \li \l{Debugging and Profiling}
                    \list
                        \li \l{Debugging Qt Quick Projects}
                        \li \l{Debugging a Qt Quick Example Application}
                        \li \l{Profiling QML Applications}
                    \endlist
            \endlist
        \li \l{Advanced Designer Topics}
            \list
                \omit
                \li Extending Component Functionality
                \endomit
                \li \l{UI Files}
                \li \l{Managing Data Collection}
                    \list
                        \li \l {Collecting Usage Statistics}
                        \li \l {Collecting User Feedback}
                        \li \l {Reporting Crashes}
                    \endlist
                \li \l {Packaging Applications}
            \endlist
        \li \l{Developer Topics}
            \list
                \li \l{Using Git}
                \li \l{Converting Qt 5 Projects into Qt 6 Projects}
                \li \l{Converting UI Projects to Applications}
                \li \l{Using External Tools}
            \endlist
        \li \l Help
            \list
                \li \l{Using the Help Mode}
                \li \l{Frequently Asked Questions}
                \li \l{Supported Platforms}
            \endlist
        \li \l{Technical Support}
        \li \l{Acknowledgements}
    \endlist
*/
