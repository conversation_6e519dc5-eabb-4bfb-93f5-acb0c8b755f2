// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage accelbubble
    \page creator-project-managing.html
    \nextpage creator-project-creating.html

    \title Managing Projects

    \image front-projects.png

    One of the major advantages of \QC is that it allows a team of designers and
    developers to share a project across different development platforms with a
    common tool for design, development, and debugging.

    \list

        \li \l{Creating Projects}

            To set up a project, you first have to decide what kind of an
            application you want to develop: do you want a user interface based
            on Qt Quick or Qt widgets. Second, you have to choose the
            language to implement the application logic: C++, JavaScript, or
            Python.

        \li \l{Using Version Control Systems}

            The recommended way to set up a project is to use a version control
            system. Store and edit only project source files and configuration
            files. Do not store generated files.

        \li \l{Configuring Projects}

            Installation programs and project wizards create default
            configurations for \QC and your projects. You can modify
            the settings in the Projects mode.

        \li \l{Managing Sessions}

            Sessions store items such as open files, breakpoints, and evaluated
            expressions, which you do not typically want to share across
            platforms.

    \endlist

    \section1 Related Topics

    \list
        \li \l{Build Systems}
    \endlist

*/
