// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-project-managing-sessions.html
    \page creator-design-mode.html
    \nextpage creator-visual-editor.html

    \title Designing User Interfaces

    \image front-ui.png

    \QC provides an integrated visual editor designing widget-based applications
    in the \uicontrol Design mode. The integration includes project management
    and code completion.

    You can develop Qt Quick applications in the \uicontrol Edit mode or use
    a separate visual editor, \QDS.

    \list

        \li \l{Developing Qt Quick Applications}

            You can use wizards to create Qt Quick projects containing
            boiler-plate code that you can edit in the \uicontrol Edit mode.

        \li \l{Developing Widget Based Applications}

            Widgets and forms created with \QD are integrated seamlessly with
            programmed code by using the Qt signals and slots mechanism that
            allows you to easily assign behavior to graphical elements. All
            properties set in \QD can be changed dynamically within the code.
            Furthermore, features such as widget promotion and custom plugins
            allow you to use your own widgets with \QD.

        \li \l{Optimizing Applications for Mobile Devices}

            Before starting application development, analyze and define the
            requirements, scope, and functionality of the application to ensure
            efficient functionality and a smooth user experience on mobile
            devices.
    \endlist
*/
