// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \previouspage studio-exporting-and-importing.html
    \page qtbridge-overview.html
    \nextpage qtbridge-ai.html

    \title Exporting from Design Tools

    When working with 2D assets, you can use \QB to export them from design
    tools into a metadata format that you can then \l{Importing Designs}{import}
    into \QDS.

    When working with 3D assets, you can use the export functions provided by
    the 3D graphics tools to save the assets in widely-used 3D graphics formats,
    and then use \QB to import them into \QDS.

    For best results when importing assets, follow
    the guidelines for creating and exporting them.

    \section1 2D Assets

    You can use the Qt Installer to install \QB if you have a
    \QDS enterprise license.

    \table
    \row
        \li \inlineimage ai-logo.png
        \li \inlineimage ps-logo.png
        \li \inlineimage xd-logo.png
        \li \inlineimage sketch-logo.png
        \li \inlineimage figma-logo.png
    \row
        \li \l{Exporting Designs from Adobe Illustrator}{Adobe Illustrator}
        \li \l{Exporting Designs from Adobe Photoshop}{Adobe Photoshop}
        \li \l{Exporting Designs from Adobe XD}{Adobe XD}
        \li \l{Exporting Designs from Sketch}{Sketch}
        \li \l{Exporting Designs from Figma}{Figma}
    \endtable

    \section1 3D Assets

    You can import files you created using 3D graphics applications and
    stored in several widely-used formats, such as .blend, .dae, .fbx,
    .glb, .gltf, .obj, .uia, or .uip.

    For an overview, see \l{Exporting 3D Assets}.

    \table
    \row
        \li \inlineimage blender-logo.png
        \li \inlineimage maya-logo.png
        \li \inlineimage qt-3ds-logo.png
    \row
        \li \l{Exporting from Blender}{Blender}
        \li \l{Exporting from Maya}{Maya}
        \li \l{Exporting from Qt 3D Studio}{Qt 3D Studio}
    \endtable

*/
