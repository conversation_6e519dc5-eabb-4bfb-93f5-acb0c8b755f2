// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-running-targets.html
    \if defined(qtdesignstudio)
    \previouspage studio-live-preview.html
    \else
    \previouspage creator-building-targets.html
    \endif
    \nextpage creator-deployment.html

    \title Running on Multiple Platforms

    By default, running an application also builds it and deploys it to a
    location from where it can be run on the desktop, on a device emulator or
    simulator, or on a \l{glossary-device}{device} that is connected to
    the development PC.

    To run executable files without deploying them first, select \uicontrol Build >
    \uicontrol {Run Without Deployment}. To make this the default option, deselect the
    \uicontrol Edit > \uicontrol Preferences > \uicontrol {Build & Run} >
    \uicontrol General > \uicontrol {Always deploy project before running it}
    check box.

    To run applications:

    \list 1

        \li Click the \uicontrol {Build and Run Kit Selector} icon (1) or select
            \uicontrol Build > \uicontrol {Open Build and Run Kit Selector} to select the
            build and run \l{glossary-buildandrun-kit}{kit}.

            \image qtcreator-kit-selector.png "Kit selector"

        \li Click the \uicontrol Run button (2).

    \endlist

    If your project has several run targets defined, such as
    \l{Running Autotests}{tests}, you can select them in the kit selector.

    \image qtcreator-kit-selector-run-targets.png "Run targets in the kit selector"

    If you have connected \l{Mobile Platforms}{mobile devices} or
    \l{Embedded Platforms}{embedded devices} to the development PC
    or added virtual devices, such as \l{Managing Android Virtual Devices (AVD)}
    {Android Virtual Devices (AVD)}, you can select them in the kit selector.
    Select \uicontrol Manage to manage device settings. For example, you can add
    AVDs or manually start disconnected AVDs.

    \l {Application Output} displays the status of the
    application while it is running. You can select the \uicontrol Run button
    to re-run applications without building them first. This is
    useful when developing Qt Quick applications because the QML files are
    interpreted at runtime. Therefore, the application does not need to be
    built again if you edited only QML files. This saves time especially if
    the application contains large image files that would need to be bundled
    into the resource file before running the application.

    \image qtcreator-application-output.png

    \if defined(qtcreator)
    For more information on the options you have, see
    \l{Specifying Run Settings}.
    \endif

    \include linux-mobile/creator-projects-running-generic-linux.qdocinc running on embedded linux

    \if defined(qtcreator)
    \include qnx/creator-projects-running-qnx.qdocinc running on qnx
    \include python/creator-python-run.qdocinc running python
    \endif
*/
