// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-vcs-mercurial.html
    \page creator-vcs-perforce.html
    \nextpage creator-vcs-subversion.html

    \title Using Perforce

    Perforce is a fast software configuration management system developed by
    Perforce Software.

    \section1 Enabling the Perforce Plugin

    To enable the Perforce plugin:

    \list 1
        \li Select \uicontrol Help > \uicontrol {About Plugins} >
        \uicontrol {Version Control} > \uicontrol Perforce.
        \li Select \uicontrol {Restart Now} to restart \QC and load the plugin.
    \endlist

    \section1 Configuring Perforce

    In the Perforce preferences, you can specify workspace details:
    \uicontrol {P4 user}, \uicontrol {P4 client}, and \uicontrol {P4 port}. To
    specify the details individually for several projects, use configuration
    files instead. Create a \c {p4config.txt} configuration file for each
    project in the top level project directory, and run
    \c{p4 set P4CONFIG=p4config.txt} once. You must deselect the
    \uicontrol {Environment Variables} check box.

    \section1 Editing Files

    In addition to the standard version control system functions described in
    \l {Using Common Functions}, you can select \uicontrol Tools >
    \uicontrol Perforce > \uicontrol {Edit File} to open a file for editing
    within the client workspace. By default, files are automatically opened for
    editing. To disable this feature, select \uicontrol Edit >
    \uicontrol Preferences > \uicontrol {Version Control} > \uicontrol Perforce,
    and then deselect the \uicontrol {Automatically open files when editing}
    check box.

    To list files that are open for editing, select \uicontrol Tools >
    \uicontrol Perforce > \uicontrol Opened.

    To group files for commit, select \uicontrol Tools > \uicontrol Perforce >
    \uicontrol {Pending Changes}.

    To view information about change lists and the files in them, select
    \uicontrol Tools > \uicontrol Perforce > \uicontrol Describe.

    By default, you must confirm that you want to submit changes. To suppress
    the confirmation prompt, select \uicontrol Edit > \uicontrol Preferences >
    \uicontrol {Version Control} > \uicontrol Perforce, and then deselect the
    \uicontrol {Prompt on submit} check box.
*/
