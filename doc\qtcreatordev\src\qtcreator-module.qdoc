// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtcreator-api.html

    \title Qt Creator API Reference

    The core of \QC is basically only a \l{ExtensionSystem}{plugin loader}. All
    functionality is implemented in plugins. The basis of \QC is implemented in
    the Core plugin. The plugin manager provides simple means for
    plugin cooperation that allow plugins to provide hooks for other plugin's
    extensions.

    \section1 Libraries

    \section2 Core Libraries

    There are a few core libraries used by many parts of Qt Creator.

    \table
        \header
            \li Library Name
            \li Description

        \row
            \li \l{Aggregation}
            \li Adds functionality for \e glueing QObjects of different types
                together, so you can \e cast between them.

        \row
            \li \l{ExtensionSystem}
            \li Implements the plugin loader framework. Provides a base class
                for plugins and basic mechanisms for plugin interaction like an
                object pool.

    \omit
        \row
            \li \l{Utils}
            \li General utility library.

        \row
            \li \l{QmlJS}
            \li QML and JavaScript language support library.

    \endtable

    \section2 Additional libraries

    \table
        \header
            \li Library Name
            \li Description

        \row
            \li \l{qtcreatorcdbext}
            \li Windows CDB debugger extension
    \endomit
    \endtable


    \section1 Plugins

    As already mentioned, \QC is basically only a plugin loader framework
    which gets its IDE functionality through plugins. The most important plugin
    is the Core plugin which provides all the basic functionality needed
    later to integrate e.g. editors or mode windows.

    \table
        \header
            \li Plugin Name
            \li Description

        \row
            \li \l{Core}
            \li The core plugin. Provides the main window and managers for
                editors, actions, mode windows and files, just to mention the
                most important ones.

                This plugin also contains classes necessary to hook into the
                \l{Core::ILocatorFilter}{Locator} as well as support for
                searching text in arbitrary widgets.

    \omit
        \row
            \li \l{ProjectExplorer}
            \li The project explorer plugin. Provides base classes for project
                handling.

        \row
            \li \l{Debugger}
            \li Debugging functionality.

        \row
            \li \l{VcsBase}
            \li Base classes for version control support.

        \row
            \li \l{TextEditor}
            \li This is where everything starts if you want to create a text
                editor. Besides the base editor itself, this plugin contains
                APIs for supporting functionality like \l{Snippets}{snippets},
                highlighting, \l{CodeAssist}{code assist}, indentation and
                style, and others.
    \endomit
    \endtable

    \section1 Reference

    \list
        \li \l {Qt Creator C++ Classes}
        \li \l {Qt Creator Namespaces}
        \li \l {Qt Creator Functions}
    \endlist
*/

/*!
    \module QtCreator
    \title Qt Creator C++ Classes

    This topic lists the documented Qt Creator C++ classes. For information
    about the classes that have not been documented, please inspect the source
    code.
*/

/*!
    \page namespaces.html
    \title Qt Creator Namespaces

    This topic lists the documented Qt Creator namespaces. For information
    about the namespaces that have not been documented, please inspect the
    source code.

    \generatelist namespaces
*/

/*!
    \page mainclasses.html
    \title Qt Creator Main Classes

    This topic lists the most important Qt Creator C++ classes.

    \generatelist mainclasses
*/

/*!
    \page functions.html
    \title Qt Creator Functions

    This topic lists the documented Qt Creator functions. For information
    about the functions that have not been documented, please inspect the
    source code.

    \generatelist functionindex
*/
