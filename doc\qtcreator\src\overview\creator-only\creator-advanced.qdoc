// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \previouspage creator-squish.html
    \page creator-advanced.html
    \nextpage creator-os-supported-platforms.html

    \title Advanced Use

    \image front-advanced.png

    \QC attempts to meet your development needs, whether you are an
    experienced Qt developer or a newcomer to Qt. When you install \QC
    as a part of \QSDK, the default configuration allows you to start coding,
    building, running and debugging applications with very little effort.

    However, you can easily change or extend the default configuration, by
    choosing a different build system or integrating external tools.

    You can also use special options to start \QC from the command line and use
    it mainly from the keyboard.

    \list

        \li \l{Supported Platforms}

            You can install and run \QC on several operating systems to create
            applications for multiple desktop and \l{glossary-device}{device}
            platforms.

        \li \l{Build Systems}

            \QC is integrated with cross-platform systems for build automation:
            qmake, Qbs, CMake, and Autotools. In addition, you can import
            generic projects that do not use those systems, and specify that \QC
            ignores your build system.

        \li \l{Using Command Line Options}

            You can start \QC and specify some options for running it from the
            command line.

        \li \l{Keyboard Shortcuts}

            \QC provides various keyboard shortcuts to speed up your development
            process. You can change the keyboard shortcuts, as well as import
            and export keyboard shortcut mapping schemes.

        \li \l{Using External Tools}

            You can use external tools directly from \QC. Qt Linguist,
            QML utilities, the default text editor for your system, and the
            \c sort tool are preconfigured for use. You can change their default
            configurations and configure new tools.

    \endlist

    \section1 Related Topics

    \list

        \li \l{Showing Task List Files in Issues}

            You can load report files created by code scanning and analysis
            tools to \l Issues. You can navigate to the corresponding source
            code by clicking the error message or by using keyboard shortcuts.

        \li \l{Inspecting Internal Logs}

            You can inspect internal log messages of \QC. They may be
            helpful when developing \QC or investigating problems.

        \li \l{Managing Data Collection}

            If you agreed to pseudonymous data collection during \QC
            installation, you can turn it on and determine what type
            of data is collected and transmitted to the backend storage.

    \endlist

*/
