type: Group
instructions:
  - type: InstallBinaryArchive
    relativeStoragePath: "{{.Env.MODULE_ARTIFACTS_RELATIVE_STORAGE_PATH}}/artifacts.tar.gz"
    directory: "qt-creator/qt-creator_build/build"
    maxTimeInSeconds: 1800
    maxTimeBetweenOutput: 1800
    userMessageOnFailure: "Failed to unarchive build artifacts, check logs"
  - type: ChangeDirectory
    directory: "{{.AgentWorkingDir}}/qt-creator/qt-creator_build/build"
  - type: ExecuteCommand
    command: "ctest -j 4 --timeout 60 --output-on-failure --label-exclude exclude_from_precheck --exclude-regex tst_perfdata"
    maxTimeInSeconds: 600
    maxTimeBetweenOutput: 600
    userMessageOnFailure: "Failed to run tests, check logs"
enable_if:
  condition: property
  property: features
  not_contains_value: "Qt5"
