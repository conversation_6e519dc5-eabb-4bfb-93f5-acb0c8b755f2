The QtCreator 1.3.1 release is a bug fix release.

Below is a list of relevant changes. You can find a complete list of changes
within the logs of Qt Creator's sources. Simply check it out from the public git
repository e.g.,

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --cherry-pick --pretty=oneline v1.3.0...v1.3.1

General
   * Updated translations

Editing
   * Fixed drawing issues when line wrap is enabled
   * Fixed problem with indentation when auto-indent is turned off

C++ Support
   * Don't show the refactoring warning message all the time
   * Insert semicolon when matching enum declarations
   * Fixed function argument widget text color in dark themes
   * Fixed that inline implemented methods did not show up in the methods filter
   * Fixed in-place renaming when text is selected
   * Fixed autoindent when using tabs instead of spaces
   * Fixed completion when a typedef symbol is used as class name
   * Fixed that template argument was marked as "not a type name" when defined as primitive type
   * Fixed some highlight errors in code using the win32 API
   * Improved completion of function definition parameter lists to append the 
     const and volatile qualifiers if required

Project support
   * Fixed that run configurations were disabled if they had no build step

Debugging
   * CDB: Fixed disassembler for 64 bit addresses
   * Fixed finding the file for build issues when mingw32-make is used
   * Ignore case of file name in breakpoint handling on Windows
   * Fixed problems with gdb timing out and debugging sessions unexpectedly finishing
   * Improved startup time of gdb sessions by not asking for all files known to gdb
   * Mac: Fixed problems with locals and watchers not updating correctly on Snow Leopard

Help
   * Don't switch to Help mode if help side bar is already visible

Platform Specific

Mac
   * Couldn't set "/usr/bin/qmake-4.6" or "/Developer/Tools/Qt/qmake" for Qt

Symbian Target
   * Fixed the time stamp of deployed files

