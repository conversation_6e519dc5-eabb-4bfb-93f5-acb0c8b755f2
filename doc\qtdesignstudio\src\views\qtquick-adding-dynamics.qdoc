// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

/*!
    \page qtquick-adding-dynamics.html
    \previouspage creator-qml-modules-with-plugins.html
    \nextpage qmldesigner-connections.html

    \title Dynamic Behaviors

    Create connections between components to enable them to communicate with
    each other. The connections can be triggered by signals that are emitted
    when the values of other components or the UI state change.

    \list
         \li \l {Working with Connections}

            You can create connections between the UI components and
            the application to enable them to communicate with each other. For
            example, how does the appearance of a button change on a mouse click
            and which action does the application need to perform in response to
            it.

            You can also create connections between UI components by
            binding their properties together. This way, when the value of a
            property changes in a parent component, it can be automatically
            changed in all the child components, for example.
        \li \l {Working with States}

            You can declare various UI states that describe how component
            properties change from a base state. Therefore, states can be
            a useful way of organizing your UI logic. You can associate
            transitions with components to define how their properties will
            animate when they change due to a state change.
    \endlist
*/
