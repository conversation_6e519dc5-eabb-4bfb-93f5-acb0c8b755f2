// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GFDL-1.3-no-invariants-only

// **********************************************************************
// NOTE: the sections are not ordered by their logical order to avoid
// reshuffling the file each time the index order changes (i.e., often).
// Run the fixnavi.pl script to adjust the links to the index order.
// **********************************************************************

/*!
    \page creator-connecting-mobile.html
    \previouspage creator-deployment-embedded-linux.html
    \nextpage creator-developing-android.html

    \title Connecting Devices

    You can connect \l{glossary-device}{devices} to the development PC to run, debug,
    and analyze applications built for them from \QC. When you install Qt for a
    target platform, such as Android or QNX,
    the build and run settings for the development targets might be set up
    automatically in \QC.

    You can connect the device to the development PC using a USB connection.
    Additionally, you can connect Linux-based devices by using a WLAN
    connection.

    The experimental WebAssembly plugin enables you to build your applications
    in WebAssembly format, to deploy them, and to run them in a web browser.

    \list

        \li \l{Connecting Android Devices}

            Qt applications for Android are compiled as \c {shared objects} that
            are loaded by a Java launcher that is part of Qt.
            This is totally transparent to users. As Qt is composed of libraries
            referencing each other, Qt 5 applications are only supported on
            Android version 4.1 (API level 16), or later, and Qt 6 applications
            on Android version 6.0 (API level 23), or later. You must install a
            Qt version targeting Android and the Android SDK and NDK to develop
            for Android devices.

        \li \l{Connecting Bare Metal Devices}

            You can connect bare metal devices to the development PC and use \QC
            to debug applications on them with GDB or a hardware debugger.

        \li \l{Connecting Boot2Qt Devices}

            You can connect \l{Boot2Qt} devices to the development PC to run,
            debug, and analyze applications built for them from \QC.

        \li \l {Adding Docker Devices}

            You can add Docker images as \e devices to run, debug, and analyze
            applications built for Docker containers from \QC.

        \li \l{Connecting iOS Devices}

            You use the tools delivered with Xcode to connect devices to \QC.
            \QC detects the tools and configured devices automatically and uses
            the tools to build, deploy, and run applications.

        \li \l{Connecting MCUs}

            You can connect MCU boards to a development host to deploy, run, and
            debug applications on them from \QC.

        \li \l{Connecting QNX Devices}

            You can connect QNX devices to the development PC to deploy, run and
            debug applications on them from within \QC. This is currently only
            supported for QNX Neutrino devices, and requires the QNX SDK to be
            installed on the development PC.

        \li \l{Connecting Remote Linux Devices}

            If you have a tool chain for building applications for embedded
            Linux devices installed on the development
            PC, you can add it and the device to \QC.

    \endlist

    \section1 Related Topics

    \list

        \li \l{Building Applications for the Web}

            You can use the experimental Qt WebAssembly plugin to build
            applications in WebAssembly format, to deploy them, and to
            run them in a web browser.

        \li \l{https://doc.qt.io/qtcreator/creator-overview-qtasam.html}
            {Qt Creator Plugin for Qt Application Manager}

            You can use the experimental Qt Application Manager plugin
            (commercial only) to deploy, run, and debug applications on the
            local Linux PC, remote generic SSH Linux targets, or
            \l{Boot2Qt}{Boot2Qt devices}.
    \endlist
*/
