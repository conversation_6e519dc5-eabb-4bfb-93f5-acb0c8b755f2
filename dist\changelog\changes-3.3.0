Qt Creator version 3.3 contains bug fixes and new features.

The most important changes are listed in this document. For a complete
list of changes, see the Git log for the Qt Creator sources that
you can check out from the public Git repository. For example:

git clone git://gitorious.org/qt-creator/qt-creator.git
git log --cherry-pick --pretty=oneline origin/3.2..origin/3.3

General
   * Dropped Qt 4 compatibility in Qt Creator's own code base.
     Require Qt 5.3 as minimum, to take advantage of Qt 5 features.
     Handling of Qt 4 based projects is of course still supported.
   * Created new wizard engine to add new script based wizards
     without the need for recompilation
   * Improved high DPI support
   * Added Window > Close Window for external windows
   * Added list of open windows to Window menu
   * Added basic theming support
   * Fixed various thread-safety issues in locator

Editing
   * Moved the Close Document button next to the document
     chooser
   * Implemented dragging of edited documents between splits,
     and from navigation views onto splits
   * Added titles to external editor windows
   * Fixed that File > Close, and the corresponding shortcuts
     and tool buttons, closed all editors for a document even
     if multiple editors were visible

Help
   * Added Home and Bookmark buttons to external and side-by-side
     help viewers
   * Added support for File > Print to external and side-by-side
     help viewers
   * Added optionally shown side bar with contents, index, bookmark
     and search views to external help viewer

Managing and Building Projects
   * Added option to specify environment variables per kit
     (QTCREATORBUG-9898)
   * Added 256-color ANSI support to application output view
     (QTCREATORBUG-12623)
   * Added Select all kits check box to project setup page
   * Added support for using variables, such as %{Qt:Version},
     in Qt version and kit display names (QTCREATORBUG-11118)
   * Changed File > Close Project to a menu that allows selecting
     a specific project to close, if more than one project is open
     (QTCREATORBUG-12857)
   * Fixed that Run menu in project context menu was never shown

QMake Projects
   * Implemented asynchronous loading of projects
   * Added option to use Qt Quick Compiler
   * Fixed that adding multiple build configurations stopped asking
     for the configuration name (QTCREATORBUG-12955)
   * Fixed crash when renaming files in case automatic rewriting
     of the .pro file is not possible (QTCREATORBUG-12930)

CMake Projects
   * Added support for bare metal devices
   * Added option to override make command (QTCREATORBUG-11788)

Qbs Projects
   * Removed experimental status of the plugin
   * Added support for renaming files (QBS-652)
   * Fixed crash with simplified project view (QTCREATORBUG-12897)

Generic Projects
   * Fixed that project files were shown twice after adding files to empty
     generic project (QTCREATORBUG-13106)

Debugging
   * GDB
      * Fixed detection of target architecture
   * CDB
      * Made it possible to abort the debugging process
      * Fixed shadowed local variable handling (QTCREATORBUG-13132)

QML Profiler
   * Added drag & drop for reordering categories in timeline (QTCREATORBUG-12337)
   * Added optional showing and hiding of categories
   * Added option to add notes to events in timeline for export
   * Fixed display of binding loops (QTCREATORBUG-12950)
   * Fixed selection of previous and next item in timeline

Analyzer

C++ Support
   * Added Convert connect() to Qt 5 Style refactoring action
   * Added smart splitting of string literals when pressing enter within strings
   * Fixed code completion for pointer in template specialization
     and initialization (QTCREATORBUG-12638)
   * Fixed code completion for typedefs of pointer arrays
     (QTCREATORBUG-12703)
   * Fixed that hovering over a symbol sometimes jumped to its definition
     (QTCREATORBUG-12986)
   * Fixed that include paths were not adjusted when files are renamed


QML Support
   * Added support for forms

Qt Quick Designer
   * Added support for a workflow where .qml code is split into
     "UI-only" and "logic" parts
   * Added support for TabView QtQuick Control
   * Extended anchoring options

Diff Viewer
   * Fixed reloading patch files (QTCREATORBUG-13241)

Version Control Systems
   * Fixed commit editor line wrapping for lines that end with a dash
     (QTCREATORBUG-8528)
   * Git
      * Removed support for Gitorious
   * Subversion
      * Dropped support for Subversion versions earlier than 1.7.0
      * Fixed deleting of files that were added but not committed
        (QTCREATORBUG-13271)
      * Fixed detection of .svn directory when opening a file in the
        root directory of the repository (QTCREATORBUG-7763)
      * Added support for describing the first commit
      * Added support for topic display (shows current revision number
        in the window title and next to the project node)
      * Fixed that commit output was not displayed (QTCREATORBUG-12929)
      * Fixed false positive matches for revision number (QTCREATORBUG-11845)
      * Added support for trusting server certificate on checkout (QTCREATORBUG-3753)
   * Mercurial
      * Fixed that describe commit showed its summary but not the full message
      * Fixed highlighting of commit message
      * Fixed wrong comment notation in commit editor - comment line prefix was
        changed to "HG:" instead of "#"

FakeVim
   * Fixed issues with Save As
     (QTCREATORBUG-12810)

Beautifier

Platform Specific

Windows

OS X

Android
   * Removed Necessitas support
   * Added Gradle build system support
   * Changed installation of package to use androiddeployqt
     when using Qt 5.4 or later
   * Improved error dialog for Android manifest
   * Split permissions and features check box into separate check boxes
     in manifest editor
   * Fixed that passwords used for signing were shown in build log
     (QTCREATORBUG-12919)

Remote Linux
   * Made checks on tool chain and target architecture/OS for deployment
     options less restrictive

QNX

BareMetal

Tools:
   * Removed unused zeroconf wrapper

Credits for these changes go to:

Adam Strzelecki
Alessandro Portale
André Pönitz
Alex Blasche
André Hartmann
BogDan Vatra
Caroline Chao
Charles Huet
Christian Kandeler
Christian Stenger
Daniel Teske
David Schulz
Dieter Piringer
Eike Ziller
El Mehdi Fekari
Erik Verbruggen
Eskil Abrahamsen Blomfeldt
Fawzi Mohamed
Filipp Ozinov
Finn Brudal
Francois Ferrand
Friedemann Kleint
Gabriel Hege
Jake Petroules
Jaroslaw Kobus
James McDonnell
Jörg Bornemann
Kai Koehne
Leena Miettinen
Lukas Holecek
Marco Bubke
Mika Attila
Mitch Curtis
Nikita Baryshnikov
Nikolai Kosjar
Oliver Wolff
Orgad Shaneh
Oswald Buddenhagen
Przemyslaw Gorszkowski
Rafael Roquetto
Ray Donnelly
Robert Löhning
Samuel Gaist
Sebastian Sauer
Sergey Belyashov
Steve Mokris
Takao Fujiwara
Takumi Asaki
Thiago Macieira
Thomas Epting
Thomas Hartmann
Thomas Otto
Thorben Kroeger
Tim Jenssen
Tim Sander
Tobias Hunger
Tommi Asp
Topi Reinio
Ulf Hermann
Viktor Ostashevskyi
Volker Vogelhuber
